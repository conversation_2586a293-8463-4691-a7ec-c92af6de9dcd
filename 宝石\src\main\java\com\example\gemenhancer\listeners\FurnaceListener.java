package com.example.gemenhancer.listeners;

import org.bukkit.block.Furnace;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.FurnaceBurnEvent;
import org.bukkit.event.inventory.FurnaceSmeltEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryType;
import org.bukkit.inventory.FurnaceInventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;

import com.example.gemenhancer.GemEnhancer;
import com.example.gemenhancer.utils.GemManager;

public class FurnaceListener implements Listener {

    private final GemEnhancer plugin;
    private final GemManager gemManager;

    public FurnaceListener(GemEnhancer plugin) {
        this.plugin = plugin;
        this.gemManager = plugin.getGemManager();

        // 启动定时任务，每秒检查一次熔炉
        new BukkitRunnable() {
            @Override
            public void run() {
                processFurnaces();
            }
        }.runTaskTimer(plugin, 20L, 20L); // 延迟1秒后开始，每1秒执行一次
    }

    /**
     * 处理所有正在燃烧的熔炉
     */
    private void processFurnaces() {
        // 这个方法会在定时任务中被调用，用于处理所有正在燃烧的熔炉
        // 由于我们无法直接获取所有熔炉，所以我们依赖于事件系统
        // 实际的强化逻辑在onFurnaceSmelt方法中
    }

    /**
     * 监听物品放入熔炉事件
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        // 检查是否是熔炉界面
        if (event.getView().getTopInventory().getType() != InventoryType.FURNACE) {
            return;
        }

        // 获取点击的物品和光标上的物品
        ItemStack cursorItem = event.getCursor();

        // 获取熔炉库存
        FurnaceInventory furnaceInventory = (FurnaceInventory) event.getView().getTopInventory();

        // 检查是否是将宝石放入燃料槽
        if (event.getRawSlot() == 1 && cursorItem != null && gemManager.isGem(cursorItem)) {
            // 如果是玩家，发送提示消息
            if (event.getWhoClicked() instanceof Player) {
                Player player = (Player) event.getWhoClicked();
                plugin.sendMessage(player, "&a你放入了一个强化宝石。将装备放在上方可以进行强化！");
            }
        }

        // 检查是否是将装备放入材料槽
        if (event.getRawSlot() == 0 && cursorItem != null) {
            String equipmentType = gemManager.getEquipmentType(cursorItem);
            if (equipmentType != null) {
                // 检查下方是否有宝石
                ItemStack fuel = furnaceInventory.getFuel();

                // 如果下方已经有宝石，并且是玩家，发送提示消息
                if (fuel != null && gemManager.isGem(fuel) && event.getWhoClicked() instanceof Player) {
                    Player player = (Player) event.getWhoClicked();
                    plugin.sendMessage(player, "&a你放入了装备。熔炉将开始强化过程！");
                }
            }
        }
    }

    /**
     * 监听熔炉燃烧事件，检查是否是宝石强化
     */
    @EventHandler
    public void onFurnaceBurn(FurnaceBurnEvent event) {
        // 获取燃料物品
        ItemStack fuel = event.getFuel();

        // 检查是否为宝石
        if (!gemManager.isGem(fuel)) {
            return; // 不是宝石，让Minecraft正常处理
        }

        // 获取熔炉库存
        Furnace furnace = (Furnace) event.getBlock().getState();
        FurnaceInventory inventory = furnace.getInventory();

        // 获取材料（上方）
        ItemStack source = inventory.getSmelting();

        // 获取宝石燃烧时间
        int burnTime = gemManager.getGemBurnTime();

        // 检查上方是否有物品
        if (source == null) {
            // 没有材料，允许燃烧但设置很短的时间
            event.setBurnTime(20); // 1秒
            return;
        }

        // 检查上方是否为可强化装备
        String equipmentType = gemManager.getEquipmentType(source);
        if (equipmentType == null) {
            // 不是可强化装备，允许燃烧但设置很短的时间
            event.setBurnTime(20); // 1秒
            return;
        }

        // 是宝石和可强化装备，设置燃烧时间
        event.setBurnTime(burnTime);

        // 记录当前的材料，以便在燃烧结束后使用
        final ItemStack finalSource = source.clone();

        // 安排一个延迟任务，在燃烧结束后处理强化逻辑
        new BukkitRunnable() {
            @Override
            public void run() {
                // 获取熔炉状态
                Furnace updatedFurnace = (Furnace) event.getBlock().getState();

                // 检查熔炉是否仍然存在
                if (updatedFurnace == null)
                    return;

                // 获取熔炉库存
                FurnaceInventory updatedInventory = updatedFurnace.getInventory();

                // 获取当前的材料
                ItemStack currentSource = updatedInventory.getSmelting();

                // 检查是否仍然有材料
                if (currentSource == null)
                    return;

                // 检查材料是否与之前相同
                if (!currentSource.equals(finalSource))
                    return;

                // 执行强化逻辑
                // 注意：我们不在这里直接调用processEnhancement，而是设置一个标记
                // 实际的强化逻辑将在FurnaceSmeltEvent中处理

                // 设置熔炉的烧制时间为199，这样就会立即触发FurnaceSmeltEvent
                // 在1.8.8中没有getCookTimeTotal()方法，直接使用固定值200-1=199
                updatedFurnace.setCookTime((short) 199);
                updatedFurnace.update();
            }
        }.runTaskLater(plugin, burnTime); // 在燃烧结束后执行
    }

    /**
     * 获取距离熔炉最近的玩家
     *
     * @param furnace     熔炉
     * @param maxDistance 最大距离
     * @return 最近的玩家，如果没有玩家在范围内则返回null
     */
    private Player getNearestPlayer(Furnace furnace, double maxDistance) {
        Player nearestPlayer = null;
        double minDistance = Double.MAX_VALUE;

        for (Player player : furnace.getWorld().getPlayers()) {
            double distance = player.getLocation().distance(furnace.getLocation());
            if (distance < minDistance && distance <= maxDistance) {
                minDistance = distance;
                nearestPlayer = player;
            }
        }

        return nearestPlayer;
    }

    /**
     * 处理装备强化逻辑
     *
     * @param furnace 熔炉
     * @param fuel    燃料（宝石）
     * @param source  材料（装备）
     */
    private void processEnhancement(Furnace furnace, ItemStack fuel, ItemStack source) {
        // 获取熔炉库存
        FurnaceInventory inventory = furnace.getInventory();

        // 检查装备类型
        String equipmentType = gemManager.getEquipmentType(source);
        if (equipmentType == null)
            return;

        // 检查是否满耐久
        if (!gemManager.isFullDurability(source)) {
            // 获取附近的玩家并发送消息
            Player nearestPlayer = getNearestPlayer(furnace, 10);
            if (nearestPlayer != null) {
                plugin.sendMessage(nearestPlayer,
                        plugin.getConfig().getString("messages.not-full-durability", "&c装备必须是满耐久才能强化！"));
            }
            return;
        }

        // 尝试强化装备
        ItemStack enhancedEquipment = gemManager.enhanceEquipment(source);

        // 判断是否强化成功
        boolean success = !source.equals(enhancedEquipment);

        // 获取附近的玩家
        Player nearestPlayer = getNearestPlayer(furnace, 10);

        // 消耗宝石
        if (fuel.getAmount() > 1) {
            fuel.setAmount(fuel.getAmount() - 1);
        } else {
            inventory.setFuel(null);
        }

        // 更新装备
        if (success) {
            inventory.setSmelting(enhancedEquipment);

            // 发送成功消息
            if (nearestPlayer != null) {
                plugin.sendMessage(nearestPlayer, plugin.getConfig().getString("messages.success", "&a强化成功！"));
            }
        } else {
            // 发送失败消息
            if (nearestPlayer != null) {
                plugin.sendMessage(nearestPlayer, plugin.getConfig().getString("messages.fail", "&c强化失败！"));
            }
        }

        // 更新熔炉状态
        furnace.update();
    }

    /**
     * 监听熔炉冶炼事件，处理宝石强化结果
     */
    @EventHandler
    public void onFurnaceSmelt(FurnaceSmeltEvent event) {
        // 获取熔炉库存
        Furnace furnace = (Furnace) event.getBlock().getState();
        FurnaceInventory inventory = furnace.getInventory();

        // 获取燃料（下方）和材料（上方）
        ItemStack fuel = inventory.getFuel();
        ItemStack source = inventory.getSmelting();

        // 检查是否符合宝石强化条件
        if (fuel == null || source == null)
            return;

        // 检查下方是否为宝石
        if (!gemManager.isGem(fuel))
            return;

        // 检查上方是否为可强化装备
        String equipmentType = gemManager.getEquipmentType(source);
        if (equipmentType == null) {
            return;
        }

        // 取消默认的冶炼结果（防止装备被烧制成其他物品）
        event.setCancelled(true);

        // 执行强化逻辑
        processEnhancement(furnace, fuel, source);
    }
}
