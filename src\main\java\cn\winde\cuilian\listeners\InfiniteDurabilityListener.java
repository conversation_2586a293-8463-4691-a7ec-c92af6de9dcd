package cn.winde.cuilian.listeners;

import cn.winde.cuilian.suit.SuitManager;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerItemDamageEvent;
import org.bukkit.inventory.ItemStack;

/**
 * 无限耐久监听器
 * 处理套装装备的无限耐久功能
 */
public class InfiniteDurabilityListener implements Listener {

    @EventHandler(priority = EventPriority.HIGH)
    public void onItemDamage(PlayerItemDamageEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        // 检查玩家是否穿戴套装
        String suitName = SuitManager.getPlayerSuit(player.getName());
        if (suitName == null) {
            return;
        }
        
        // 获取套装属性
        SuitManager.SuitAttribute attribute = SuitManager.getPlayerSuitAttribute(player.getName());
        if (attribute == null || !attribute.infiniteDurability) {
            return;
        }
        
        // 检查损坏的物品是否是套装装备
        if (isSuitItem(item, attribute)) {
            // 取消耐久度损坏
            event.setCancelled(true);
            
            // 可选：显示无限耐久提示（避免频繁显示）
            if (Math.random() < 0.01) { // 1%概率显示提示
                player.sendMessage("§a§l[套装] §7无限耐久保护生效！");
            }
        }
    }
    
    /**
     * 检查物品是否是套装装备
     */
    private boolean isSuitItem(ItemStack item, SuitManager.SuitAttribute attribute) {
        if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
            return false;
        }
        
        String itemName = item.getItemMeta().getDisplayName();
        
        // 移除颜色代码进行比较
        String cleanItemName = itemName.replaceAll("§[0-9a-fk-or]", "");
        
        // 检查是否匹配套装装备名称
        return matchItemName(cleanItemName, attribute.helmet) ||
               matchItemName(cleanItemName, attribute.chestplate) ||
               matchItemName(cleanItemName, attribute.leggings) ||
               matchItemName(cleanItemName, attribute.boots) ||
               matchItemName(cleanItemName, attribute.sword) ||
               matchItemName(cleanItemName, attribute.bow);
    }
    
    /**
     * 匹配装备名称
     */
    private boolean matchItemName(String itemName, String suitItemName) {
        if (itemName == null || suitItemName == null) {
            return false;
        }
        
        // 移除颜色代码
        String cleanSuitName = suitItemName.replaceAll("§[0-9a-fk-or]", "");
        
        return itemName.contains(cleanSuitName) || cleanSuitName.contains(itemName);
    }
}
