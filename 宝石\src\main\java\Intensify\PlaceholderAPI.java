package Intensify;

import java.util.ArrayList;
import java.util.List;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;

/**
 * PlaceholderAPI扩展类，用于提供战力系统的变量
 */
public class PlaceholderAPI {
  private final Intensify plugin;

  public PlaceholderAPI(Intensify plugin) {
    this.plugin = plugin;
  }

  /**
   * 注册PlaceholderAPI扩展
   */
  public void register() {
    // 检查PlaceholderAPI是否已安装
    if (Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
      // 注册扩展
      new PowerExpansion(plugin).register();
    }
  }

  /**
   * PlaceholderAPI扩展类
   */
  public class PowerExpansion extends me.clip.placeholderapi.expansion.PlaceholderExpansion {
    private final Intensify plugin;

    public PowerExpansion(Intensify plugin) {
      this.plugin = plugin;
    }

    @Override
    public String getIdentifier() {
      return "intensify";
    }

    @Override
    public String getAuthor() {
      return plugin.getDescription().getAuthors().toString();
    }

    @Override
    public String getVersion() {
      return plugin.getDescription().getVersion();
    }

    @Override
    public boolean persist() {
      return true;
    }

    @Override
    public String onRequest(OfflinePlayer player, String identifier) {
      // 处理玩家自己的战力值
      if (identifier.equals("power")) {
        if (player == null || !player.isOnline()) {
          return "0";
        }

        // 获取玩家战力值
        int power = plugin.calculatePlayerPower((Player) player);
        return String.valueOf(power);
      }

      // 处理战力排行榜
      if (identifier.startsWith("top_")) {
        try {
          // 解析排名
          String[] parts = identifier.split("_");
          if (parts.length < 2) {
            return "未知";
          }

          // 获取排名
          int rank = Integer.parseInt(parts[1]);
          if (rank < 1 || rank > 10) {
            return "排名超出范围";
          }

          // 确保排行榜是最新的
          if (System.currentTimeMillis() - plugin.getLastRankingUpdate() > plugin.getRankingUpdateInterval()) {
            plugin.updatePowerRanking();
          }

          // 获取排行榜数据
          List<PowerData> ranking = plugin.getPowerRanking();
          if (ranking.size() < rank) {
            return "暂无数据";
          }

          // 获取指定排名的玩家数据
          PowerData powerData = ranking.get(rank - 1);
          String playerName = powerData.getPlayerName();
          int power = powerData.getPower();

          // 根据请求类型返回不同的数据
          if (parts.length > 2) {
            String type = parts[2];
            if (type.equals("name")) {
              return playerName;
            } else if (type.equals("power")) {
              return String.valueOf(power);
            }
          }

          // 默认返回格式：玩家名称 (战力值)
          return playerName + " (" + power + ")";
        } catch (NumberFormatException e) {
          return "格式错误";
        }
      }

      return null;
    }
  }
}
