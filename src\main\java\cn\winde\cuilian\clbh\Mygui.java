/*
 * Decompiled with CFR 0.152.
 */
package cn.winde.cuilian.clbh;

import cn.winde.cuilian.Cuilian;
import cn.winde.cuilian.suit.SuitManager;
import java.awt.Color;
import java.awt.EventQueue;

import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.IOException;
import java.util.Timer;
import java.util.TimerTask;

import javax.swing.DefaultComboBoxModel;
import javax.swing.DefaultListModel;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JList;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTabbedPane;
import javax.swing.JTextField;
import javax.swing.border.EmptyBorder;
import javax.swing.BorderFactory;
import javax.swing.ListSelectionModel;

import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

// 添加玩家头颅显示所需的import
import javax.swing.ImageIcon;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.net.URL;
import java.util.concurrent.CompletableFuture;
import javax.imageio.ImageIO;

public class Mygui
        extends JFrame {
    private JPanel contentPane;
    private JCheckBox checkBox;

    // 玩家管理相关组件
    private PlayerGridPanel onlinePlayersGridPanel;
    private JTextField offlinePlayerField;
    private JComboBox<String> suitComboBox;
    private JComboBox<String> stoneComboBox;
    private JTextField stoneAmountField;

    // 套装创建相关组件
    private JTextField suitNameField;
    private JComboBox<String> qualityComboBox;
    private JTextField headNameField;
    private JTextField chestNameField;
    private JTextField legNameField;
    private JTextField footNameField;
    private JTextField swordNameField;
    private JTextField bowNameField;
    private JTextField attackField;
    private JTextField defenseField;
    private JTextField healthField;
    private JTextField speedField;
    private JTextField jumpField;
    private JTextField vampireField;
    // 宝石等级配置
    private JTextField headGemField;
    private JTextField chestGemField;
    private JTextField legGemField;
    private JTextField footGemField;
    private JTextField swordGemField;
    private JTextField bowGemField;
    // 描述配置
    private javax.swing.JTextArea descriptionArea;
    // 特效配置
    private JList<String> effectsList;
    private DefaultListModel<String> effectsModel;
    private JComboBox<String> effectTypeComboBox;
    private JComboBox<String> color1ComboBox;
    private JComboBox<String> color2ComboBox;
    private JComboBox<String> color3ComboBox;

    // 特效配置管理相关组件
    private JTextField newStarLevelField;
    private JComboBox<String> effectStarLevelComboBox;
    private JList<String> currentEffectsList;
    private DefaultListModel<String> currentEffectsModel;
    private JComboBox<String> availableEffectTypeComboBox;
    private JComboBox<String> effectColor1ComboBox;
    private JComboBox<String> effectColor2ComboBox;
    private JComboBox<String> effectColor3ComboBox;
    private JCheckBox effectEnabledCheckBox;
    private JLabel statusLabel; // 状态显示标签

    // 符咒管理相关组件
    private JComboBox<String> charmLevelComboBox;
    private JComboBox<String> charmTypeComboBox;
    private JTextField charmQuantityField;
    private JTextField charmTargetPlayerField;
    private PlayerGridPanel charmPlayersGridPanel;
    private DefaultListModel<String> charmOnlinePlayersModel;

    // 游戏内GUI管理相关组件
    private PlayerGridPanel guiPlayersGridPanel;
    private JTextField guiPlayerNameField;

    // 批量操作相关组件
    private DefaultListModel<String> batchPlayersModel;

    // TPS自动特效开关相关组件
    private JCheckBox tpsAutoEffectCheckBox;
    private JTextField tpsThresholdField;
    private JTextField tpsRecoveryDelayField;
    private JTextField previewDurationField;
    private JTextField previewCooldownField;
    private JTextField tpsDelayField;

    // 套装无限耐久开关
    private JCheckBox infiniteDurabilityCheckBox;

    // 套装特效消息强制禁用开关
    private JCheckBox forceDisableMessagesCheckBox;

    // 静态引用，用于玩家加入/退出事件的自动更新
    private static Mygui instance;

    // 套装删除相关组件
    private JComboBox<String> deleteSuitComboBox;

    // 星级配置相关组件
    private JTextField starLevelField;
    private JComboBox<String> existingStarLevelComboBox; // 现有星级选择
    // 属性配置
    private JTextField starAttackField;
    private JTextField starDefenseField;
    private JTextField starVampireField;
    private JTextField starJumpField;
    private JTextField starFallDamageField;
    private JTextField starCounterAttackField;
    // 强化石几率配置 - 针对当前星级的所有强化石类型
    private JTextField putongProbabilityField; // 普通强化石几率
    private JTextField zhongdengProbabilityField; // 中等强化石几率
    private JTextField gaodengProbabilityField; // 高等强化石几率
    private JTextField wanmeiProbabilityField; // 完美强化石几率

    public static void main(String[] args) {
        Mygui.setui();
    }

    public static void setui() {
        EventQueue.invokeLater(new Runnable() {

            @Override
            public void run() {
                try {
                    Mygui frame = new Mygui();
                    frame.checkBox.setSelected(Cuilian.config.getBoolean("effects"));
                    frame.setVisible(true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    public Mygui() {
        // 设置静态实例引用，用于玩家加入/退出事件的自动更新
        instance = this;

        // 设置现代化窗口属性
        this.setTitle("淬炼插件管理界面 v2.0");
        this.setDefaultCloseOperation(JFrame.HIDE_ON_CLOSE);
        this.setSize(1400, 900); // 增大界面尺寸以适应更大字体和组件
        this.setLocationRelativeTo(null); // 居中显示
        this.setResizable(true); // 允许调整大小

        // 创建主面板，使用BorderLayout实现自动调整
        this.contentPane = new JPanel(new java.awt.BorderLayout());
        this.contentPane.setBorder(new EmptyBorder(10, 10, 10, 10));
        this.contentPane.setBackground(java.awt.Color.WHITE);
        this.setContentPane(this.contentPane);

        // 创建标题面板
        JPanel titlePanel = createTitlePanel();
        this.contentPane.add(titlePanel, java.awt.BorderLayout.NORTH);

        // 创建主标签页面板
        JTabbedPane tabbedPane = new JTabbedPane(JTabbedPane.TOP);
        tabbedPane.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        tabbedPane.setBackground(java.awt.Color.WHITE);
        this.contentPane.add(tabbedPane, java.awt.BorderLayout.CENTER);

        // 添加玩家管理标签页
        JPanel playerManagementPanel = createPlayerManagementPanel();
        tabbedPane.addTab("玩家管理", null, playerManagementPanel, null);

        // 添加套装创建标签页
        JPanel suitCreatorPanel = createSuitCreatorPanel();
        tabbedPane.addTab("套装创建", null, suitCreatorPanel, null);

        // 添加星级配置标签页
        JPanel starConfigPanel = createStarConfigPanel();
        tabbedPane.addTab("星级配置", null, starConfigPanel, null);

        // 特效配置标签页
        JPanel effectConfigPanel = createEffectConfigPanel();
        tabbedPane.addTab("特效配置", null, effectConfigPanel, null);

        // 符咒管理标签页
        JPanel charmPanel = createCharmManagementPanel();
        tabbedPane.addTab("符咒管理", null, charmPanel, null);

        // 游戏内GUI管理标签页
        JPanel inGameGUIPanel = createInGameGUIPanel();
        tabbedPane.addTab("游戏内GUI", null, inGameGUIPanel, null);

        // 服务器监控标签页
        JPanel serverMonitorPanel = createServerMonitorPanel();
        tabbedPane.addTab("服务器监控", null, serverMonitorPanel, null);

        // 批量操作标签页
        JPanel batchOperationPanel = createBatchOperationPanel();
        tabbedPane.addTab("批量操作", null, batchOperationPanel, null);

        // 权限与安全标签页
        JPanel securityPanel = createSecurityPanel();
        tabbedPane.addTab("权限与安全", null, securityPanel, null);

        // 创建底部面板
        JPanel bottomPanel = createBottomPanel();
        this.contentPane.add(bottomPanel, java.awt.BorderLayout.SOUTH);

        // 初始化数据
        initializeData();

        // 添加窗口关闭事件监听器
        this.addWindowListener(new java.awt.event.WindowAdapter() {
            @Override
            public void windowClosing(java.awt.event.WindowEvent e) {
                stopSuitDetectionTask(); // 停止套装检测任务
                stopAutoSaveTask(); // 停止自动保存任务
                stopAutoUpdateTask(); // 停止自动更新任务
                stopProbabilityFileWatcher(); // 停止文件监控
                saveMonitoringData(); // 保存监控数据
            }
        });

        // 启动自动保存任务
        startAutoSaveTask();

        // 启动自动更新监控数据任务
        startAutoUpdateTask();

        // 启动定期清理离线玩家的任务
        startPlayerCleanupTask();

        // 启动Probability.yml文件监控
        startProbabilityFileWatcher();
    }

    /**
     * 启动定期清理离线玩家的任务
     */
    private void startPlayerCleanupTask() {
        // 每5秒检查一次在线玩家列表，清理已离线的玩家
        Timer cleanupTimer = new Timer(true);
        cleanupTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                try {
                    cleanupOfflinePlayers();
                } catch (Exception e) {
                }
            }
        }, 5000, 5000); // 5秒后开始，每5秒执行一次
    }

    /**
     * 清理已离线的玩家
     */
    private void cleanupOfflinePlayers() {
        if (instance == null)
            return;

        // 获取当前真实在线的玩家列表
        java.util.Set<String> realOnlinePlayers = new java.util.HashSet<>();
        try {
            for (Player player : Bukkit.getOnlinePlayers()) {
                realOnlinePlayers.add(player.getName());
            }
        } catch (Exception e) {
            return;
        }

        // 在EDT线程中更新UI
        javax.swing.SwingUtilities.invokeLater(() -> {
            try {
                // 检查并清理玩家管理面板中的离线玩家
                if (instance.onlinePlayersGridPanel != null) {
                    java.util.List<String> playersToRemove = new java.util.ArrayList<>();

                    // 获取当前UI中显示的玩家列表
                    for (PlayerGridPanel.PlayerButton button : instance.onlinePlayersGridPanel.playerButtons) {
                        String playerName = button.playerName;
                        if (!realOnlinePlayers.contains(playerName)) {
                            playersToRemove.add(playerName);
                        }
                    }

                    // 移除离线玩家
                    for (String playerName : playersToRemove) {
                        instance.onlinePlayersGridPanel.removePlayer(playerName);
                    }
                }

                // 同样清理其他面板
                cleanupOtherPanels(realOnlinePlayers);

            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    /**
     * 清理其他面板中的离线玩家
     */
    private void cleanupOtherPanels(java.util.Set<String> realOnlinePlayers) {
        try {
            // 清理符咒管理面板
            if (instance.charmPlayersGridPanel != null) {
                java.util.List<String> playersToRemove = new java.util.ArrayList<>();
                for (PlayerGridPanel.PlayerButton button : instance.charmPlayersGridPanel.playerButtons) {
                    if (!realOnlinePlayers.contains(button.playerName)) {
                        playersToRemove.add(button.playerName);
                    }
                }
                for (String playerName : playersToRemove) {
                    instance.charmPlayersGridPanel.removePlayer(playerName);
                }
            }

            // 清理游戏内GUI面板
            if (instance.guiPlayersGridPanel != null) {
                java.util.List<String> playersToRemove = new java.util.ArrayList<>();
                for (PlayerGridPanel.PlayerButton button : instance.guiPlayersGridPanel.playerButtons) {
                    if (!realOnlinePlayers.contains(button.playerName)) {
                        playersToRemove.add(button.playerName);
                    }
                }
                for (String playerName : playersToRemove) {
                    instance.guiPlayersGridPanel.removePlayer(playerName);
                }
            }

            // 清理批量操作面板
            if (instance.batchPlayersModel != null) {
                java.util.List<String> playersToRemove = new java.util.ArrayList<>();
                for (int i = 0; i < instance.batchPlayersModel.getSize(); i++) {
                    String playerName = instance.batchPlayersModel.getElementAt(i);
                    if (!realOnlinePlayers.contains(playerName)) {
                        playersToRemove.add(playerName);
                    }
                }
                for (String playerName : playersToRemove) {
                    instance.batchPlayersModel.removeElement(playerName);
                }
            }

        } catch (Exception e) {
            System.err.println("清理其他面板失败: " + e.getMessage());
        }
    }

    /**
     * 静态方法：供外部事件监听器调用，用于自动更新在线玩家列表
     */
    public static void updateOnlinePlayersList() {
        if (instance != null) {
            // 在EDT线程中安全地刷新玩家列表
            javax.swing.SwingUtilities.invokeLater(() -> {
                instance.refreshOnlinePlayers();
                // 同时更新游戏内GUI面板的玩家列表
                instance.refreshGUIPlayersList();
            });
        }
    }

    /**
     * 刷新在线玩家列表
     */
    private void refreshOnlinePlayers() {
        java.util.List<String> players = new java.util.ArrayList<>();
        try {
            for (Player player : Bukkit.getOnlinePlayers()) {
                players.add(player.getName());
            }

            // 调试信息：打印实际获取到的玩家数量

            for (String playerName : players) {
            }

        } catch (Exception e) {
            // 如果Bukkit不可用，添加一些测试数据来验证布局
            for (int i = 1; i <= 25; i++) {
                players.add("测试玩家" + i);
            }
        }

        // 更新网格面板
        if (this.onlinePlayersGridPanel != null) {
            this.onlinePlayersGridPanel.updatePlayers(players);
        }
    }

    /**
     * 发送套装给玩家
     */
    private void sendSuitToPlayer() {
        String targetPlayer = getSelectedPlayer();
        if (targetPlayer == null || targetPlayer.isEmpty()) {
            JOptionPane.showMessageDialog(null, "请选择或输入玩家名称！");
            return;
        }

        String selectedSuit = (String) this.suitComboBox.getSelectedItem();
        if (selectedSuit == null || selectedSuit.equals("选择套装")) {
            JOptionPane.showMessageDialog(null, "请选择要发送的套装！");
            return;
        }

        try {
            Player player = Bukkit.getPlayer(targetPlayer);
            if (player != null && player.isOnline()) {
                // 在线玩家 - 直接使用选中的套装名称（已经是原始名称）
                boolean success = SuitManager.giveSuitToPlayer(player, selectedSuit);
                if (success) {
                    // 显示时移除颜色代码
                    String displayName = removeColorCodes(selectedSuit);
                    JOptionPane.showMessageDialog(null, "成功给予玩家 " + targetPlayer + " 套装: " + displayName);
                } else {
                    JOptionPane.showMessageDialog(null, "发送套装失败！套装可能不存在。");
                }
            } else {
                // 离线玩家
                @SuppressWarnings("deprecation")
                OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(targetPlayer);
                if (offlinePlayer.hasPlayedBefore()) {
                    JOptionPane.showMessageDialog(null, "玩家 " + targetPlayer + " 当前离线，请等待玩家上线后手动发送。");
                } else {
                    JOptionPane.showMessageDialog(null, "玩家 " + targetPlayer + " 不存在！");
                }
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "发送套装时出现错误: " + e.getMessage());
        }
    }

    /**
     * 发送淬炼石给玩家
     */
    private void sendStoneToPlayer() {
        String targetPlayer = getSelectedPlayer();
        if (targetPlayer == null || targetPlayer.isEmpty()) {
            JOptionPane.showMessageDialog(null, "请选择或输入玩家名称！");
            return;
        }

        String stoneType = (String) this.stoneComboBox.getSelectedItem();
        if (stoneType == null) {
            JOptionPane.showMessageDialog(null, "请选择要发送的淬炼石类型！");
            return;
        }

        int amount;
        try {
            amount = Integer.parseInt(this.stoneAmountField.getText());
            if (amount <= 0) {
                JOptionPane.showMessageDialog(null, "数量必须大于0！");
                return;
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请输入有效的数量！");
            return;
        }

        try {
            Player player = Bukkit.getPlayer(targetPlayer);
            if (player != null && player.isOnline()) {
                // 在线玩家
                ItemStack stone = createStoneByType(stoneType);
                if (stone != null) {
                    stone.setAmount(amount);
                    player.getInventory().addItem(stone);
                    JOptionPane.showMessageDialog(null,
                            "成功给予玩家 " + targetPlayer + " " + amount + "个 " + stoneType + "淬炼石");
                } else {
                    JOptionPane.showMessageDialog(null, "创建淬炼石失败！");
                }
            } else {
                // 离线玩家
                @SuppressWarnings("deprecation")
                OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(targetPlayer);
                if (offlinePlayer.hasPlayedBefore()) {
                    JOptionPane.showMessageDialog(null, "玩家 " + targetPlayer + " 当前离线，请等待玩家上线后手动发送。");
                } else {
                    JOptionPane.showMessageDialog(null, "玩家 " + targetPlayer + " 不存在！");
                }
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "发送淬炼石时出现错误: " + e.getMessage());
        }
    }

    /**
     * 获取选中的玩家名称
     */
    private String getSelectedPlayer() {
        // 优先使用在线玩家网格面板中的选择
        if (this.onlinePlayersGridPanel != null) {
            String selectedOnline = this.onlinePlayersGridPanel.getSelectedPlayer();
            if (selectedOnline != null && !selectedOnline.isEmpty()) {
                return selectedOnline;
            }
        }

        // 如果没有选择在线玩家，使用离线玩家输入框
        String offlineInput = this.offlinePlayerField.getText().trim();
        if (!offlineInput.isEmpty()) {
            return offlineInput;
        }

        return null;
    }

    /**
     * 根据类型创建淬炼石
     */
    private ItemStack createStoneByType(String type) {
        switch (type) {
            case "普通":
                return Cuilian.clbs_putong();
            case "中等":
                return Cuilian.clbs_zhongdeng();
            case "高等":
                return Cuilian.clbs_gaodeng();
            case "上等":
                return Cuilian.clbs_wanmei();
            case "符咒":
                return Cuilian.clbs_yuangu();
            case "吞噬":
                return Cuilian.huaming();
            default:
                return null;
        }
    }

    /**
     * 添加特效到列表
     */
    private void addEffectToList() {
        String effectTypeDisplay = (String) this.effectTypeComboBox.getSelectedItem();
        String color1 = (String) this.color1ComboBox.getSelectedItem();
        String color2 = (String) this.color2ComboBox.getSelectedItem();
        String color3 = (String) this.color3ComboBox.getSelectedItem();

        if (effectTypeDisplay != null && color1 != null && color2 != null && color3 != null) {
            // 提取英文特效名称（去掉中文说明）
            String effectType = effectTypeDisplay.split(" - ")[0];
            String effectString = effectType + " (" + color1 + "," + color2 + "," + color3 + ")";
            this.effectsModel.addElement(effectString);
        }
    }

    /**
     * 从列表中删除特效
     */
    private void removeEffectFromList() {
        int selectedIndex = this.effectsList.getSelectedIndex();
        if (selectedIndex != -1) {
            this.effectsModel.removeElementAt(selectedIndex);
        } else {
            JOptionPane.showMessageDialog(null, "请先选择要删除的特效！");
        }
    }

    /**
     * 保存套装到配置文件
     */
    private void saveSuitToConfig() {
        String suitName = this.suitNameField.getText().trim();
        if (suitName.isEmpty()) {
            JOptionPane.showMessageDialog(null, "请输入套装名称！");
            return;
        }

        // 检查套装是否已存在
        if (Cuilian.Suit.getConfigurationSection("suit." + suitName) != null) {
            int result = JOptionPane.showConfirmDialog(null,
                    "套装 '" + suitName + "' 已存在，是否覆盖？",
                    "确认覆盖",
                    JOptionPane.YES_NO_OPTION);
            if (result != JOptionPane.YES_OPTION) {
                return;
            }
        }

        try {
            // 创建套装配置节点
            String basePath = "suit." + suitName;

            // 基本信息
            int quality = Integer.parseInt((String) this.qualityComboBox.getSelectedItem());
            Cuilian.Suit.set(basePath + ".quality", quality);

            // 装备名称
            Cuilian.Suit.set(basePath + ".item.head", this.headNameField.getText().trim());
            Cuilian.Suit.set(basePath + ".item.chest", this.chestNameField.getText().trim());
            Cuilian.Suit.set(basePath + ".item.leg", this.legNameField.getText().trim());
            Cuilian.Suit.set(basePath + ".item.foot", this.footNameField.getText().trim());

            // 武器（如果有输入）
            String swordName = this.swordNameField.getText().trim();
            String bowName = this.bowNameField.getText().trim();
            if (!swordName.isEmpty() || !bowName.isEmpty()) {
                if (!swordName.isEmpty()) {
                    Cuilian.Suit.set(basePath + ".item.weapon.sword", swordName);
                }
                if (!bowName.isEmpty()) {
                    Cuilian.Suit.set(basePath + ".item.weapon.bow", bowName);
                }
            }

            // 属性配置
            Cuilian.Suit.set(basePath + ".attribute.attack_damage", Integer.parseInt(this.attackField.getText()));
            Cuilian.Suit.set(basePath + ".attribute.defense", Integer.parseInt(this.defenseField.getText()));
            Cuilian.Suit.set(basePath + ".attribute.health", Integer.parseInt(this.healthField.getText()));
            Cuilian.Suit.set(basePath + ".attribute.speed", Double.parseDouble(this.speedField.getText()));
            Cuilian.Suit.set(basePath + ".attribute.jump", Integer.parseInt(this.jumpField.getText()));
            Cuilian.Suit.set(basePath + ".attribute.vampire", Integer.parseInt(this.vampireField.getText()));

            // 无限耐久配置
            Cuilian.Suit.set(basePath + ".attribute.infinite_durability", this.infiniteDurabilityCheckBox.isSelected());

            // 特效配置
            if (this.effectsModel.getSize() > 0) {
                Cuilian.Suit.set(basePath + ".effect.enable_stacking", true);

                // 创建特效列表
                java.util.List<java.util.Map<String, Object>> effectsList = new java.util.ArrayList<>();

                for (int i = 0; i < this.effectsModel.getSize(); i++) {
                    String effectString = this.effectsModel.getElementAt(i);
                    // 解析特效字符串: "effectType (color1,color2,color3)"
                    String[] parts = effectString.split(" \\(");
                    if (parts.length == 2) {
                        String effectType = parts[0];
                        String colorsPart = parts[1].replace(")", "");
                        String[] colors = colorsPart.split(",");

                        if (colors.length == 3) {
                            java.util.Map<String, Object> effectMap = new java.util.HashMap<>();
                            effectMap.put("type", effectType);
                            effectMap.put("enabled", true);
                            effectMap.put("colore1", colors[0]);
                            effectMap.put("colore2", colors[1]);
                            effectMap.put("colore3", colors[2]);
                            effectsList.add(effectMap);
                        }
                    }
                }

                Cuilian.Suit.set(basePath + ".effect.effects", effectsList);

                // 兼容旧格式（使用第一个特效）
                if (!effectsList.isEmpty()) {
                    java.util.Map<String, Object> firstEffect = effectsList.get(0);
                    Cuilian.Suit.set(basePath + ".effect.type", firstEffect.get("type"));
                    Cuilian.Suit.set(basePath + ".effect.color1", firstEffect.get("colore1"));
                    Cuilian.Suit.set(basePath + ".effect.color2", firstEffect.get("colore2"));
                    Cuilian.Suit.set(basePath + ".effect.color3", firstEffect.get("colore3"));
                }
            }

            // 宝石等级配置
            Cuilian.Suit.set(basePath + ".gem_levels.head", Integer.parseInt(this.headGemField.getText()));
            Cuilian.Suit.set(basePath + ".gem_levels.chest", Integer.parseInt(this.chestGemField.getText()));
            Cuilian.Suit.set(basePath + ".gem_levels.leg", Integer.parseInt(this.legGemField.getText()));
            Cuilian.Suit.set(basePath + ".gem_levels.foot", Integer.parseInt(this.footGemField.getText()));
            if (!swordName.isEmpty()) {
                Cuilian.Suit.set(basePath + ".gem_levels.sword", Integer.parseInt(this.swordGemField.getText()));
            }
            if (!bowName.isEmpty()) {
                Cuilian.Suit.set(basePath + ".gem_levels.bow", Integer.parseInt(this.bowGemField.getText()));
            }

            // 描述配置
            String descriptionText = this.descriptionArea.getText().trim();
            java.util.List<String> description = new java.util.ArrayList<>();
            if (!descriptionText.isEmpty()) {
                String[] lines = descriptionText.split("\n");
                for (String line : lines) {
                    if (!line.trim().isEmpty()) {
                        description.add(line.trim());
                    }
                }
            } else {
                // 如果没有输入描述，使用默认描述
                description.add("§6§l" + suitName + "套装");
                description.add("§e传说中的" + suitName.replace("套装", "") + "装者套装");
                description.add("§a拥有强大的力量和神秘的特效");
            }
            Cuilian.Suit.set(basePath + ".description", description);

            // 保存配置文件
            Cuilian.Suit.save(Cuilian.f5);

            JOptionPane.showMessageDialog(null, "套装 '" + suitName + "' 已成功保存到 Suit.yml！");

            // 自动更新所有相关的下拉列表
            updateAllSuitLists();

            // 清空表单
            clearSuitForm();

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请检查数值输入是否正确！");
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "保存套装时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 清空套装创建表单
     */
    private void clearSuitForm() {
        this.suitNameField.setText("");
        this.headNameField.setText("");
        this.chestNameField.setText("");
        this.legNameField.setText("");
        this.footNameField.setText("");
        this.swordNameField.setText("");
        this.bowNameField.setText("");
        this.attackField.setText("15");
        this.defenseField.setText("20");
        this.healthField.setText("40");
        this.speedField.setText("0.1");
        this.jumpField.setText("1");
        this.vampireField.setText("10");
        // 重置无限耐久开关
        this.infiniteDurabilityCheckBox.setSelected(false);
        // 清空宝石等级字段
        this.headGemField.setText("15");
        this.chestGemField.setText("15");
        this.legGemField.setText("15");
        this.footGemField.setText("15");
        this.swordGemField.setText("18");
        this.bowGemField.setText("16");
        // 清空描述
        this.descriptionArea.setText("§6§l自定义套装\n§e传说中的装者套装\n§a拥有强大的力量和神秘的特效");
        // 清空特效
        this.effectsModel.clear();
        this.qualityComboBox.setSelectedIndex(5);
        this.effectTypeComboBox.setSelectedIndex(0);
        this.color1ComboBox.setSelectedIndex(0);
        this.color2ComboBox.setSelectedIndex(1);
        this.color3ComboBox.setSelectedIndex(4);
    }

    /**
     * 动态加载可用套装列表
     */
    private void loadAvailableSuits() {
        this.suitComboBox.removeAllItems();
        this.suitComboBox.addItem("选择套装");

        try {
            // 从Suit.yml配置文件中读取所有套装
            if (Cuilian.Suit != null) {
                org.bukkit.configuration.ConfigurationSection suitSection = Cuilian.Suit
                        .getConfigurationSection("suit");
                if (suitSection != null) {
                    java.util.Set<String> suitNames = suitSection.getKeys(false);
                    for (String suitName : suitNames) {
                        // 根据套装品质获取正确的颜色代码
                        String coloredSuitName = getSuitWithQualityColor(suitName);
                        this.suitComboBox.addItem(coloredSuitName);
                        System.out.println("添加套装: " + coloredSuitName); // 调试信息
                    }
                }
            }
        } catch (Exception e) {
            // 如果读取失败，添加一些默认套装（带颜色代码用于测试）
            this.suitComboBox.addItem("§6§l神武套装");
            this.suitComboBox.addItem("§a§l星辰套装");
            this.suitComboBox.addItem("§c§l暗影套装");
            System.out.println("加载套装列表时出现错误: " + e.getMessage());
        }

        // 设置自定义渲染器
        this.suitComboBox.setRenderer(new ColorCodeListCellRenderer());
    }

    /**
     * 加载删除套装列表
     */
    private void loadDeleteSuitList() {
        this.deleteSuitComboBox.removeAllItems();
        this.deleteSuitComboBox.addItem("选择要删除的套装");

        try {
            // 从Suit.yml配置文件中读取所有套装
            if (Cuilian.Suit != null) {
                org.bukkit.configuration.ConfigurationSection suitSection = Cuilian.Suit
                        .getConfigurationSection("suit");
                if (suitSection != null) {
                    java.util.Set<String> suitNames = suitSection.getKeys(false);
                    for (String suitName : suitNames) {
                        // 根据套装品质获取正确的颜色代码
                        String coloredSuitName = getSuitWithQualityColor(suitName);
                        this.deleteSuitComboBox.addItem(coloredSuitName);
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("加载删除套装列表时出现错误: " + e.getMessage());
        }

        // 设置自定义渲染器
        this.deleteSuitComboBox.setRenderer(new ColorCodeListCellRenderer());
    }

    /**
     * 移除Minecraft颜色代码
     *
     * @param text 包含颜色代码的文本
     * @return 移除颜色代码后的纯文本
     */
    private String removeColorCodes(String text) {
        if (text == null) {
            return "";
        }
        // 移除所有Minecraft颜色代码和格式代码
        return text.replaceAll("§[0-9a-fk-or]", "");
    }

    /**
     * 根据套装品质获取带颜色代码的套装名称
     *
     * @param suitName 套装名称
     * @return 带品质颜色的套装名称
     */
    private String getSuitWithQualityColor(String suitName) {
        try {
            // 获取套装的品质等级
            int quality = Cuilian.Suit.getInt("suit." + suitName + ".quality", 5); // 默认传说品质

            // 获取品质对应的颜色代码
            String colorCode = Cuilian.Suit.getString("quality.levels." + quality + ".color", "§6");

            // 返回带颜色代码的套装名称
            return colorCode + "§l" + suitName;
        } catch (Exception e) {
            System.out.println("获取套装颜色时出现错误: " + e.getMessage());
            // 如果出错，返回默认金色
            return "§6§l" + suitName;
        }
    }

    /**
     * 自定义的列表单元格渲染器，用于处理Minecraft颜色代码
     */
    private static class ColorCodeListCellRenderer extends javax.swing.DefaultListCellRenderer {
        @Override
        public java.awt.Component getListCellRendererComponent(
                javax.swing.JList<?> list,
                Object value,
                int index,
                boolean isSelected,
                boolean cellHasFocus) {

            super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);

            if (value != null) {
                String text = value.toString();

                // 移除颜色代码并设置为显示文本
                String cleanText = removeColorCodes(text);
                setText(cleanText);

                // 解析颜色代码并应用颜色
                java.awt.Color textColor = parseMinecraftColor(text);
                if (textColor != null && !isSelected) {
                    setForeground(textColor);
                }

                // 解析格式代码并应用样式
                boolean isBold = text.contains("§l");
                boolean isItalic = text.contains("§o");
                boolean isUnderlined = text.contains("§n");

                int style = java.awt.Font.PLAIN;
                if (isBold)
                    style |= java.awt.Font.BOLD;
                if (isItalic)
                    style |= java.awt.Font.ITALIC;

                setFont(getFont().deriveFont(style));
            }

            return this;
        }

        /**
         * 移除Minecraft颜色代码
         */
        private static String removeColorCodes(String text) {
            if (text == null)
                return "";
            return text.replaceAll("§[0-9a-fk-or]", "");
        }

        /**
         * 解析Minecraft颜色代码并返回对应的Java颜色
         */
        private static java.awt.Color parseMinecraftColor(String text) {
            if (text == null)
                return null;

            // 查找最后一个颜色代码
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("§([0-9a-f])");
            java.util.regex.Matcher matcher = pattern.matcher(text);

            String lastColorCode = null;
            while (matcher.find()) {
                lastColorCode = matcher.group(1);
            }

            if (lastColorCode == null)
                return null;

            // Minecraft颜色代码映射
            switch (lastColorCode) {
                case "0":
                    return new java.awt.Color(0, 0, 0); // 黑色
                case "1":
                    return new java.awt.Color(0, 0, 170); // 深蓝色
                case "2":
                    return new java.awt.Color(0, 170, 0); // 深绿色
                case "3":
                    return new java.awt.Color(0, 170, 170); // 深青色
                case "4":
                    return new java.awt.Color(170, 0, 0); // 深红色
                case "5":
                    return new java.awt.Color(170, 0, 170); // 紫色
                case "6":
                    return new java.awt.Color(255, 170, 0); // 金色
                case "7":
                    return new java.awt.Color(170, 170, 170); // 灰色
                case "8":
                    return new java.awt.Color(85, 85, 85); // 深灰色
                case "9":
                    return new java.awt.Color(85, 85, 255); // 蓝色
                case "a":
                    return new java.awt.Color(85, 255, 85); // 绿色
                case "b":
                    return new java.awt.Color(85, 255, 255); // 青色
                case "c":
                    return new java.awt.Color(255, 85, 85); // 红色
                case "d":
                    return new java.awt.Color(255, 85, 255); // 粉色
                case "e":
                    return new java.awt.Color(255, 255, 85); // 黄色
                case "f":
                    return new java.awt.Color(255, 255, 255); // 白色
                default:
                    return null;
            }
        }
    }

    /**
     * 删除套装从配置文件
     */
    private void deleteSuitFromConfig() {
        String selectedSuit = (String) this.deleteSuitComboBox.getSelectedItem();
        if (selectedSuit == null || selectedSuit.equals("选择要删除的套装")) {
            JOptionPane.showMessageDialog(null, "请选择要删除的套装！");
            return;
        }

        // 显示时移除颜色代码
        String displayName = removeColorCodes(selectedSuit);

        // 确认删除
        int result = JOptionPane.showConfirmDialog(null,
                "确定要删除套装 '" + displayName + "' 吗？\n此操作不可撤销！",
                "确认删除",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.WARNING_MESSAGE);

        if (result != JOptionPane.YES_OPTION) {
            return;
        }

        try {
            // 直接使用选中的套装名称（已经是原始名称）
            Cuilian.Suit.set("suit." + selectedSuit, null);

            // 保存配置文件
            Cuilian.Suit.save(Cuilian.f5);

            JOptionPane.showMessageDialog(null, "套装 '" + displayName + "' 已成功删除！");

            // 自动更新所有相关的下拉列表
            updateAllSuitLists();

        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "删除套装时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 更新所有套装相关的下拉列表
     */
    private void updateAllSuitLists() {
        // 更新玩家管理页面的套装发送列表
        loadAvailableSuits();

        // 更新套装创建页面的删除套装列表
        loadDeleteSuitList();
    }

    /**
     * 保存星级配置到配置文件
     */
    private void saveStarConfiguration() {
        String newStarLevel = this.starLevelField.getText().trim();
        String selectedExisting = (String) this.existingStarLevelComboBox.getSelectedItem();

        int level;
        boolean isNewLevel = false;

        if (!newStarLevel.isEmpty()) {
            // 创建新星级
            try {
                level = Integer.parseInt(newStarLevel);
                if (level < 0) {
                    JOptionPane.showMessageDialog(null, "星级等级必须大于等于0！");
                    return;
                }
                isNewLevel = true;
            } catch (NumberFormatException e) {
                JOptionPane.showMessageDialog(null, "请输入有效的星级数字！");
                return;
            }
        } else if (selectedExisting != null && !selectedExisting.equals("选择现有星级")) {
            // 编辑现有星级
            try {
                String levelStr = selectedExisting.replace("星", "");
                level = Integer.parseInt(levelStr);
            } catch (NumberFormatException e) {
                JOptionPane.showMessageDialog(null, "解析现有星级时出现错误！");
                return;
            }
        } else {
            JOptionPane.showMessageDialog(null, "请输入新星级等级或选择现有星级进行编辑！");
            return;
        }

        try {

            // 星级配置只创建新的淬炼等级，不包含特效配置
            // 特效配置需要在config.yml中单独设置

            // 保存到Weapon.yml的属性部分
            int attack = Integer.parseInt(this.starAttackField.getText());
            int defense = Integer.parseInt(this.starDefenseField.getText());
            int vampire = Integer.parseInt(this.starVampireField.getText());
            int jump = Integer.parseInt(this.starJumpField.getText());
            int fallDamage = Integer.parseInt(this.starFallDamageField.getText());
            int counterAttack = Integer.parseInt(this.starCounterAttackField.getText());

            Cuilian.Weapon.set("shanghai." + level, attack);
            Cuilian.Weapon.set("Defense." + level, defense);
            Cuilian.Weapon.set("xixue." + level, vampire);
            Cuilian.Weapon.set("jump." + level, jump);
            Cuilian.Weapon.set("js." + level, fallDamage);
            Cuilian.Weapon.set("fs." + level, counterAttack);

            // 保存到Probability.yml的强化石几率部分 - 只针对当前星级
            int putongProb = Integer.parseInt(this.putongProbabilityField.getText());
            int zhongdengProb = Integer.parseInt(this.zhongdengProbabilityField.getText());
            int gaodengProb = Integer.parseInt(this.gaodengProbabilityField.getText());
            int wanmeiProb = Integer.parseInt(this.wanmeiProbabilityField.getText());

            Cuilian.Probability.set("putong." + level, putongProb);
            Cuilian.Probability.set("zhongdeng." + level, zhongdengProb);
            Cuilian.Probability.set("gaodeng." + level, gaodengProb);
            Cuilian.Probability.set("wanmei." + level, wanmeiProb);

            // 保存所有配置文件
            Cuilian.config.save(Cuilian.filess);
            Cuilian.Weapon.save(Cuilian.f);
            Cuilian.Probability.save(Cuilian.f6);

            // 重新加载配置到内存中的HashMap，确保新配置生效
            reloadConfigurationData();

            String actionType = isNewLevel ? "创建" : "更新";
            JOptionPane.showMessageDialog(null, "星级配置 " + level + "星 已成功" + actionType + "！\n配置已重新加载，可以立即使用。");

            // 自动更新现有星级列表
            loadExistingStarLevels();

            // 清空表单
            clearStarForm();

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请检查数值输入是否正确！");
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "保存星级配置时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 清空星级配置表单
     */
    private void clearStarForm() {
        this.starLevelField.setText("");
        this.existingStarLevelComboBox.setSelectedIndex(0);
        this.starAttackField.setText("0");
        this.starDefenseField.setText("0");
        this.starVampireField.setText("0");
        this.starJumpField.setText("0");
        this.starFallDamageField.setText("0");
        this.starCounterAttackField.setText("0");

        // 重置强化石几率为默认值
        this.putongProbabilityField.setText("100");
        this.zhongdengProbabilityField.setText("100");
        this.gaodengProbabilityField.setText("100");
        this.wanmeiProbabilityField.setText("100");
    }

    /**
     * 加载现有星级列表
     */
    private void loadExistingStarLevels() {
        this.existingStarLevelComboBox.removeAllItems();
        this.existingStarLevelComboBox.addItem("选择现有星级");

        try {
            // 从Weapon.yml中读取所有星级，使用TreeSet收集所有数字星级
            java.util.Set<Integer> starLevelNumbers = new java.util.TreeSet<>();

            if (Cuilian.Weapon != null) {
                // 从各个属性配置中收集星级
                org.bukkit.configuration.ConfigurationSection shanghaiSection = Cuilian.Weapon
                        .getConfigurationSection("shanghai");
                if (shanghaiSection != null) {
                    for (String level : shanghaiSection.getKeys(false)) {
                        try {
                            int levelNum = Integer.parseInt(level);
                            starLevelNumbers.add(levelNum);
                        } catch (NumberFormatException e) {
                            // 忽略非数字的配置项
                        }
                    }
                }

                org.bukkit.configuration.ConfigurationSection defenseSection = Cuilian.Weapon
                        .getConfigurationSection("Defense");
                if (defenseSection != null) {
                    for (String level : defenseSection.getKeys(false)) {
                        try {
                            int levelNum = Integer.parseInt(level);
                            starLevelNumbers.add(levelNum);
                        } catch (NumberFormatException e) {
                            // 忽略非数字的配置项
                        }
                    }
                }

                // 按数字从小到大的顺序添加到下拉列表
                for (Integer levelNum : starLevelNumbers) {
                    this.existingStarLevelComboBox.addItem(levelNum + "星");
                }
            }
        } catch (Exception e) {
            System.out.println("加载现有星级列表时出现错误: " + e.getMessage());
        }
    }

    /**
     * 加载选中星级的数据到表单
     */
    private void loadExistingStarLevelData() {
        String selectedItem = (String) this.existingStarLevelComboBox.getSelectedItem();
        if (selectedItem == null || selectedItem.equals("选择现有星级")) {
            return;
        }

        try {
            // 提取星级数字
            String levelStr = selectedItem.replace("星", "");
            int level = Integer.parseInt(levelStr);

            // 清空新建星级输入框
            this.starLevelField.setText("");

            // 加载属性数据
            if (Cuilian.Weapon != null) {
                this.starAttackField.setText(String.valueOf(Cuilian.Weapon.getInt("shanghai." + level, 0)));
                this.starDefenseField.setText(String.valueOf(Cuilian.Weapon.getInt("Defense." + level, 0)));
                this.starVampireField.setText(String.valueOf(Cuilian.Weapon.getInt("xixue." + level, 0)));
                this.starJumpField.setText(String.valueOf(Cuilian.Weapon.getInt("jump." + level, 0)));
                this.starFallDamageField.setText(String.valueOf(Cuilian.Weapon.getInt("js." + level, 0)));
                this.starCounterAttackField.setText(String.valueOf(Cuilian.Weapon.getInt("fs." + level, 0)));
            }

            // 加载强化石几率数据
            if (Cuilian.Probability != null) {
                this.putongProbabilityField.setText(String.valueOf(Cuilian.Probability.getInt("putong." + level, 100)));
                this.zhongdengProbabilityField
                        .setText(String.valueOf(Cuilian.Probability.getInt("zhongdeng." + level, 100)));
                this.gaodengProbabilityField
                        .setText(String.valueOf(Cuilian.Probability.getInt("gaodeng." + level, 100)));
                this.wanmeiProbabilityField.setText(String.valueOf(Cuilian.Probability.getInt("wanmei." + level, 100)));
            }

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "解析星级数据时出现错误！");
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "加载星级数据时出现错误: " + e.getMessage());
        }
    }

    /**
     * 重新加载配置数据到内存中的HashMap
     */
    private void reloadConfigurationData() {
        try {
            // 重新加载Weapon.yml配置到HashMap
            if (Cuilian.Weapon != null) {
                cn.winde.cuilian.Loadlang.loadWeapon(Cuilian.Weapon);
            }

            // 重新加载Probability.yml配置到HashMap
            if (Cuilian.Probability != null) {
                cn.winde.cuilian.Loadlang.loadProbability(Cuilian.Probability);
            }

            System.out.println("配置数据已重新加载到内存");
        } catch (Exception e) {
            System.out.println("重新加载配置数据时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 重新加载特效配置到游戏中
     */
    private void reloadEffectConfiguration() {
        try {
            // 保存当前选中的星级
            String currentSelectedLevel = (String) this.effectStarLevelComboBox.getSelectedItem();

            // 重新加载config.yml配置文件
            if (Cuilian.filess != null && Cuilian.filess.exists()) {
                Cuilian.config = org.bukkit.configuration.file.YamlConfiguration.loadConfiguration(Cuilian.filess);
                System.out.println("特效配置已重新加载到游戏中");
            }

            // 刷新UI中的星级列表（可能有新增的星级）
            initializeEffectConfigData();

            // 恢复之前选中的星级
            if (currentSelectedLevel != null && !currentSelectedLevel.equals("选择星级")) {
                this.effectStarLevelComboBox.setSelectedItem(currentSelectedLevel);
            }

            // 重新加载当前选中星级的特效列表
            loadEffectsForSelectedLevel();

        } catch (Exception e) {
            System.out.println("重新加载特效配置时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 更新状态标签显示
     */
    private void updateStatusLabel(String message, boolean isSuccess) {
        if (this.statusLabel != null) {
            this.statusLabel.setText(message);
            if (isSuccess) {
                this.statusLabel.setForeground(new java.awt.Color(0, 128, 0)); // 绿色
            } else {
                this.statusLabel.setForeground(new java.awt.Color(255, 0, 0)); // 红色
            }

            // 3秒后恢复为就绪状态
            javax.swing.Timer timer = new javax.swing.Timer(3000, e -> {
                this.statusLabel.setText("就绪");
                this.statusLabel.setForeground(new java.awt.Color(0, 128, 0));
            });
            timer.setRepeats(false);
            timer.start();
        }
    }

    /**
     * 根据英文特效类型获取中文显示名称
     */
    private String getEffectTypeDisplayName(String effectType) {
        java.util.Map<String, String> effectTypeMap = new java.util.HashMap<>();

        // 淬炼5.6版本经典特效
        effectTypeMap.put("flame_basic", "flame_basic - 基础火焰");
        effectTypeMap.put("footstep", "footstep - 脚步");
        effectTypeMap.put("spiral_flame", "spiral_flame - 螺旋火焰");
        effectTypeMap.put("rotating_halo", "rotating_halo - 旋转光环");
        effectTypeMap.put("four_direction_spell", "four_direction_spell - 四方向法术");
        effectTypeMap.put("wings_56", "wings_56 - 5.6版本翅膀");

        // 现代版本基础特效
        effectTypeMap.put("wings", "wings - 翅膀");
        effectTypeMap.put("halo", "halo - 光环");
        effectTypeMap.put("flame", "flame - 火焰");
        effectTypeMap.put("nebula", "nebula - 星云");
        effectTypeMap.put("tornado", "tornado - 龙卷风");
        effectTypeMap.put("stars", "stars - 星星");

        // 现代版本几何特效
        effectTypeMap.put("spiral", "spiral - 螺旋");
        effectTypeMap.put("sphere", "sphere - 球体");
        effectTypeMap.put("orbit", "orbit - 轨道");
        effectTypeMap.put("cube", "cube - 立方体");
        effectTypeMap.put("helix", "helix - 螺旋桨");

        // 现代版本动态特效
        effectTypeMap.put("batman", "batman - 蝙蝠侠");
        effectTypeMap.put("popper", "popper - 爆炸螺旋");
        effectTypeMap.put("pulse", "pulse - 脉冲");
        effectTypeMap.put("whirl", "whirl - 旋涡");
        effectTypeMap.put("whirlwind", "whirlwind - 旋风");
        effectTypeMap.put("invocation", "invocation - 召唤法阵");

        // 现代版本高级特效
        effectTypeMap.put("lightning", "lightning - 雷电");
        effectTypeMap.put("rainbow", "rainbow - 彩虹");
        effectTypeMap.put("spacerift", "spacerift - 时空裂缝");
        effectTypeMap.put("frost", "frost - 冰霜");
        effectTypeMap.put("shadow", "shadow - 暗影");

        // PlayerParticles集成特效
        effectTypeMap.put("pp_wings", "pp_wings - PP翅膀");
        effectTypeMap.put("pp_beam", "pp_beam - PP光束");
        effectTypeMap.put("pp_quadhelix", "pp_quadhelix - PP四重螺旋");
        effectTypeMap.put("pp_swords", "pp_swords - PP剑击");

        return effectTypeMap.getOrDefault(effectType, effectType + " - 未知特效");
    }

    /**
     * 初始化特效配置数据
     */
    private void initializeEffectConfigData() {
        // 初始化星级选择下拉框
        this.effectStarLevelComboBox.removeAllItems();
        this.effectStarLevelComboBox.addItem("选择星级");

        // 动态读取已配置的星级
        try {
            if (Cuilian.config != null && Cuilian.config.isConfigurationSection("eff")) {
                org.bukkit.configuration.ConfigurationSection effSection = Cuilian.config
                        .getConfigurationSection("eff");
                java.util.Set<String> levelKeys = effSection.getKeys(false);

                // 提取数字并排序
                java.util.List<Integer> levelNumbers = new java.util.ArrayList<>();
                for (String key : levelKeys) {
                    if (key.startsWith("leve")) {
                        try {
                            int level = Integer.parseInt(key.substring(4));
                            levelNumbers.add(level);
                        } catch (NumberFormatException e) {
                            // 忽略无效的星级配置
                        }
                    }
                }

                // 排序并添加到下拉框
                java.util.Collections.sort(levelNumbers);
                for (Integer level : levelNumbers) {
                    this.effectStarLevelComboBox.addItem(level + "星");
                }
            } else {
                // 如果配置文件不可用，使用默认星级
                String[] defaultLevels = { "6", "9", "12", "15", "18", "19" };
                for (String level : defaultLevels) {
                    this.effectStarLevelComboBox.addItem(level + "星");
                }
            }
        } catch (Exception e) {
            // 出错时使用默认星级
            String[] defaultLevels = { "6", "9", "12", "15", "18", "19" };
            for (String level : defaultLevels) {
                this.effectStarLevelComboBox.addItem(level + "星");
            }
        }

        // 初始化特效类型下拉框
        this.availableEffectTypeComboBox.removeAllItems();
        String[] effectTypes = {
                // 淬炼5.6版本经典特效
                "flame_basic - 基础火焰", "footstep - 脚步", "spiral_flame - 螺旋火焰",
                "rotating_halo - 旋转光环", "four_direction_spell - 四方向法术", "wings_56 - 5.6版本翅膀",
                // 现代版本基础特效
                "wings - 翅膀", "halo - 光环", "flame - 火焰", "nebula - 星云", "tornado - 龙卷风", "stars - 星星",
                // 现代版本几何特效
                "spiral - 螺旋", "sphere - 球体", "orbit - 轨道", "cube - 立方体", "helix - 螺旋桨",
                // 现代版本动态特效
                "batman - 蝙蝠侠", "popper - 爆炸螺旋", "pulse - 脉冲", "whirl - 旋涡", "whirlwind - 旋风", "invocation - 召唤法阵",
                // 现代版本高级特效
                "lightning - 雷电", "rainbow - 彩虹", "spacerift - 时空裂缝", "frost - 冰霜", "shadow - 暗影",
                // PlayerParticles集成特效
                "pp_wings - PP翅膀", "pp_beam - PP光束", "pp_quadhelix - PP四重螺旋", "pp_swords - PP剑击"
        };

        for (String effectType : effectTypes) {
            this.availableEffectTypeComboBox.addItem(effectType);
        }

        // 初始化颜色下拉框
        String[] colors = { "红", "橙", "黄", "绿", "蓝", "紫", "粉", "黑", "白", "灰", "银" };

        this.effectColor1ComboBox.removeAllItems();
        this.effectColor2ComboBox.removeAllItems();
        this.effectColor3ComboBox.removeAllItems();

        for (String color : colors) {
            this.effectColor1ComboBox.addItem(color);
            this.effectColor2ComboBox.addItem(color);
            this.effectColor3ComboBox.addItem(color);
        }

        // 设置默认颜色
        this.effectColor1ComboBox.setSelectedItem("红");
        this.effectColor2ComboBox.setSelectedItem("橙");
        this.effectColor3ComboBox.setSelectedItem("黄");
    }

    /**
     * 加载选中星级的特效列表
     */
    private void loadEffectsForSelectedLevel() {
        String selectedLevel = (String) this.effectStarLevelComboBox.getSelectedItem();
        if (selectedLevel == null || selectedLevel.equals("选择星级")) {
            this.currentEffectsModel.clear();
            return;
        }

        // 提取星级数字
        String levelStr = selectedLevel.replace("星", "");

        this.currentEffectsModel.clear();

        try {
            // 从config.yml读取特效配置
            if (Cuilian.config != null) {
                String configPath = "eff.leve" + levelStr + ".effects";
                if (Cuilian.config.isList(configPath)) {
                    java.util.List<?> effectsList = Cuilian.config.getList(configPath);
                    if (effectsList != null) {
                        for (Object effectObj : effectsList) {
                            if (effectObj instanceof java.util.Map) {
                                @SuppressWarnings("unchecked")
                                java.util.Map<String, Object> effectMap = (java.util.Map<String, Object>) effectObj;
                                String type = (String) effectMap.get("type");
                                boolean enabled = (Boolean) effectMap.getOrDefault("enabled", true);
                                String color1 = (String) effectMap.getOrDefault("colore1", "红");
                                String color2 = (String) effectMap.getOrDefault("colore2", "橙");
                                String color3 = (String) effectMap.getOrDefault("colore3", "黄");

                                // 获取中文描述
                                String typeDisplay = getEffectTypeDisplayName(type);

                                String displayText = String.format("%s [%s] (%s,%s,%s)",
                                        typeDisplay, enabled ? "启用" : "禁用", color1, color2, color3);
                                this.currentEffectsModel.addElement(displayText);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "加载特效配置时出现错误: " + e.getMessage());
        }
    }

    /**
     * 添加特效到当前星级
     */
    private void addEffectToCurrentLevel() {
        String selectedLevel = (String) this.effectStarLevelComboBox.getSelectedItem();
        if (selectedLevel == null || selectedLevel.equals("选择星级")) {
            JOptionPane.showMessageDialog(null, "请先选择星级！");
            return;
        }

        String effectTypeDisplay = (String) this.availableEffectTypeComboBox.getSelectedItem();
        if (effectTypeDisplay == null) {
            JOptionPane.showMessageDialog(null, "请选择特效类型！");
            return;
        }

        boolean enabled = this.effectEnabledCheckBox.isSelected();
        String color1 = (String) this.effectColor1ComboBox.getSelectedItem();
        String color2 = (String) this.effectColor2ComboBox.getSelectedItem();
        String color3 = (String) this.effectColor3ComboBox.getSelectedItem();

        // 添加到显示列表（显示中文描述）
        String displayText = String.format("%s [%s] (%s,%s,%s)",
                effectTypeDisplay, enabled ? "启用" : "禁用", color1, color2, color3);
        this.currentEffectsModel.addElement(displayText);

        // 自动保存配置并重新加载
        saveEffectConfigurationSilently();

        // 更新状态标签
        updateStatusLabel("特效已添加并自动保存: " + effectTypeDisplay.split(" - ")[1], true);
        System.out.println("特效已添加并自动保存: " + effectTypeDisplay);
    }

    /**
     * 加载选中的特效到编辑区域
     */
    private void loadSelectedEffectForEditing() {
        int selectedIndex = this.currentEffectsList.getSelectedIndex();
        if (selectedIndex == -1) {
            return;
        }

        String effectText = this.currentEffectsModel.getElementAt(selectedIndex);

        try {
            // 解析特效文本: "type - 中文 [启用/禁用] (color1,color2,color3)"
            String[] parts = effectText.split(" \\[");
            if (parts.length >= 2) {
                String typeDisplay = parts[0];
                String[] statusAndColors = parts[1].split("\\] \\(");
                if (statusAndColors.length >= 2) {
                    boolean enabled = statusAndColors[0].equals("启用");
                    String colorsStr = statusAndColors[1].replace(")", "");
                    String[] colors = colorsStr.split(",");

                    // 设置编辑区域的值（使用完整的显示名称）
                    this.availableEffectTypeComboBox.setSelectedItem(typeDisplay);
                    this.effectEnabledCheckBox.setSelected(enabled);

                    if (colors.length > 0)
                        this.effectColor1ComboBox.setSelectedItem(colors[0]);
                    if (colors.length > 1)
                        this.effectColor2ComboBox.setSelectedItem(colors[1]);
                    if (colors.length > 2)
                        this.effectColor3ComboBox.setSelectedItem(colors[2]);

                    // 更新状态标签
                    updateStatusLabel("特效已加载到编辑区域: " + typeDisplay.split(" - ")[1], true);
                    System.out.println("特效已加载到编辑区域: " + typeDisplay);
                }
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "解析特效数据时出现错误: " + e.getMessage());
        }
    }

    /**
     * 删除选中的特效
     */
    private void removeSelectedEffect() {
        int selectedIndex = this.currentEffectsList.getSelectedIndex();
        if (selectedIndex == -1) {
            JOptionPane.showMessageDialog(null, "请先选择要删除的特效！");
            return;
        }

        this.currentEffectsModel.remove(selectedIndex);

        // 自动保存配置并重新加载
        saveEffectConfigurationSilently();

        // 更新状态标签
        updateStatusLabel("特效已删除并自动保存", true);
        System.out.println("特效已删除并自动保存");
    }

    /**
     * 静默保存特效配置（不显示消息框）
     */
    private void saveEffectConfigurationSilently() {
        String selectedLevel = (String) this.effectStarLevelComboBox.getSelectedItem();
        if (selectedLevel == null || selectedLevel.equals("选择星级")) {
            return;
        }

        String levelStr = selectedLevel.replace("星", "");

        try {
            // 构建特效列表
            java.util.List<java.util.Map<String, Object>> effectsList = new java.util.ArrayList<>();

            for (int i = 0; i < this.currentEffectsModel.size(); i++) {
                String effectText = this.currentEffectsModel.getElementAt(i);

                // 解析特效文本: "type - 中文 [启用/禁用] (color1,color2,color3)"
                String[] parts = effectText.split(" \\[");
                if (parts.length >= 2) {
                    String typeDisplay = parts[0];
                    // 提取英文部分作为实际的特效类型
                    String type = typeDisplay.split(" - ")[0];

                    String[] statusAndColors = parts[1].split("\\] \\(");
                    if (statusAndColors.length >= 2) {
                        boolean enabled = statusAndColors[0].equals("启用");
                        String colorsStr = statusAndColors[1].replace(")", "");
                        String[] colors = colorsStr.split(",");

                        java.util.Map<String, Object> effectMap = new java.util.HashMap<>();
                        effectMap.put("type", type);
                        effectMap.put("enabled", enabled);
                        effectMap.put("colore1", colors.length > 0 ? colors[0] : "红");
                        effectMap.put("colore2", colors.length > 1 ? colors[1] : "橙");
                        effectMap.put("colore3", colors.length > 2 ? colors[2] : "黄");

                        effectsList.add(effectMap);
                    }
                }
            }

            // 保存到配置文件
            String configPath = "eff.leve" + levelStr + ".effects";
            Cuilian.config.set(configPath, effectsList);

            // 保存文件
            Cuilian.config.save(Cuilian.filess);

            // 重新加载配置到游戏中
            reloadEffectConfiguration();

        } catch (Exception e) {
            System.out.println("静默保存特效配置时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 初始化符咒数据
     */
    private void initializeCharmData() {
        // 初始化符咒等级下拉框
        this.charmLevelComboBox.removeAllItems();

        // 动态获取最大配置星级
        try {
            int maxLevel = cn.winde.cuilian.Cuilian.getMaxConfiguredLevel();
            for (int i = 1; i <= maxLevel; i++) {
                this.charmLevelComboBox.addItem(i + "星");
            }
        } catch (Exception e) {
            // 如果出错，使用默认星级
            for (int i = 1; i <= 20; i++) {
                this.charmLevelComboBox.addItem(i + "星");
            }
        }

        // 初始化符咒在线玩家网格面板
        refreshCharmOnlinePlayers();
    }

    /**
     * 初始化符咒在线玩家列表
     */
    private void initializeCharmOnlinePlayersList() {
        this.charmOnlinePlayersModel.clear();

        // 使用延迟任务确保服务器完全启动后再加载玩家列表
        javax.swing.SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                try {
                    // 获取当前在线玩家列表
                    for (org.bukkit.entity.Player player : org.bukkit.Bukkit.getOnlinePlayers()) {
                        charmOnlinePlayersModel.addElement(player.getName());
                    }
                } catch (Exception e) {
                }
            }
        });
    }

    /**
     * 刷新符咒管理的在线玩家网格面板
     */
    private void refreshCharmOnlinePlayers() {
        if (this.charmPlayersGridPanel == null) {
            return;
        }

        java.util.List<String> onlinePlayers = new java.util.ArrayList<>();

        // 获取在线玩家
        try {
            if (org.bukkit.Bukkit.getServer() != null) {
                for (org.bukkit.entity.Player player : org.bukkit.Bukkit.getOnlinePlayers()) {
                    onlinePlayers.add(player.getName());
                }
            }
        } catch (Exception e) {
            // 如果获取失败，添加一些测试数据
            onlinePlayers.add("测试玩家1");
            onlinePlayers.add("测试玩家2");
            onlinePlayers.add("测试玩家3");
        }

        // 更新玩家网格面板
        this.charmPlayersGridPanel.updatePlayers(onlinePlayers);
    }

    /**
     * 刷新符咒在线玩家列表 (兼容旧方法)
     */
    private void refreshCharmOnlinePlayersList() {
        this.charmOnlinePlayersModel.clear();

        try {
            // 获取在线玩家列表
            for (org.bukkit.entity.Player player : org.bukkit.Bukkit.getOnlinePlayers()) {
                this.charmOnlinePlayersModel.addElement(player.getName());
            }
        } catch (Exception e) {
        }
    }

    /**
     * 给予符咒给玩家
     */
    private void giveCharmToPlayer() {
        String charmType = (String) this.charmTypeComboBox.getSelectedItem();
        String levelStr = (String) this.charmLevelComboBox.getSelectedItem();
        String quantityStr = this.charmQuantityField.getText();

        // 优先从网格面板获取选中的玩家，如果没有选中则从输入框获取
        String targetPlayer = null;
        if (this.charmPlayersGridPanel != null && this.charmPlayersGridPanel.getSelectedPlayer() != null) {
            targetPlayer = this.charmPlayersGridPanel.getSelectedPlayer();
        } else {
            targetPlayer = this.charmTargetPlayerField.getText();
        }

        if (charmType == null || levelStr == null) {
            JOptionPane.showMessageDialog(null, "请选择符咒类型和等级！");
            return;
        }

        if (targetPlayer == null || targetPlayer.trim().isEmpty()) {
            JOptionPane.showMessageDialog(null, "请选择在线玩家或输入离线玩家名！");
            return;
        }

        try {
            int level = Integer.parseInt(levelStr.replace("星", ""));
            int quantity = Integer.parseInt(quantityStr);

            if (quantity <= 0) {
                JOptionPane.showMessageDialog(null, "数量必须大于0！");
                return;
            }

            // 创建符咒物品
            ItemStack item;
            if (charmType.equals("淬炼符咒")) {
                item = cn.winde.cuilian.Cuilian.createCuilianCharm(level);
            } else {
                item = cn.winde.cuilian.Cuilian.createCuilianDirectRod(level);
            }

            if (item == null) {
                JOptionPane.showMessageDialog(null, "创建符咒失败！");
                return;
            }

            item.setAmount(quantity);

            // 检查玩家是否在线
            Player onlinePlayer = org.bukkit.Bukkit.getPlayer(targetPlayer.trim());
            if (onlinePlayer != null && onlinePlayer.isOnline()) {
                // 在线玩家：直接给予物品
                onlinePlayer.getInventory().addItem(item);
                onlinePlayer.sendMessage("§a你获得了 §6" + quantity + "个 " + level + "星" + charmType);

                JOptionPane.showMessageDialog(null,
                        "成功给予在线玩家 " + targetPlayer.trim() + " " + quantity + "个 " + level + "星" + charmType);
            } else {
                // 离线玩家：使用命令方式给予
                try {
                    // 检查玩家是否存在于服务器
                    @SuppressWarnings("deprecation")
                    OfflinePlayer offlinePlayer = org.bukkit.Bukkit.getOfflinePlayer(targetPlayer.trim());

                    if (offlinePlayer != null && offlinePlayer.hasPlayedBefore()) {
                        // 使用控制台命令给予离线玩家物品
                        String command = String.format("cuilian give %s %s %d %d",
                                targetPlayer.trim(),
                                charmType.equals("淬炼符咒") ? "charm" : "directrod",
                                level,
                                quantity);

                        // 在主线程中执行命令
                        org.bukkit.Bukkit.getScheduler().runTask(cn.winde.cuilian.Cuilian.getInstance(), () -> {
                            org.bukkit.Bukkit.getServer().dispatchCommand(
                                    org.bukkit.Bukkit.getConsoleSender(),
                                    command);
                        });

                        JOptionPane.showMessageDialog(null,
                                "成功给予离线玩家 " + targetPlayer.trim() + " " + quantity + "个 " + level + "星" + charmType +
                                        "\n物品将在玩家下次上线时发送到背包");
                    } else {
                        JOptionPane.showMessageDialog(null,
                                "玩家 " + targetPlayer.trim() + " 从未进入过服务器！\n请确认玩家名称正确。");
                    }
                } catch (Exception ex) {
                    // 如果命令执行失败，提示用户手动操作
                    JOptionPane.showMessageDialog(null,
                            "自动给予离线玩家失败！\n" +
                                    "请在游戏中手动执行以下命令：\n" +
                                    "/cuilian give " + targetPlayer.trim() + " " +
                                    (charmType.equals("淬炼符咒") ? "charm" : "directrod") + " " +
                                    level + " " + quantity);
                }
            }

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "等级和数量必须是有效数字！");
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "给予符咒时出现错误: " + e.getMessage());
        }
    }

    /**
     * 静态方法：处理玩家加入事件
     * 自动更新UI中的在线玩家列表
     */
    public static void onPlayerJoin(String playerName) {
        if (instance != null) {
            // 使用SwingUtilities确保在EDT线程中执行UI更新
            javax.swing.SwingUtilities.invokeLater(new Runnable() {
                @Override
                public void run() {
                    try {
                        // 更新符咒管理的在线玩家列表
                        if (instance.charmOnlinePlayersModel != null) {
                            // 检查玩家是否已经在列表中
                            boolean playerExists = false;
                            for (int i = 0; i < instance.charmOnlinePlayersModel.getSize(); i++) {
                                if (playerName.equals(instance.charmOnlinePlayersModel.getElementAt(i))) {
                                    playerExists = true;
                                    break;
                                }
                            }

                            // 如果玩家不在列表中，添加到列表
                            if (!playerExists) {
                                instance.charmOnlinePlayersModel.addElement(playerName);
                            }
                        }

                        // 直接向网格面板添加玩家，而不是重新刷新整个列表
                        if (instance.onlinePlayersGridPanel != null) {
                            instance.onlinePlayersGridPanel.addPlayer(playerName);
                        }

                        // 同时更新符咒管理的网格面板
                        if (instance.charmPlayersGridPanel != null) {
                            instance.charmPlayersGridPanel.addPlayer(playerName);
                        }

                        // 同时更新游戏内GUI面板的玩家列表
                        if (instance.guiPlayersGridPanel != null) {
                            instance.guiPlayersGridPanel.addPlayer(playerName);
                        }

                        // 同时更新批量操作面板的玩家列表
                        if (instance.batchPlayersModel != null) {
                            // 检查玩家是否已经在列表中
                            boolean playerExists = false;
                            for (int i = 0; i < instance.batchPlayersModel.getSize(); i++) {
                                if (playerName.equals(instance.batchPlayersModel.getElementAt(i))) {
                                    playerExists = true;
                                    break;
                                }
                            }
                            // 如果玩家不在列表中，添加到列表
                            if (!playerExists) {
                                instance.batchPlayersModel.addElement(playerName);
                            }
                        }

                    } catch (Exception e) {
                        // 忽略错误
                    }
                }
            });
        }
    }

    /**
     * 静态方法：处理玩家退出事件
     * 自动更新UI中的在线玩家列表
     */
    public static void onPlayerLeave(String playerName) {
        if (instance != null) {
            // 使用SwingUtilities确保在EDT线程中执行UI更新
            javax.swing.SwingUtilities.invokeLater(new Runnable() {
                @Override
                public void run() {
                    try {
                        // 更新符咒管理的在线玩家列表
                        if (instance.charmOnlinePlayersModel != null) {
                            instance.charmOnlinePlayersModel.removeElement(playerName);
                        }

                        // 如果当前输入框中是该玩家，清空输入框
                        if (instance.charmTargetPlayerField != null &&
                                playerName.equals(instance.charmTargetPlayerField.getText())) {
                            instance.charmTargetPlayerField.setText("");
                        }

                        // 如果离线玩家输入框中是该玩家，清空输入框
                        if (instance.offlinePlayerField != null &&
                                playerName.equals(instance.offlinePlayerField.getText())) {
                            instance.offlinePlayerField.setText("");
                        }

                        // 直接从网格面板移除玩家，而不是重新刷新整个列表
                        if (instance.onlinePlayersGridPanel != null) {
                            instance.onlinePlayersGridPanel.removePlayer(playerName);
                        }

                        // 同时从符咒管理的网格面板移除玩家
                        if (instance.charmPlayersGridPanel != null) {
                            instance.charmPlayersGridPanel.removePlayer(playerName);
                        }

                        // 同时从游戏内GUI面板移除玩家
                        if (instance.guiPlayersGridPanel != null) {
                            instance.guiPlayersGridPanel.removePlayer(playerName);
                        }

                        // 如果游戏内GUI输入框中是该玩家，清空输入框
                        if (instance.guiPlayerNameField != null &&
                                playerName.equals(instance.guiPlayerNameField.getText())) {
                            instance.guiPlayerNameField.setText("");
                        }

                        // 同时更新批量操作面板的玩家列表
                        if (instance.batchPlayersModel != null) {
                            instance.batchPlayersModel.removeElement(playerName);
                        }

                    } catch (Exception e) {
                        // 忽略错误
                    }
                }
            });
        }
    }

    /**
     * 创建标题面板
     */
    private JPanel createTitlePanel() {
        JPanel titlePanel = new JPanel(new java.awt.FlowLayout(java.awt.FlowLayout.CENTER));
        titlePanel.setBackground(java.awt.Color.WHITE);
        titlePanel.setBorder(BorderFactory.createEmptyBorder(10, 0, 10, 0));

        JLabel titleLabel = new JLabel("淬炼插件管理界面 v2.0");
        titleLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 18));
        titleLabel.setForeground(new java.awt.Color(51, 102, 153));
        titlePanel.add(titleLabel);

        return titlePanel;
    }

    /**
     * 创建玩家管理面板
     */
    private JPanel createPlayerManagementPanel() {
        JPanel panel = new JPanel(new java.awt.BorderLayout());
        panel.setBackground(java.awt.Color.WHITE);

        // 创建可调节大小的分割面板
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(400); // 初始分割位置
        splitPane.setResizeWeight(0.4); // 左侧占40%权重
        splitPane.setBorder(null);
        splitPane.setBackground(java.awt.Color.WHITE);

        // 左侧面板 - 在线玩家列表
        JPanel leftPanel = createPlayerLeftPanel();
        splitPane.setLeftComponent(leftPanel);

        // 右侧面板 - 玩家物品管理
        JPanel rightPanel = createPlayerRightPanel();
        splitPane.setRightComponent(rightPanel);

        panel.add(splitPane, java.awt.BorderLayout.CENTER);

        // 初始化在线玩家列表
        refreshOnlinePlayers();

        return panel;
    }

    /**
     * 创建玩家管理左侧面板 - 在线玩家列表
     */
    private JPanel createPlayerLeftPanel() {
        JPanel leftPanel = new JPanel(new java.awt.BorderLayout());
        leftPanel.setBackground(java.awt.Color.WHITE);
        leftPanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(), "在线玩家列表",
                javax.swing.border.TitledBorder.DEFAULT_JUSTIFICATION,
                javax.swing.border.TitledBorder.DEFAULT_POSITION,
                new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14)));

        // 创建多列玩家网格面板
        this.onlinePlayersGridPanel = new PlayerGridPanel();
        JScrollPane scrollPane = new JScrollPane(this.onlinePlayersGridPanel);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_NEVER);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setPreferredSize(new java.awt.Dimension(350, 450));
        leftPanel.add(scrollPane, java.awt.BorderLayout.CENTER);

        // 底部操作按钮
        JPanel buttonPanel = new JPanel(new java.awt.FlowLayout(java.awt.FlowLayout.CENTER, 10, 5));
        buttonPanel.setBackground(java.awt.Color.WHITE);

        JButton refreshBtn = new JButton("刷新在线玩家");
        refreshBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        refreshBtn.setPreferredSize(new java.awt.Dimension(120, 30));
        refreshBtn.addActionListener(e -> refreshOnlinePlayers());
        buttonPanel.add(refreshBtn);

        JButton clearBtn = new JButton("清除选择");
        clearBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        clearBtn.setPreferredSize(new java.awt.Dimension(100, 30));
        clearBtn.addActionListener(e -> {
            if (onlinePlayersGridPanel != null) {
                onlinePlayersGridPanel.clearSelection();
            }
            if (offlinePlayerField != null) {
                offlinePlayerField.setText("");
            }
        });
        buttonPanel.add(clearBtn);

        leftPanel.add(buttonPanel, java.awt.BorderLayout.SOUTH);

        return leftPanel;
    }

    /**
     * 创建玩家管理右侧面板 - 玩家物品管理
     */
    private JPanel createPlayerRightPanel() {
        JPanel rightPanel = new JPanel(new java.awt.GridBagLayout());
        rightPanel.setBackground(java.awt.Color.WHITE);
        rightPanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(), "玩家物品管理",
                javax.swing.border.TitledBorder.DEFAULT_JUSTIFICATION,
                javax.swing.border.TitledBorder.DEFAULT_POSITION,
                new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14)));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 第一行：离线玩家名称
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        JLabel offlineLabel = new JLabel("离线玩家名称:");
        offlineLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        rightPanel.add(offlineLabel, gbc);

        gbc.gridx = 1;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        this.offlinePlayerField = new JTextField();
        this.offlinePlayerField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        this.offlinePlayerField.setPreferredSize(new java.awt.Dimension(300, 30));
        this.offlinePlayerField.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyTyped(java.awt.event.KeyEvent e) {
                if (!offlinePlayerField.getText().trim().isEmpty()) {
                    if (onlinePlayersGridPanel != null) {
                        onlinePlayersGridPanel.clearSelection();
                    }
                }
            }
        });
        rightPanel.add(this.offlinePlayerField, gbc);

        // 第二行：套装管理
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        JLabel suitLabel = new JLabel("套装类型:");
        suitLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        rightPanel.add(suitLabel, gbc);

        gbc.gridx = 1;
        gbc.gridwidth = 1;
        gbc.weightx = 1.0;
        this.suitComboBox = new JComboBox<String>();
        this.suitComboBox.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        this.suitComboBox.setPreferredSize(new java.awt.Dimension(200, 30));
        loadAvailableSuits();
        rightPanel.add(this.suitComboBox, gbc);

        gbc.gridx = 2;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        JButton sendSuitBtn = new JButton("发送套装");
        sendSuitBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        sendSuitBtn.setPreferredSize(new java.awt.Dimension(100, 30));
        sendSuitBtn.addActionListener(e -> sendSuitToPlayer());
        rightPanel.add(sendSuitBtn, gbc);

        // 第三行：淬炼石类型
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        JLabel stoneTypeLabel = new JLabel("淬炼石类型:");
        stoneTypeLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        rightPanel.add(stoneTypeLabel, gbc);

        gbc.gridx = 1;
        gbc.gridwidth = 1;
        gbc.weightx = 1.0;
        this.stoneComboBox = new JComboBox<String>();
        this.stoneComboBox
                .setModel(new DefaultComboBoxModel<String>(new String[] { "普通", "中等", "高等", "上等", "符咒", "吞噬" }));
        this.stoneComboBox.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        this.stoneComboBox.setPreferredSize(new java.awt.Dimension(200, 30));
        rightPanel.add(this.stoneComboBox, gbc);

        gbc.gridx = 2;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        JButton sendStoneBtn = new JButton("发送淬炼石");
        sendStoneBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        sendStoneBtn.setPreferredSize(new java.awt.Dimension(100, 30));
        sendStoneBtn.addActionListener(e -> sendStoneToPlayer());
        rightPanel.add(sendStoneBtn, gbc);

        // 第四行：数量设置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        // 减少上边距，让数量标签更靠近淬炼石类型
        gbc.insets = new java.awt.Insets(2, 8, 8, 8);
        JLabel amountLabel = new JLabel("数量:");
        amountLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        rightPanel.add(amountLabel, gbc);

        gbc.gridx = 1;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        this.stoneAmountField = new JTextField("1");
        this.stoneAmountField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        this.stoneAmountField.setPreferredSize(new java.awt.Dimension(300, 30));
        rightPanel.add(this.stoneAmountField, gbc);

        return rightPanel;
    }

    /**
     * 创建套装创建面板
     */
    private JPanel createSuitCreatorPanel() {
        JPanel mainPanel = new JPanel(new java.awt.BorderLayout());
        mainPanel.setBackground(java.awt.Color.WHITE);

        // 创建左右分栏面板
        JPanel splitPanel = new JPanel(new java.awt.BorderLayout());
        splitPanel.setBackground(java.awt.Color.WHITE);
        splitPanel.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));

        // 左侧面板 - 基本信息和属性配置
        JPanel leftPanel = createLeftConfigPanel();

        // 右侧面板 - 描述和特效配置
        JPanel rightPanel = createRightConfigPanel();

        // 添加到分栏面板
        splitPanel.add(leftPanel, java.awt.BorderLayout.WEST);
        splitPanel.add(rightPanel, java.awt.BorderLayout.CENTER);

        // 添加滚动支持
        JScrollPane scrollPane = new JScrollPane(splitPanel);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);

        mainPanel.add(scrollPane, java.awt.BorderLayout.CENTER);

        // 初始化删除套装列表
        loadDeleteSuitList();

        return mainPanel;
    }

    /**
     * 创建左侧配置面板
     */
    private JPanel createLeftConfigPanel() {
        JPanel leftPanel = new JPanel(new java.awt.GridBagLayout());
        leftPanel.setBackground(java.awt.Color.WHITE);
        leftPanel.setBorder(BorderFactory.createTitledBorder("基本配置"));
        leftPanel.setPreferredSize(new java.awt.Dimension(500, 600));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 套装基本信息
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        JLabel basicInfoLabel = new JLabel("套装基本信息");
        basicInfoLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        leftPanel.add(basicInfoLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("套装名称:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.suitNameField = new JTextField(20);
        leftPanel.add(this.suitNameField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("品质等级:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 0.5;
        this.qualityComboBox = new JComboBox<String>();
        this.qualityComboBox.setModel(new DefaultComboBoxModel<String>(new String[] { "1", "2", "3", "4", "5", "6" }));
        this.qualityComboBox.setSelectedIndex(5);
        leftPanel.add(this.qualityComboBox, gbc);

        // 装备名称配置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        JLabel equipLabel = new JLabel("装备名称配置");
        equipLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        leftPanel.add(equipLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("头盔:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.headNameField = new JTextField(15);
        leftPanel.add(this.headNameField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("胸甲:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.chestNameField = new JTextField(15);
        leftPanel.add(this.chestNameField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("护腿:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.legNameField = new JTextField(15);
        leftPanel.add(this.legNameField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("靴子:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.footNameField = new JTextField(15);
        leftPanel.add(this.footNameField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("剑:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.swordNameField = new JTextField(15);
        leftPanel.add(this.swordNameField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("弓:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.bowNameField = new JTextField(15);
        leftPanel.add(this.bowNameField, gbc);

        // 属性配置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        JLabel attributeLabel = new JLabel("属性配置");
        attributeLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        leftPanel.add(attributeLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("攻击:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.attackField = new JTextField("15", 12);
        leftPanel.add(this.attackField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("防御:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.defenseField = new JTextField("20", 12);
        leftPanel.add(this.defenseField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("生命:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.healthField = new JTextField("40", 12);
        leftPanel.add(this.healthField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("速度:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.speedField = new JTextField("0.1", 12);
        leftPanel.add(this.speedField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("跳跃:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.jumpField = new JTextField("1", 12);
        leftPanel.add(this.jumpField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("吸血:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.vampireField = new JTextField("10", 12);
        leftPanel.add(this.vampireField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        this.infiniteDurabilityCheckBox = new JCheckBox("无限耐久");
        this.infiniteDurabilityCheckBox.setToolTipText("启用后套装装备将拥有无限耐久");
        leftPanel.add(this.infiniteDurabilityCheckBox, gbc);

        // 宝石等级配置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        JLabel gemLabel = new JLabel("宝石等级配置");
        gemLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        leftPanel.add(gemLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("头盔宝石:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.headGemField = new JTextField("15", 12);
        leftPanel.add(this.headGemField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("胸甲宝石:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.chestGemField = new JTextField("15", 12);
        leftPanel.add(this.chestGemField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("护腿宝石:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.legGemField = new JTextField("15", 12);
        leftPanel.add(this.legGemField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("靴子宝石:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.footGemField = new JTextField("15", 12);
        leftPanel.add(this.footGemField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("剑宝石:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.swordGemField = new JTextField("18", 12);
        leftPanel.add(this.swordGemField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("弓宝石:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.bowGemField = new JTextField("16", 12);
        leftPanel.add(this.bowGemField, gbc);

        return leftPanel;
    }

    /**
     * 创建右侧配置面板
     */
    private JPanel createRightConfigPanel() {
        JPanel rightPanel = new JPanel(new java.awt.GridBagLayout());
        rightPanel.setBackground(java.awt.Color.WHITE);
        rightPanel.setBorder(BorderFactory.createTitledBorder("描述和特效配置"));
        rightPanel.setPreferredSize(new java.awt.Dimension(450, 600));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 描述配置
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        JLabel descLabel = new JLabel("套装描述");
        descLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        rightPanel.add(descLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.fill = java.awt.GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 0.3;
        this.descriptionArea = new javax.swing.JTextArea();
        this.descriptionArea.setText("§6§l自定义套装\n§e传说中的装者套装\n§a拥有强大的力量和神秘的特效");
        this.descriptionArea.setLineWrap(true);
        this.descriptionArea.setWrapStyleWord(true);
        this.descriptionArea.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        JScrollPane descScrollPane = new JScrollPane(this.descriptionArea);
        descScrollPane.setPreferredSize(new java.awt.Dimension(400, 100));
        rightPanel.add(descScrollPane, gbc);

        // 特效配置
        row++;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        gbc.weighty = 0;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        JLabel effectLabel = new JLabel("特效配置");
        effectLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        rightPanel.add(effectLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        rightPanel.add(new JLabel("特效类型:"), gbc);
        gbc.gridx = 1;
        gbc.gridwidth = 3;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        this.effectTypeComboBox = new JComboBox<String>();
        this.effectTypeComboBox.setModel(new DefaultComboBoxModel<String>(new String[] {
                "flame_basic - 基础火焰", "footstep - 脚步", "spiral_flame - 螺旋火焰",
                "rotating_halo - 旋转光环", "four_direction_spell - 四方向法术", "wings_56 - 5.6版本翅膀",
                "wings - 翅膀", "halo - 光环", "flame - 火焰", "nebula - 星云", "tornado - 龙卷风", "stars - 星星",
                "spiral - 螺旋", "sphere - 球体", "orbit - 轨道", "cube - 立方体", "helix - 螺旋桨",
                "batman - 蝙蝠侠", "popper - 爆炸螺旋", "pulse - 脉冲", "whirl - 旋涡", "whirlwind - 旋风", "invocation - 召唤法阵",
                "lightning - 雷电", "rainbow - 彩虹", "spacerift - 时空裂缝", "frost - 冰霜", "shadow - 暗影"
        }));
        rightPanel.add(this.effectTypeComboBox, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        rightPanel.add(new JLabel("颜色1:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.color1ComboBox = new JComboBox<String>();
        this.color1ComboBox.setModel(new DefaultComboBoxModel<String>(new String[] {
                "红", "橙", "黄", "绿", "蓝", "紫", "粉", "黑", "白", "灰", "银"
        }));
        rightPanel.add(this.color1ComboBox, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        rightPanel.add(new JLabel("颜色2:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.color2ComboBox = new JComboBox<String>();
        this.color2ComboBox.setModel(new DefaultComboBoxModel<String>(new String[] {
                "红", "橙", "黄", "绿", "蓝", "紫", "粉", "黑", "白", "灰", "银"
        }));
        this.color2ComboBox.setSelectedIndex(1); // 默认橙色
        rightPanel.add(this.color2ComboBox, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        rightPanel.add(new JLabel("颜色3:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.color3ComboBox = new JComboBox<String>();
        this.color3ComboBox.setModel(new DefaultComboBoxModel<String>(new String[] {
                "红", "橙", "黄", "绿", "蓝", "紫", "粉", "黑", "白", "灰", "银"
        }));
        this.color3ComboBox.setSelectedIndex(4); // 默认蓝色
        rightPanel.add(this.color3ComboBox, gbc);

        // 特效列表
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        gbc.weighty = 0;
        JLabel effectsListLabel = new JLabel("已添加特效:");
        effectsListLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        rightPanel.add(effectsListLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.fill = java.awt.GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 0.3;
        this.effectsModel = new DefaultListModel<String>();
        this.effectsList = new JList<String>(this.effectsModel);
        this.effectsList.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        JScrollPane effectsScrollPane = new JScrollPane(this.effectsList);
        effectsScrollPane.setPreferredSize(new java.awt.Dimension(400, 100));
        rightPanel.add(effectsScrollPane, gbc);

        // 特效操作按钮
        row++;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        gbc.weighty = 0;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        JButton addEffectBtn = new JButton("添加特效");
        addEffectBtn.addActionListener(e -> addEffectToList());
        rightPanel.add(addEffectBtn, gbc);

        gbc.gridx = 1;
        JButton removeEffectBtn = new JButton("删除特效");
        removeEffectBtn.addActionListener(e -> removeEffectFromList());
        rightPanel.add(removeEffectBtn, gbc);

        gbc.gridx = 2;
        gbc.gridwidth = 2;
        JButton clearEffectsBtn = new JButton("清空特效");
        clearEffectsBtn.addActionListener(e -> effectsModel.clear());
        rightPanel.add(clearEffectsBtn, gbc);

        // 套装管理按钮
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JButton saveSuitBtn = new JButton("保存套装到Suit.yml");
        saveSuitBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        saveSuitBtn.addActionListener(e -> saveSuitToConfig());
        rightPanel.add(saveSuitBtn, gbc);

        gbc.gridx = 2;
        gbc.gridwidth = 2;
        JButton clearFormBtn = new JButton("清空表单");
        clearFormBtn.addActionListener(e -> clearSuitForm());
        rightPanel.add(clearFormBtn, gbc);

        // 套装删除区域
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.fill = java.awt.GridBagConstraints.NONE;
        JLabel deleteSuitLabel = new JLabel("删除套装:");
        deleteSuitLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        rightPanel.add(deleteSuitLabel, gbc);

        gbc.gridx = 1;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        this.deleteSuitComboBox = new JComboBox<String>();
        rightPanel.add(this.deleteSuitComboBox, gbc);

        gbc.gridx = 3;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.fill = java.awt.GridBagConstraints.NONE;
        JButton deleteSuitBtn = new JButton("删除套装");
        deleteSuitBtn.addActionListener(e -> deleteSuitFromConfig());
        rightPanel.add(deleteSuitBtn, gbc);

        return rightPanel;
    }

    /**
     * 创建星级配置面板
     */
    private JPanel createStarConfigPanel() {
        JPanel mainPanel = new JPanel(new java.awt.BorderLayout());
        mainPanel.setBackground(java.awt.Color.WHITE);

        // 创建左右分栏布局
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(500);
        splitPane.setResizeWeight(0.5);
        splitPane.setBorder(null);

        // 左侧面板 - 基本配置
        JPanel leftPanel = createStarLeftPanel();
        splitPane.setLeftComponent(leftPanel);

        // 右侧面板 - 强化石配置
        JPanel rightPanel = createStarRightPanel();
        splitPane.setRightComponent(rightPanel);

        mainPanel.add(splitPane, java.awt.BorderLayout.CENTER);

        // 初始化现有星级列表
        loadExistingStarLevels();

        return mainPanel;
    }

    /**
     * 创建星级配置左侧面板
     */
    private JPanel createStarLeftPanel() {
        JPanel contentPanel = new JPanel(new java.awt.GridBagLayout());
        contentPanel.setBackground(java.awt.Color.WHITE);
        contentPanel.setBorder(BorderFactory.createTitledBorder("星级基本配置"));
        contentPanel.setPreferredSize(new java.awt.Dimension(480, 500));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 星级基本信息
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        JLabel basicInfoLabel = new JLabel("星级管理");
        basicInfoLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        contentPanel.add(basicInfoLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        contentPanel.add(new JLabel("新建星级:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.starLevelField = new JTextField(15);
        contentPanel.add(this.starLevelField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("编辑现有星级:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.existingStarLevelComboBox = new JComboBox<String>();
        this.existingStarLevelComboBox.addActionListener(e -> loadExistingStarLevelData());
        contentPanel.add(this.existingStarLevelComboBox, gbc);

        // 属性配置区域
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        JLabel attributeLabel = new JLabel("属性配置");
        attributeLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        contentPanel.add(attributeLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        contentPanel.add(new JLabel("攻击伤害:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.starAttackField = new JTextField("0", 12);
        contentPanel.add(this.starAttackField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("防御:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.starDefenseField = new JTextField("0", 12);
        contentPanel.add(this.starDefenseField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("吸血:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.starVampireField = new JTextField("0", 12);
        contentPanel.add(this.starVampireField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("跳跃:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.starJumpField = new JTextField("0", 12);
        contentPanel.add(this.starJumpField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("跌落减伤:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.starFallDamageField = new JTextField("0", 12);
        contentPanel.add(this.starFallDamageField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("反伤:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.starCounterAttackField = new JTextField("0", 12);
        contentPanel.add(this.starCounterAttackField, gbc);

        // 操作按钮
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        JButton saveBtn = new JButton("保存星级配置");
        saveBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        saveBtn.addActionListener(e -> saveStarConfiguration());
        contentPanel.add(saveBtn, gbc);

        gbc.gridx = 2;
        gbc.gridwidth = 2;
        JButton clearBtn = new JButton("清空表单");
        clearBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        clearBtn.addActionListener(e -> clearStarForm());
        contentPanel.add(clearBtn, gbc);

        return contentPanel;
    }

    /**
     * 创建星级配置右侧面板
     */
    private JPanel createStarRightPanel() {
        JPanel contentPanel = new JPanel(new java.awt.GridBagLayout());
        contentPanel.setBackground(java.awt.Color.WHITE);
        contentPanel.setBorder(BorderFactory.createTitledBorder("强化石成功几率配置"));
        contentPanel.setPreferredSize(new java.awt.Dimension(480, 500));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 强化石几率配置标题
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        JLabel probabilityLabel = new JLabel("强化石成功几率(%)");
        probabilityLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        contentPanel.add(probabilityLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        contentPanel.add(new JLabel("普通强化石:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.putongProbabilityField = new JTextField("100", 12);
        contentPanel.add(this.putongProbabilityField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("中等强化石:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.zhongdengProbabilityField = new JTextField("100", 12);
        contentPanel.add(this.zhongdengProbabilityField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("高等强化石:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.gaodengProbabilityField = new JTextField("100", 12);
        contentPanel.add(this.gaodengProbabilityField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("完美强化石:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.wanmeiProbabilityField = new JTextField("100", 12);
        contentPanel.add(this.wanmeiProbabilityField, gbc);

        return contentPanel;
    }

    /**
     * 创建特效配置面板
     */
    private JPanel createEffectConfigPanel() {
        JPanel mainPanel = new JPanel(new java.awt.BorderLayout());
        mainPanel.setBackground(java.awt.Color.WHITE);

        // 创建左右分栏布局
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(500);
        splitPane.setResizeWeight(0.5);
        splitPane.setBorder(null);

        // 左侧面板 - 星级特效管理
        JPanel leftPanel = createEffectLeftPanel();
        splitPane.setLeftComponent(leftPanel);

        // 右侧面板 - 特效创建和编辑
        JPanel rightPanel = createEffectRightPanel();
        splitPane.setRightComponent(rightPanel);

        mainPanel.add(splitPane, java.awt.BorderLayout.CENTER);

        // 初始化特效配置数据
        initializeEffectConfigData();

        return mainPanel;
    }

    /**
     * 创建特效配置左侧面板 - 星级特效管理
     */
    private JPanel createEffectLeftPanel() {
        JPanel contentPanel = new JPanel(new java.awt.GridBagLayout());
        contentPanel.setBackground(java.awt.Color.WHITE);
        contentPanel.setBorder(BorderFactory.createTitledBorder("星级特效管理"));
        contentPanel.setPreferredSize(new java.awt.Dimension(480, 600));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 星级管理标题
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        JLabel titleLabel = new JLabel("星级特效管理");
        titleLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        contentPanel.add(titleLabel, gbc);

        // 创建新星级
        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        contentPanel.add(new JLabel("创建星级:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.newStarLevelField = new JTextField(10);
        this.newStarLevelField.setToolTipText("输入新的星级数字，如：1, 2, 3...");
        contentPanel.add(this.newStarLevelField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        JButton createStarBtn = new JButton("创建");
        createStarBtn.addActionListener(e -> createNewStarLevel());
        contentPanel.add(createStarBtn, gbc);

        gbc.gridx = 3;
        gbc.weightx = 0.0;
        JButton deleteStarBtn = new JButton("删除");
        deleteStarBtn.addActionListener(e -> deleteSelectedStarLevel());
        contentPanel.add(deleteStarBtn, gbc);

        // 选择现有星级
        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        contentPanel.add(new JLabel("选择星级:"), gbc);
        gbc.gridx = 1;
        gbc.gridwidth = 3;
        gbc.weightx = 1.0;
        this.effectStarLevelComboBox = new JComboBox<String>();
        this.effectStarLevelComboBox.addActionListener(e -> loadEffectsForSelectedLevel());
        contentPanel.add(this.effectStarLevelComboBox, gbc);

        // 当前特效列表
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        gbc.weighty = 0;
        JLabel currentEffectsLabel = new JLabel("当前星级特效列表:");
        currentEffectsLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        contentPanel.add(currentEffectsLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.fill = java.awt.GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 0.4;
        this.currentEffectsModel = new DefaultListModel<String>();
        this.currentEffectsList = new JList<String>(this.currentEffectsModel);
        this.currentEffectsList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        this.currentEffectsList.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        this.currentEffectsList.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedEffectForEditing();
            }
        });
        JScrollPane currentEffectsScrollPane = new JScrollPane(this.currentEffectsList);
        currentEffectsScrollPane.setPreferredSize(new java.awt.Dimension(450, 150));
        contentPanel.add(currentEffectsScrollPane, gbc);

        // 特效操作按钮
        row++;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        gbc.weighty = 0;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JButton deleteEffectBtn = new JButton("删除选中特效");
        deleteEffectBtn.addActionListener(e -> removeSelectedEffect());
        contentPanel.add(deleteEffectBtn, gbc);

        gbc.gridx = 2;
        gbc.gridwidth = 2;
        JButton clearAllEffectsBtn = new JButton("清空所有特效");
        clearAllEffectsBtn.addActionListener(e -> clearAllEffectsForLevel());
        contentPanel.add(clearAllEffectsBtn, gbc);

        return contentPanel;
    }

    /**
     * 创建特效配置右侧面板 - 特效创建和编辑
     */
    private JPanel createEffectRightPanel() {
        JPanel contentPanel = new JPanel(new java.awt.GridBagLayout());
        contentPanel.setBackground(java.awt.Color.WHITE);
        contentPanel.setBorder(BorderFactory.createTitledBorder("特效创建和编辑"));
        contentPanel.setPreferredSize(new java.awt.Dimension(480, 600));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 特效创建标题
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        JLabel createLabel = new JLabel("特效创建和编辑");
        createLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        contentPanel.add(createLabel, gbc);

        // 特效类型选择
        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        contentPanel.add(new JLabel("特效类型:"), gbc);
        gbc.gridx = 1;
        gbc.gridwidth = 3;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        this.availableEffectTypeComboBox = new JComboBox<String>();
        this.availableEffectTypeComboBox.setModel(new DefaultComboBoxModel<String>(new String[] {
                "flame_basic - 基础火焰", "footstep - 脚步", "spiral_flame - 螺旋火焰",
                "rotating_halo - 旋转光环", "four_direction_spell - 四方向法术", "wings_56 - 5.6版本翅膀",
                "wings - 翅膀", "halo - 光环", "flame - 火焰", "nebula - 星云", "tornado - 龙卷风", "stars - 星星",
                "spiral - 螺旋", "sphere - 球体", "orbit - 轨道", "cube - 立方体", "helix - 螺旋桨",
                "batman - 蝙蝠侠", "popper - 爆炸螺旋", "pulse - 脉冲", "whirl - 旋涡", "whirlwind - 旋风",
                "invocation - 召唤法阵", "lightning - 雷电", "rainbow - 彩虹", "spacerift - 时空裂缝",
                "frost - 冰霜", "shadow - 暗影"
        }));
        contentPanel.add(this.availableEffectTypeComboBox, gbc);

        // 特效启用状态
        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.fill = java.awt.GridBagConstraints.NONE;
        this.effectEnabledCheckBox = new JCheckBox("启用特效");
        this.effectEnabledCheckBox.setSelected(true);
        contentPanel.add(this.effectEnabledCheckBox, gbc);

        gbc.gridx = 1;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("叠加特效:"), gbc);
        gbc.gridx = 2;
        gbc.weightx = 0.0;
        JCheckBox stackingCheckBox = new JCheckBox("允许叠加");
        stackingCheckBox.setSelected(true);
        contentPanel.add(stackingCheckBox, gbc);

        // 颜色配置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        JLabel colorLabel = new JLabel("颜色配置 (支持自定义RGB值)");
        colorLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        contentPanel.add(colorLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        contentPanel.add(new JLabel("颜色1:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.effectColor1ComboBox = new JComboBox<String>();
        this.effectColor1ComboBox.setModel(new DefaultComboBoxModel<String>(new String[] {
                "红 (255,0,0)", "橙 (255,165,0)", "黄 (255,255,0)", "绿 (0,255,0)", "蓝 (0,0,255)",
                "紫 (128,0,128)", "粉 (255,192,203)", "黑 (0,0,0)", "白 (255,255,255)", "灰 (128,128,128)",
                "银 (192,192,192)", "金 (255,215,0)", "青 (0,255,255)", "自定义..."
        }));
        contentPanel.add(this.effectColor1ComboBox, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("颜色2:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.effectColor2ComboBox = new JComboBox<String>();
        this.effectColor2ComboBox.setModel(new DefaultComboBoxModel<String>(new String[] {
                "红 (255,0,0)", "橙 (255,165,0)", "黄 (255,255,0)", "绿 (0,255,0)", "蓝 (0,0,255)",
                "紫 (128,0,128)", "粉 (255,192,203)", "黑 (0,0,0)", "白 (255,255,255)", "灰 (128,128,128)",
                "银 (192,192,192)", "金 (255,215,0)", "青 (0,255,255)", "自定义..."
        }));
        this.effectColor2ComboBox.setSelectedIndex(1); // 默认橙色
        contentPanel.add(this.effectColor2ComboBox, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("颜色3:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.effectColor3ComboBox = new JComboBox<String>();
        this.effectColor3ComboBox.setModel(new DefaultComboBoxModel<String>(new String[] {
                "红 (255,0,0)", "橙 (255,165,0)", "黄 (255,255,0)", "绿 (0,255,0)", "蓝 (0,0,255)",
                "紫 (128,0,128)", "粉 (255,192,203)", "黑 (0,0,0)", "白 (255,255,255)", "灰 (128,128,128)",
                "银 (192,192,192)", "金 (255,215,0)", "青 (0,255,255)", "自定义..."
        }));
        this.effectColor3ComboBox.setSelectedIndex(4); // 默认蓝色
        contentPanel.add(this.effectColor3ComboBox, gbc);

        // 操作按钮
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        JButton addEffectBtn = new JButton("添加特效到当前星级");
        addEffectBtn.addActionListener(e -> addEffectToCurrentLevel());
        contentPanel.add(addEffectBtn, gbc);

        gbc.gridx = 2;
        gbc.gridwidth = 2;
        JButton saveAllBtn = new JButton("保存所有特效配置");
        saveAllBtn.addActionListener(e -> saveAllEffectConfigurations());
        contentPanel.add(saveAllBtn, gbc);

        // 状态标签
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.anchor = java.awt.GridBagConstraints.CENTER;
        gbc.fill = java.awt.GridBagConstraints.NONE;
        this.statusLabel = new JLabel("就绪");
        this.statusLabel.setForeground(new java.awt.Color(0, 128, 0));
        contentPanel.add(this.statusLabel, gbc);

        return contentPanel;
    }

    /**
     * 创建符咒管理面板
     */
    private JPanel createCharmManagementPanel() {
        JPanel mainPanel = new JPanel(new java.awt.BorderLayout());
        mainPanel.setBackground(java.awt.Color.WHITE);

        // 创建可调节大小的分割面板 - 与玩家管理保持一致
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(400); // 与玩家管理一致的初始分割位置
        splitPane.setResizeWeight(0.4); // 与玩家管理一致的左侧权重40%
        splitPane.setBorder(null);
        splitPane.setBackground(java.awt.Color.WHITE);

        // 左侧面板 - 在线玩家选择
        JPanel leftPanel = createCharmLeftPanel();
        splitPane.setLeftComponent(leftPanel);

        // 右侧面板 - 符咒配置和操作
        JPanel rightPanel = createCharmRightPanel();
        splitPane.setRightComponent(rightPanel);

        mainPanel.add(splitPane, java.awt.BorderLayout.CENTER);

        // 初始化符咒数据
        initializeCharmData();

        return mainPanel;
    }

    /**
     * 创建符咒管理左侧面板 - 在线玩家列表
     */
    private JPanel createCharmLeftPanel() {
        JPanel leftPanel = new JPanel(new java.awt.BorderLayout());
        leftPanel.setBackground(java.awt.Color.WHITE);
        leftPanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(), "在线玩家列表",
                javax.swing.border.TitledBorder.DEFAULT_JUSTIFICATION,
                javax.swing.border.TitledBorder.DEFAULT_POSITION,
                new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14)));

        // 创建符咒专用的玩家网格面板
        this.charmPlayersGridPanel = new PlayerGridPanel();
        JScrollPane scrollPane = new JScrollPane(this.charmPlayersGridPanel);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_NEVER);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setPreferredSize(new java.awt.Dimension(350, 450));
        leftPanel.add(scrollPane, java.awt.BorderLayout.CENTER);

        // 底部操作按钮
        JPanel buttonPanel = new JPanel(new java.awt.FlowLayout(java.awt.FlowLayout.CENTER, 10, 5));
        buttonPanel.setBackground(java.awt.Color.WHITE);

        JButton refreshBtn = new JButton("刷新在线玩家");
        refreshBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        refreshBtn.setPreferredSize(new java.awt.Dimension(120, 30));
        refreshBtn.addActionListener(e -> refreshCharmOnlinePlayers());
        buttonPanel.add(refreshBtn);

        JButton clearBtn = new JButton("清除选择");
        clearBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        clearBtn.setPreferredSize(new java.awt.Dimension(100, 30));
        clearBtn.addActionListener(e -> {
            if (charmPlayersGridPanel != null) {
                charmPlayersGridPanel.clearSelection();
            }
            if (charmTargetPlayerField != null) {
                charmTargetPlayerField.setText("");
            }
        });
        buttonPanel.add(clearBtn);

        leftPanel.add(buttonPanel, java.awt.BorderLayout.SOUTH);

        return leftPanel;
    }

    /**
     * 创建符咒管理右侧面板 - 符咒配置和操作
     */
    private JPanel createCharmRightPanel() {
        JPanel contentPanel = new JPanel(new java.awt.GridBagLayout());
        contentPanel.setBackground(java.awt.Color.WHITE);
        contentPanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(), "符咒配置",
                javax.swing.border.TitledBorder.DEFAULT_JUSTIFICATION,
                javax.swing.border.TitledBorder.DEFAULT_POSITION,
                new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14)));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 离线玩家输入
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        JLabel offlineLabel = new JLabel("离线玩家名称:");
        offlineLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        contentPanel.add(offlineLabel, gbc);

        gbc.gridx = 1;
        gbc.gridwidth = 1;
        gbc.weightx = 1.0;
        this.charmTargetPlayerField = new JTextField();
        this.charmTargetPlayerField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        this.charmTargetPlayerField.setPreferredSize(new java.awt.Dimension(200, 30));
        this.charmTargetPlayerField.setToolTipText("输入离线玩家名称，或从左侧选择在线玩家");
        contentPanel.add(this.charmTargetPlayerField, gbc);

        // 符咒类型配置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        JLabel typeLabel = new JLabel("符咒类型配置");
        typeLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        contentPanel.add(typeLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        contentPanel.add(new JLabel("符咒类型:"), gbc);

        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.charmTypeComboBox = new JComboBox<String>();
        this.charmTypeComboBox.addItem("淬炼直升棒");
        this.charmTypeComboBox.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        this.charmTypeComboBox.setToolTipText("目前只支持淬炼直升棒，会自动检测配置的总星级数");
        contentPanel.add(this.charmTypeComboBox, gbc);

        // 符咒等级配置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("目标星级:"), gbc);

        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.charmLevelComboBox = new JComboBox<String>();
        this.charmLevelComboBox.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        this.charmLevelComboBox.setToolTipText("选择符咒升级到的目标星级");
        contentPanel.add(this.charmLevelComboBox, gbc);

        // 数量配置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("数量:"), gbc);

        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.charmQuantityField = new JTextField("1", 10);
        this.charmQuantityField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        this.charmQuantityField.setToolTipText("输入要给予的符咒数量");
        contentPanel.add(this.charmQuantityField, gbc);

        // 操作按钮
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        JButton giveCharmBtn = new JButton("给予符咒");
        giveCharmBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        giveCharmBtn.addActionListener(e -> giveCharmToPlayer());
        contentPanel.add(giveCharmBtn, gbc);

        // 说明文本
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        gbc.weighty = 1.0;
        gbc.fill = java.awt.GridBagConstraints.BOTH;

        javax.swing.JTextArea infoArea = new javax.swing.JTextArea();
        infoArea.setText("符咒说明:\n\n" +
                "• 淬炼直升棒: 可以直接将装备升级到指定星级\n" +
                "• 自动检测: 系统会自动检测配置文件中的最大星级\n" +
                "• 实时更新: 当星级配置更新时，符咒等级选项会自动刷新\n" +
                "• 支持离线: 可以给离线玩家发送符咒");
        infoArea.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        infoArea.setEditable(false);
        infoArea.setBackground(new java.awt.Color(245, 245, 245));
        infoArea.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        infoArea.setLineWrap(true);
        infoArea.setWrapStyleWord(true);

        JScrollPane infoScrollPane = new JScrollPane(infoArea);
        infoScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        infoScrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
        contentPanel.add(infoScrollPane, gbc);

        return contentPanel;
    }

    /**
     * 创建服务器监控面板
     */
    private JPanel createServerMonitorPanel() {
        JPanel mainPanel = new JPanel(new java.awt.BorderLayout());
        mainPanel.setBackground(java.awt.Color.WHITE);

        // 创建左右分栏布局
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(500);
        splitPane.setResizeWeight(0.5);
        splitPane.setBorder(null);

        // 左侧面板 - 实时监控
        JPanel leftPanel = createMonitorLeftPanel();
        splitPane.setLeftComponent(leftPanel);

        // 右侧面板 - 统计信息
        JPanel rightPanel = createMonitorRightPanel();
        splitPane.setRightComponent(rightPanel);

        mainPanel.add(splitPane, java.awt.BorderLayout.CENTER);

        return mainPanel;
    }

    /**
     * 创建监控左侧面板 - 实时监控
     */
    private JPanel createMonitorLeftPanel() {
        JPanel leftPanel = new JPanel(new java.awt.GridBagLayout());
        leftPanel.setBackground(java.awt.Color.WHITE);
        leftPanel.setBorder(BorderFactory.createTitledBorder("实时监控"));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // TPS监控
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("当前TPS:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JLabel tpsLabel = new JLabel("20.0");
        tpsLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        tpsLabel.setForeground(new java.awt.Color(0, 128, 0));
        leftPanel.add(tpsLabel, gbc);

        // 在线玩家数
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("在线玩家:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JLabel playersLabel = new JLabel("0/100");
        playersLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        leftPanel.add(playersLabel, gbc);

        // 内存使用
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("内存使用:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JLabel memoryLabel = new JLabel("512MB/2GB");
        memoryLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        leftPanel.add(memoryLabel, gbc);

        // 插件状态
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("插件状态:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JLabel statusLabel = new JLabel("运行正常");
        statusLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        statusLabel.setForeground(new java.awt.Color(0, 128, 0));
        leftPanel.add(statusLabel, gbc);

        // 刷新按钮
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JButton refreshBtn = new JButton("刷新监控数据");
        refreshBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        refreshBtn.addActionListener(e -> updateMonitorData(tpsLabel, playersLabel, memoryLabel, statusLabel));
        leftPanel.add(refreshBtn, gbc);

        // 保存监控标签的引用，用于后续更新
        this.monitoringTpsLabel = tpsLabel;
        this.monitoringPlayersLabel = playersLabel;
        this.monitoringMemoryLabel = memoryLabel;
        this.monitoringStatusLabel = statusLabel;

        // 初始更新一次监控数据
        updateMonitorData(tpsLabel, playersLabel, memoryLabel, statusLabel);

        // 启动套装检测任务
        startSuitDetectionTask();

        return leftPanel;
    }

    /**
     * 创建监控右侧面板 - 统计信息
     */
    private JPanel createMonitorRightPanel() {
        JPanel rightPanel = new JPanel(new java.awt.BorderLayout());
        rightPanel.setBackground(java.awt.Color.WHITE);
        rightPanel.setBorder(BorderFactory.createTitledBorder("详细统计信息"));

        // 创建标签页面板
        JTabbedPane statsTabPane = new JTabbedPane(JTabbedPane.TOP);
        statsTabPane.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));

        // 套装统计标签页
        JPanel suitStatsPanel = createSuitStatsPanel();
        statsTabPane.addTab("套装统计", suitStatsPanel);

        // 淬炼石统计标签页
        JPanel stoneStatsPanel = createStoneStatsPanel();
        statsTabPane.addTab("淬炼石统计", stoneStatsPanel);

        // 玩家活跃度统计标签页
        JPanel playerStatsPanel = createPlayerStatsPanel();
        statsTabPane.addTab("玩家统计", playerStatsPanel);

        // 性能统计标签页
        JPanel performanceStatsPanel = createPerformanceStatsPanel();
        statsTabPane.addTab("性能统计", performanceStatsPanel);

        rightPanel.add(statsTabPane, java.awt.BorderLayout.CENTER);

        // 底部按钮面板
        JPanel bottomPanel = new JPanel(new java.awt.FlowLayout());
        bottomPanel.setBackground(java.awt.Color.WHITE);

        JButton refreshStatsBtn = new JButton("刷新统计数据");
        refreshStatsBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        refreshStatsBtn.addActionListener(e -> refreshAllStats());
        bottomPanel.add(refreshStatsBtn);

        JButton clearLocalDataBtn = new JButton("清空本地记录");
        clearLocalDataBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        clearLocalDataBtn.setForeground(new java.awt.Color(255, 140, 0));
        clearLocalDataBtn.addActionListener(e -> clearLocalStorageData());
        bottomPanel.add(clearLocalDataBtn);

        JButton clearDataBtn = new JButton("清空数据");
        clearDataBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        clearDataBtn.setForeground(new java.awt.Color(255, 0, 0));
        clearDataBtn.addActionListener(e -> clearAllStats());
        bottomPanel.add(clearDataBtn);

        JButton autoUpdateBtn = new JButton("立即检测套装");
        autoUpdateBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        autoUpdateBtn.setForeground(new java.awt.Color(0, 128, 0));
        autoUpdateBtn.addActionListener(e -> {
            updateSuitUsageStats();
            refreshStatsDisplay();
            JOptionPane.showMessageDialog(this, "套装统计已更新！", "提示", JOptionPane.INFORMATION_MESSAGE);
        });
        bottomPanel.add(autoUpdateBtn);

        rightPanel.add(bottomPanel, java.awt.BorderLayout.SOUTH);

        // 不加载任何测试数据，所有统计数据默认为0
        // 用户可以通过"刷新统计数据"按钮加载真实数据

        return rightPanel;
    }

    /**
     * 创建套装统计面板
     */
    private JPanel createSuitStatsPanel() {
        JPanel panel = new JPanel(new java.awt.GridBagLayout());
        panel.setBackground(java.awt.Color.WHITE);

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(5, 5, 5, 5);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 套装总数
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("已创建套装总数:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        totalSuitsLabel = new JLabel("0");
        totalSuitsLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        panel.add(totalSuitsLabel, gbc);

        // 最受欢迎套装
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("最受欢迎套装:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        popularSuitLabel = new JLabel("暂无数据");
        popularSuitLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        panel.add(popularSuitLabel, gbc);

        // 套装使用率排行
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel rankingLabel = new JLabel("套装使用排行榜:");
        rankingLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        panel.add(rankingLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = java.awt.GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 1.0;

        suitRankingModel = new DefaultListModel<>();
        JList<String> suitRankingList = new JList<>(suitRankingModel);
        suitRankingList.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        // 设置自定义渲染器来处理颜色代码
        suitRankingList.setCellRenderer(new ColorCodeListCellRenderer());
        JScrollPane suitScrollPane = new JScrollPane(suitRankingList);
        suitScrollPane.setPreferredSize(new java.awt.Dimension(200, 150));
        panel.add(suitScrollPane, gbc);

        return panel;
    }

    /**
     * 创建淬炼石统计面板
     */
    private JPanel createStoneStatsPanel() {
        JPanel panel = new JPanel(new java.awt.GridBagLayout());
        panel.setBackground(java.awt.Color.WHITE);

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(5, 5, 5, 5);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 今日消耗统计
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel todayLabel = new JLabel("今日淬炼石消耗:");
        todayLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        panel.add(todayLabel, gbc);

        String[] stoneTypes = { "普通", "中等", "高等", "上等", "符咒", "吞噬" };
        for (String type : stoneTypes) {
            row++;
            gbc.gridwidth = 1;
            gbc.gridx = 0;
            gbc.gridy = row;
            gbc.weightx = 0.0;
            panel.add(new JLabel(type + "淬炼石:"), gbc);
            gbc.gridx = 1;
            gbc.weightx = 1.0;
            JLabel countLabel = new JLabel("0个");
            countLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 11));
            panel.add(countLabel, gbc);

            // 保存标签引用以便后续更新
            stoneCountLabels.put(type, countLabel);
        }

        // 成功率统计
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel successLabel = new JLabel("淬炼成功率统计:");
        successLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        panel.add(successLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        panel.add(new JLabel("今日平均成功率:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        avgSuccessLabel = new JLabel("75.5%");
        avgSuccessLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 11));
        avgSuccessLabel.setForeground(new java.awt.Color(0, 128, 0));
        panel.add(avgSuccessLabel, gbc);

        return panel;
    }

    /**
     * 创建玩家统计面板
     */
    private JPanel createPlayerStatsPanel() {
        JPanel panel = new JPanel(new java.awt.GridBagLayout());
        panel.setBackground(java.awt.Color.WHITE);

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(5, 5, 5, 5);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 玩家总数统计
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("注册玩家总数:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        totalPlayersLabel = new JLabel("0");
        totalPlayersLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        panel.add(totalPlayersLabel, gbc);

        // 今日活跃玩家
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("今日活跃玩家:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        activePlayersLabel = new JLabel("0");
        activePlayersLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        panel.add(activePlayersLabel, gbc);

        // 新玩家统计
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("今日新玩家:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        newPlayersLabel = new JLabel("0");
        newPlayersLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        panel.add(newPlayersLabel, gbc);

        // 平均在线时长
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("平均在线时长:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        avgOnlineLabel = new JLabel("2小时30分钟");
        avgOnlineLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        panel.add(avgOnlineLabel, gbc);

        // 最活跃玩家排行
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel activeRankLabel = new JLabel("★ 最活跃玩家排行榜 ★");
        activeRankLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        activeRankLabel.setForeground(new java.awt.Color(255, 215, 0)); // 金色
        activeRankLabel.setHorizontalAlignment(JLabel.CENTER);
        panel.add(activeRankLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = java.awt.GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 1.0;

        // 创建包含前三宝座和普通排名的完整面板
        JPanel rankingContainer = new JPanel(new java.awt.BorderLayout());
        rankingContainer.setBackground(java.awt.Color.WHITE);

        // 前三宝座面板
        topThreePanel = new TopThreePanel();
        topThreePanel.setPreferredSize(new java.awt.Dimension(280, 150)); // 增加高度到150
        rankingContainer.add(topThreePanel, java.awt.BorderLayout.NORTH);

        // 普通排名面板
        normalRankingPanel = new NormalRankingPanel();
        JScrollPane normalScrollPane = new JScrollPane(normalRankingPanel);
        normalScrollPane.setPreferredSize(new java.awt.Dimension(280, 150));
        normalScrollPane.setBorder(BorderFactory.createTitledBorder("其他排名"));
        rankingContainer.add(normalScrollPane, java.awt.BorderLayout.CENTER);

        panel.add(rankingContainer, gbc);

        return panel;
    }

    /**
     * 创建性能统计面板
     */
    private JPanel createPerformanceStatsPanel() {
        JPanel panel = new JPanel(new java.awt.GridBagLayout());
        panel.setBackground(java.awt.Color.WHITE);

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(5, 5, 5, 5);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // TPS历史统计
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("24小时平均TPS:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        avgTpsLabel = new JLabel("19.8");
        avgTpsLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        avgTpsLabel.setForeground(new java.awt.Color(0, 128, 0));
        panel.add(avgTpsLabel, gbc);

        // 最低TPS
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("24小时最低TPS:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        minTpsLabel = new JLabel("15.2");
        minTpsLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        minTpsLabel.setForeground(new java.awt.Color(255, 165, 0));
        panel.add(minTpsLabel, gbc);

        // 服务器运行时间
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("服务器运行时间:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        uptimeLabel = new JLabel("3天12小时45分钟");
        uptimeLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        panel.add(uptimeLabel, gbc);

        // 插件加载数量
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("已加载插件数量:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        pluginCountLabel = new JLabel(String.valueOf(Bukkit.getPluginManager().getPlugins().length));
        pluginCountLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        panel.add(pluginCountLabel, gbc);

        // 世界数量
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("世界数量:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        worldCountLabel = new JLabel(String.valueOf(Bukkit.getWorlds().size()));
        worldCountLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        panel.add(worldCountLabel, gbc);

        // CPU使用率（模拟）
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("CPU使用率:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        cpuLabel = new JLabel("45%");
        cpuLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        cpuLabel.setForeground(new java.awt.Color(0, 128, 0));
        panel.add(cpuLabel, gbc);

        return panel;
    }

    // 统计数据存储
    private java.util.Map<String, Integer> suitUsageStats = new java.util.HashMap<>();
    private java.util.Map<String, Integer> stoneUsageStats = new java.util.HashMap<>();
    private java.util.List<String> operationLogs = new java.util.ArrayList<>();
    private double[] tpsHistory = new double[288]; // 24小时，每5分钟一个点
    private int tpsHistoryIndex = 0;

    // 文件监控
    private java.nio.file.WatchService probabilityFileWatcher;
    private java.util.concurrent.ScheduledExecutorService fileWatcherExecutor;
    private long lastProbabilityFileModified = 0;

    // 监控数据存储文件路径
    private static final String MONITORING_DATA_FILE = "plugins/Cuilian/monitoring_data.yml";

    // 自动保存任务
    private javax.swing.Timer autoSaveTimer;

    // 自动更新监控数据任务
    private javax.swing.Timer autoUpdateTimer;

    // 监控面板标签引用
    private JLabel monitoringTpsLabel;
    private JLabel monitoringPlayersLabel;
    private JLabel monitoringMemoryLabel;
    private JLabel monitoringStatusLabel;

    /**
     * 刷新所有统计数据
     */
    private void refreshAllStats() {
        try {
            // 不重新加载保存的数据，只更新当前真实数据

            // 更新套装使用统计
            updateSuitUsageStats();

            // 更新淬炼石使用统计
            updateStoneUsageStats();

            // 更新玩家活跃度统计（只获取当前真实数据）
            updateCurrentPlayerStats();

            // 更新性能统计
            updatePerformanceStats();

            // 刷新界面显示
            refreshStatsDisplay();

            // 自动保存监控数据
            saveMonitoringData();

            JOptionPane.showMessageDialog(this, "统计数据已刷新！", "提示", JOptionPane.INFORMATION_MESSAGE);
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "刷新统计数据失败: " + e.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
            e.printStackTrace();
        }
    }

    /**
     * 更新套装使用统计
     */
    private void updateSuitUsageStats() {
        try {
            suitUsageStats.clear();

            // 遍历所有在线玩家，统计套装使用情况
            for (Player player : Bukkit.getOnlinePlayers()) {
                // 检查玩家当前装备的套装
                String currentSuit = getCurrentPlayerSuit(player);
                if (currentSuit != null && !currentSuit.isEmpty()) {
                    suitUsageStats.put(currentSuit, suitUsageStats.getOrDefault(currentSuit, 0) + 1);
                }
            }

            // 如果有插件实例，也可以从配置文件或数据库获取历史数据
            if (Cuilian.getInstance() != null) {
                // 这里可以添加从数据库或配置文件读取历史统计的逻辑
                addHistoricalSuitStats();
            }

        } catch (Exception e) {
            System.err.println("更新套装统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取玩家当前套装
     */
    private String getCurrentPlayerSuit(Player player) {
        try {
            // 检查玩家装备栏
            org.bukkit.inventory.ItemStack helmet = player.getInventory().getHelmet();
            org.bukkit.inventory.ItemStack chestplate = player.getInventory().getChestplate();
            org.bukkit.inventory.ItemStack leggings = player.getInventory().getLeggings();
            org.bukkit.inventory.ItemStack boots = player.getInventory().getBoots();

            // 检查是否有套装标识
            if (helmet != null && helmet.hasItemMeta() && helmet.getItemMeta().hasLore()) {
                java.util.List<String> lore = helmet.getItemMeta().getLore();
                for (String line : lore) {
                    if (line.contains("套装:") || line.contains("套装：")) {
                        // 提取套装名称并移除所有颜色代码
                        String suitName = line.replace("套装:", "").replace("套装：", "").trim();
                        // 移除颜色代码，只保留纯文本套装名称
                        return removeColorCodes(suitName);
                    }
                }
            }

            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 添加历史套装统计数据
     */
    private void addHistoricalSuitStats() {
        try {
            // 只有在没有真实数据时才添加模拟数据
            // 这里可以从数据库或配置文件读取真实的历史统计数据
            // 暂时不添加模拟数据，避免启动时显示虚假信息

            // 如果需要测试，可以取消注释以下代码：
            /*
             * suitUsageStats.put("神武套装", suitUsageStats.getOrDefault("神武套装", 0) + 15);
             * suitUsageStats.put("龙鳞套装", suitUsageStats.getOrDefault("龙鳞套装", 0) + 12);
             * suitUsageStats.put("凤羽套装", suitUsageStats.getOrDefault("凤羽套装", 0) + 8);
             * suitUsageStats.put("雷神套装", suitUsageStats.getOrDefault("雷神套装", 0) + 6);
             * suitUsageStats.put("冰霜套装", suitUsageStats.getOrDefault("冰霜套装", 0) + 4);
             */
        } catch (Exception e) {
            System.err.println("添加历史套装统计失败: " + e.getMessage());
        }
    }

    /**
     * 更新淬炼石使用统计
     */
    private void updateStoneUsageStats() {
        try {
            stoneUsageStats.clear();

            // 从真实数据源获取淬炼石使用统计
            if (Cuilian.getInstance() != null) {
                // 从插件数据中获取真实的淬炼石使用统计
                updateRealStoneStats();
            }

            // 如果没有真实数据，保持为空，不显示虚假信息
            // 管理员可以通过"刷新统计数据"按钮手动更新

        } catch (Exception e) {
            System.err.println("更新淬炼石统计失败: " + e.getMessage());
        }
    }

    /**
     * 更新真实的淬炼石统计
     */
    private void updateRealStoneStats() {
        try {
            // 从Cuilian插件获取今日淬炼石使用统计
            java.util.HashMap<String, Integer> todayUsage = Cuilian.getTodayStoneUsage();

            // 只有当有真实使用数据时才更新
            if (todayUsage != null && !todayUsage.isEmpty()) {
                // 检查是否有非零的使用数据
                boolean hasRealUsage = todayUsage.values().stream().anyMatch(count -> count > 0);
                if (hasRealUsage) {
                    stoneUsageStats.clear();
                    stoneUsageStats.putAll(todayUsage);
                }
            }

            // 如果没有真实使用数据，确保统计为0
            if (stoneUsageStats.isEmpty()) {
                stoneUsageStats.put("普通", 0);
                stoneUsageStats.put("中等", 0);
                stoneUsageStats.put("高等", 0);
                stoneUsageStats.put("上等", 0);
                stoneUsageStats.put("符咒", 0);
                stoneUsageStats.put("吞噬", 0);
            }

        } catch (Exception e) {
            System.err.println("更新真实淬炼石统计失败: " + e.getMessage());
            // 出错时确保统计为0
            stoneUsageStats.clear();
            stoneUsageStats.put("普通", 0);
            stoneUsageStats.put("中等", 0);
            stoneUsageStats.put("高等", 0);
            stoneUsageStats.put("上等", 0);
            stoneUsageStats.put("符咒", 0);
            stoneUsageStats.put("吞噬", 0);
        }
    }

    /**
     * 更新玩家活跃度统计
     */
    private void updatePlayerActivityStats() {
        try {
            // 获取真实的服务器玩家统计
            int onlinePlayers = Bukkit.getOnlinePlayers().size();

            // 获取真实的注册玩家总数
            int totalPlayers = getRealTotalRegisteredPlayers();

            // 获取真实的今日活跃玩家数（当前在线玩家数）
            int todayActive = onlinePlayers;

            // 获取真实的今日新玩家数（基于玩家首次登录时间）
            int todayNew = getRealTodayNewPlayers();

            // 获取真实的平均在线时长（基于玩家统计数据）
            int avgOnlineTime = getRealAverageOnlineTime();

            // 存储真实数据
            playerStats.put("totalPlayers", totalPlayers);
            playerStats.put("todayActive", todayActive);
            playerStats.put("todayNew", todayNew);
            playerStats.put("avgOnlineTime", avgOnlineTime);

        } catch (Exception e) {
            System.err.println("更新玩家活跃度统计失败: " + e.getMessage());
        }
    }

    /**
     * 更新当前玩家统计（不加载保存的数据）
     */
    private void updateCurrentPlayerStats() {
        try {
            // 清空现有数据，只获取当前真实数据
            playerStats.clear();

            // 获取真实的服务器玩家统计
            int onlinePlayers = Bukkit.getOnlinePlayers().size();

            // 获取真实的注册玩家总数
            int totalPlayers = getRealTotalRegisteredPlayers();

            // 获取真实的今日活跃玩家数（当前在线玩家数）
            int todayActive = onlinePlayers;

            // 获取真实的今日新玩家数（基于玩家首次登录时间）
            int todayNew = getRealTodayNewPlayers();

            // 获取真实的平均在线时长（基于玩家统计数据）
            int avgOnlineTime = getRealAverageOnlineTime();

            // 存储真实数据
            playerStats.put("totalPlayers", totalPlayers);
            playerStats.put("todayActive", todayActive);
            playerStats.put("todayNew", todayNew);
            playerStats.put("avgOnlineTime", avgOnlineTime);

        } catch (Exception e) {
            System.err.println("更新当前玩家统计失败: " + e.getMessage());
        }
    }

    // 玩家统计数据存储
    private java.util.Map<String, Object> playerStats = new java.util.HashMap<>();

    /**
     * 获取真实的注册玩家总数
     */
    private int getRealTotalRegisteredPlayers() {
        try {
            // 从Bukkit获取离线玩家数量（包括所有曾经登录过的玩家）
            org.bukkit.OfflinePlayer[] offlinePlayers = Bukkit.getOfflinePlayers();
            return offlinePlayers.length;
        } catch (Exception e) {
            System.err.println("获取注册玩家总数失败: " + e.getMessage());
            return 0; // 默认值改为0，避免显示虚假数据
        }
    }

    /**
     * 获取真实的今日新玩家数量
     */
    private int getRealTodayNewPlayers() {
        try {
            // 获取今天的开始时间（00:00:00）
            java.util.Calendar today = java.util.Calendar.getInstance();
            today.set(java.util.Calendar.HOUR_OF_DAY, 0);
            today.set(java.util.Calendar.MINUTE, 0);
            today.set(java.util.Calendar.SECOND, 0);
            today.set(java.util.Calendar.MILLISECOND, 0);
            long todayStart = today.getTimeInMillis();

            int newPlayersToday = 0;

            // 检查所有离线玩家的首次登录时间
            for (org.bukkit.OfflinePlayer offlinePlayer : Bukkit.getOfflinePlayers()) {
                if (offlinePlayer.hasPlayedBefore()) {
                    long firstPlayed = offlinePlayer.getFirstPlayed();
                    if (firstPlayed >= todayStart) {
                        newPlayersToday++;
                    }
                }
            }

            return newPlayersToday;
        } catch (Exception e) {
            System.err.println("获取今日新玩家数量失败: " + e.getMessage());
            return 0;
        }
    }

    /**
     * 获取真实的平均在线时长
     */
    private int getRealAverageOnlineTime() {
        try {
            // 计算所有玩家（包括离线玩家）的平均总游戏时长
            long totalPlayTime = 0;
            int playerCount = 0;

            // 遍历所有离线玩家
            for (org.bukkit.OfflinePlayer offlinePlayer : Bukkit.getOfflinePlayers()) {
                if (offlinePlayer.hasPlayedBefore()) {
                    try {
                        // 获取玩家的总游戏时间（从首次登录到最后登录的时间差）
                        long firstPlayed = offlinePlayer.getFirstPlayed();
                        long lastPlayed = offlinePlayer.getLastPlayed();

                        // 如果玩家在线，使用当前时间
                        if (offlinePlayer.isOnline()) {
                            lastPlayed = System.currentTimeMillis();
                        }

                        // 计算总时间跨度
                        long totalTimeSpan = lastPlayed - firstPlayed;

                        // 估算实际游戏时间（假设玩家在总时间跨度中有10%的时间在线）
                        long estimatedPlayTime = totalTimeSpan / 10;

                        // 限制最小值（至少5分钟）和最大值（最多24小时）
                        estimatedPlayTime = Math.max(5 * 60 * 1000, estimatedPlayTime); // 最少5分钟
                        estimatedPlayTime = Math.min(24 * 60 * 60 * 1000, estimatedPlayTime); // 最多24小时

                        totalPlayTime += estimatedPlayTime;
                        playerCount++;
                    } catch (Exception e) {
                        // 跳过有问题的玩家
                        continue;
                    }
                }
            }

            if (playerCount > 0) {
                // 返回平均游戏时长（分钟）
                long avgPlayTimeMillis = totalPlayTime / playerCount;
                return (int) (avgPlayTimeMillis / (1000 * 60)); // 转换为分钟
            } else {
                return 0; // 没有玩家数据
            }
        } catch (Exception e) {
            System.err.println("获取平均在线时长失败: " + e.getMessage());
            return 0;
        }
    }

    // 存储玩家登录时间的Map
    private static java.util.Map<String, Long> playerLoginTimes = new java.util.HashMap<>();

    /**
     * 获取玩家当前会话的在线时长
     */
    private long getCurrentSessionTime(Player player) {
        try {
            String playerName = player.getName();
            long currentTime = System.currentTimeMillis();

            // 从Map中获取玩家的登录时间
            Long loginTime = playerLoginTimes.get(playerName);
            if (loginTime != null) {
                // 计算本次会话的在线时长
                return currentTime - loginTime;
            } else {
                // 如果没有记录，使用玩家的最后登录时间作为估算
                org.bukkit.OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(playerName);
                if (offlinePlayer.hasPlayedBefore()) {
                    long lastPlayed = offlinePlayer.getLastPlayed();
                    // 估算：如果最后游戏时间距离现在不超过1小时，认为是本次会话
                    if (currentTime - lastPlayed < 3600000) { // 1小时 = 3600000毫秒
                        return currentTime - lastPlayed;
                    }
                }
                // 默认返回30分钟（新玩家或无法确定的情况）
                return 30 * 60 * 1000; // 30分钟
            }
        } catch (Exception e) {
            // 如果获取失败，返回默认值30分钟
            return 30 * 60 * 1000;
        }
    }

    /**
     * 记录玩家登录时间（应该在玩家登录事件中调用）
     */
    public static void recordPlayerLogin(String playerName) {
        playerLoginTimes.put(playerName, System.currentTimeMillis());
    }

    /**
     * 移除玩家登录记录（应该在玩家退出事件中调用）
     */
    public static void removePlayerLogin(String playerName) {
        playerLoginTimes.remove(playerName);
    }

    /**
     * 实时更新数据（玩家加入/离开时调用）
     */
    public static void updateRealTimeData() {
        try {
            // 在EDT线程中安全地更新UI
            javax.swing.SwingUtilities.invokeLater(() -> {
                try {
                    // 获取当前实例
                    if (instance != null) {
                        // 更新玩家统计数据
                        instance.updatePlayerActivityStats();
                        instance.updatePlayerStatsDisplay();

                        // 更新排行榜
                        if (instance.topThreePanel != null && instance.normalRankingPanel != null) {
                            instance.updateNewRankingPanels();
                        }

                        // 更新在线玩家列表
                        instance.refreshOnlinePlayers();
                        instance.refreshCharmOnlinePlayers();
                        instance.refreshGUIPlayersList();

                        // 更新监控面板的在线玩家数
                        instance.updateMonitoringPlayerCount();

                        // 保存更新后的数据
                        instance.saveMonitoringData();
                    }
                } catch (Exception e) {
                    System.err.println("实时更新数据失败: " + e.getMessage());
                }
            });
        } catch (Exception e) {
            System.err.println("调用实时更新失败: " + e.getMessage());
        }
    }

    /**
     * 更新监控面板的在线玩家数
     */
    private void updateMonitoringPlayerCount() {
        try {
            // 更新性能统计数据
            updatePerformanceStats();

            // 如果有监控标签的引用，直接更新
            if (monitoringTpsLabel != null && monitoringPlayersLabel != null &&
                    monitoringMemoryLabel != null && monitoringStatusLabel != null) {
                updateMonitorData(monitoringTpsLabel, monitoringPlayersLabel,
                        monitoringMemoryLabel, monitoringStatusLabel);
            } else {
                // 查找监控面板中的标签并更新
                if (this.getContentPane() != null) {
                    updatePlayerCountInMonitoringPanel(this.getContentPane());
                }
            }
        } catch (Exception e) {
            System.err.println("更新监控面板数据失败: " + e.getMessage());
        }
    }

    /**
     * 递归查找并更新监控面板中的玩家数标签
     */
    private void updatePlayerCountInMonitoringPanel(java.awt.Container container) {
        try {
            for (java.awt.Component component : container.getComponents()) {
                if (component instanceof JLabel) {
                    JLabel label = (JLabel) component;
                    String text = label.getText();
                    // 查找在线玩家数标签
                    if (text != null && text.contains("/") && (text.contains("在线玩家") || text.matches("\\d+/\\d+"))) {
                        int onlinePlayers = Bukkit.getOnlinePlayers().size();
                        int maxPlayers = Bukkit.getMaxPlayers();
                        label.setText(onlinePlayers + "/" + maxPlayers);
                    }
                } else if (component instanceof java.awt.Container) {
                    updatePlayerCountInMonitoringPanel((java.awt.Container) component);
                }
            }
        } catch (Exception e) {
            // 忽略错误，避免影响其他功能
        }
    }

    /**
     * 更新真实的活跃玩家排行
     */
    private void updateRealActivePlayerRanking() {
        try {
            // 创建玩家活跃度列表
            java.util.List<PlayerActivity> playerActivities = new java.util.ArrayList<>();

            // 获取所有在线玩家的活跃度信息
            for (Player player : Bukkit.getOnlinePlayers()) {
                try {
                    String playerName = player.getName();

                    // 获取玩家本次会话的在线时长
                    long currentSessionTime = getCurrentSessionTime(player);

                    // 转换为小时和分钟
                    long hours = currentSessionTime / (1000 * 60 * 60);
                    long minutes = (currentSessionTime % (1000 * 60 * 60)) / (1000 * 60);

                    playerActivities.add(new PlayerActivity(playerName, hours, minutes, currentSessionTime));
                } catch (Exception e) {
                    // 跳过有问题的玩家
                    continue;
                }
            }

            // 按本次会话在线时长排序（降序）
            playerActivities.sort((a, b) -> Long.compare(b.totalTimeMillis, a.totalTimeMillis));

            // 添加到排行榜（最多显示10个）
            int rank = 1;
            for (PlayerActivity activity : playerActivities) {
                if (rank > 10)
                    break; // 最多显示前10名

                String rankText;
                if (activity.hours > 0) {
                    rankText = String.format("%d. %s - 本次在线%d小时%d分钟",
                            rank, activity.playerName, activity.hours, activity.minutes);
                } else {
                    rankText = String.format("%d. %s - 本次在线%d分钟",
                            rank, activity.playerName, activity.minutes);
                }

                activeRankingModel.addElement(rankText);
                rank++;
            }

            // 如果没有在线玩家，显示提示
            if (playerActivities.isEmpty()) {
                activeRankingModel.addElement("暂无在线玩家");
            }

        } catch (Exception e) {
            System.err.println("更新活跃玩家排行失败: " + e.getMessage());
            activeRankingModel.addElement("获取排行数据失败");
        }
    }

    /**
     * 玩家活跃度数据类
     */
    private static class PlayerActivity {
        String playerName;
        long hours;
        long minutes;
        long totalTimeMillis;

        PlayerActivity(String playerName, long hours, long minutes, long totalTimeMillis) {
            this.playerName = playerName;
            this.hours = hours;
            this.minutes = minutes;
            this.totalTimeMillis = totalTimeMillis;
        }
    }

    /**
     * 更新新的排行榜面板（前三宝座 + 普通排名）
     */
    private void updateNewRankingPanels() {
        try {
            // 创建玩家活跃度列表
            java.util.List<PlayerActivity> playerActivities = new java.util.ArrayList<>();

            // 获取所有在线玩家的当前会话在线时长
            for (Player player : Bukkit.getOnlinePlayers()) {
                try {
                    String playerName = player.getName();

                    // 获取玩家本次会话的在线时长
                    long currentSessionTime = getCurrentSessionTime(player);

                    // 转换为小时和分钟
                    long hours = currentSessionTime / (1000 * 60 * 60);
                    long minutes = (currentSessionTime % (1000 * 60 * 60)) / (1000 * 60);

                    playerActivities.add(new PlayerActivity(playerName, hours, minutes, currentSessionTime));
                } catch (Exception e) {
                    // 跳过有问题的玩家
                    continue;
                }
            }

            // 按本次会话在线时长排序（降序）
            playerActivities.sort((a, b) -> Long.compare(b.totalTimeMillis, a.totalTimeMillis));

            // 更新前三宝座面板
            topThreePanel.updateTopThree(playerActivities);

            // 更新普通排名面板
            normalRankingPanel.updateNormalRanking(playerActivities);

        } catch (Exception e) {
            System.err.println("更新排行榜面板失败: " + e.getMessage());
        }
    }

    /**
     * 自定义的活跃玩家排行榜面板
     */
    private class ActivePlayerRankingPanel extends JPanel {
        private java.util.List<PlayerActivity> playerActivities = new java.util.ArrayList<>();
        private java.util.Map<String, java.awt.Image> playerHeadCache = new java.util.HashMap<>();

        public ActivePlayerRankingPanel() {
            setLayout(null);
            setBackground(new java.awt.Color(248, 248, 255)); // 淡蓝色背景
            setPreferredSize(new java.awt.Dimension(280, 300));
        }

        /**
         * 更新排行榜数据
         */
        public void updateRanking(java.util.List<PlayerActivity> activities) {
            this.playerActivities = new java.util.ArrayList<>(activities);
            repaint();
        }

        @Override
        protected void paintComponent(java.awt.Graphics g) {
            super.paintComponent(g);
            java.awt.Graphics2D g2d = (java.awt.Graphics2D) g;
            g2d.setRenderingHint(java.awt.RenderingHints.KEY_ANTIALIASING, java.awt.RenderingHints.VALUE_ANTIALIAS_ON);

            if (playerActivities.isEmpty()) {
                // 显示无玩家提示
                g2d.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 16));
                g2d.setColor(java.awt.Color.GRAY);
                String noPlayerText = "暂无在线玩家";
                java.awt.FontMetrics fm = g2d.getFontMetrics();
                int textWidth = fm.stringWidth(noPlayerText);
                g2d.drawString(noPlayerText, (getWidth() - textWidth) / 2, getHeight() / 2);
                return;
            }

            // 绘制排行榜
            int yOffset = 20;
            int itemHeight = 45;

            for (int i = 0; i < Math.min(playerActivities.size(), 10); i++) {
                PlayerActivity activity = playerActivities.get(i);
                int rank = i + 1;
                int y = yOffset + i * itemHeight;

                // 绘制排名背景
                drawRankBackground(g2d, rank, y, itemHeight);

                // 绘制排名数字
                drawRankNumber(g2d, rank, y, itemHeight);

                // 绘制玩家头颅
                drawPlayerHead(g2d, activity.playerName, y, itemHeight);

                // 绘制玩家信息
                drawPlayerInfo(g2d, activity, rank, y, itemHeight);
            }
        }

        /**
         * 绘制排名背景
         */
        private void drawRankBackground(java.awt.Graphics2D g2d, int rank, int y, int itemHeight) {
            java.awt.Color bgColor;
            switch (rank) {
                case 1:
                    bgColor = new java.awt.Color(255, 215, 0, 100); // 金色
                    break;
                case 2:
                    bgColor = new java.awt.Color(192, 192, 192, 100); // 银色
                    break;
                case 3:
                    bgColor = new java.awt.Color(205, 127, 50, 100); // 铜色
                    break;
                default:
                    bgColor = new java.awt.Color(240, 240, 240, 100); // 灰色
                    break;
            }

            g2d.setColor(bgColor);
            g2d.fillRoundRect(5, y - 5, getWidth() - 10, itemHeight - 5, 10, 10);

            // 绘制边框
            if (rank <= 3) {
                java.awt.Color borderColor = rank == 1 ? new java.awt.Color(255, 215, 0)
                        : rank == 2 ? new java.awt.Color(192, 192, 192) : new java.awt.Color(205, 127, 50);
                g2d.setColor(borderColor);
                g2d.setStroke(new java.awt.BasicStroke(2));
                g2d.drawRoundRect(5, y - 5, getWidth() - 10, itemHeight - 5, 10, 10);
            }
        }

        /**
         * 绘制排名数字
         */
        private void drawRankNumber(java.awt.Graphics2D g2d, int rank, int y, int itemHeight) {
            String rankText;
            java.awt.Color rankColor;

            switch (rank) {
                case 1:
                    rankText = "👑";
                    rankColor = new java.awt.Color(255, 215, 0);
                    break;
                case 2:
                    rankText = "🥈";
                    rankColor = new java.awt.Color(192, 192, 192);
                    break;
                case 3:
                    rankText = "🥉";
                    rankColor = new java.awt.Color(205, 127, 50);
                    break;
                default:
                    rankText = String.valueOf(rank);
                    rankColor = new java.awt.Color(100, 100, 100);
                    break;
            }

            g2d.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, rank <= 3 ? 18 : 14));
            g2d.setColor(rankColor);
            g2d.drawString(rankText, 15, y + itemHeight / 2 + 5);
        }

        /**
         * 绘制玩家头颅
         */
        private void drawPlayerHead(java.awt.Graphics2D g2d, String playerName, int y, int itemHeight) {
            int headSize = 32;
            int headX = 45;
            int headY = y + (itemHeight - headSize) / 2;

            // 尝试获取玩家头颅图像
            java.awt.Image headImage = getPlayerHeadImage(playerName);
            if (headImage != null) {
                g2d.drawImage(headImage, headX, headY, headSize, headSize, null);
            } else {
                // 绘制默认头颅
                g2d.setColor(new java.awt.Color(139, 69, 19)); // 棕色
                g2d.fillRect(headX, headY, headSize, headSize);
                g2d.setColor(java.awt.Color.BLACK);
                g2d.drawRect(headX, headY, headSize, headSize);

                // 绘制简单的脸部特征
                g2d.setColor(java.awt.Color.WHITE);
                g2d.fillOval(headX + 8, headY + 8, 4, 4); // 左眼
                g2d.fillOval(headX + 20, headY + 8, 4, 4); // 右眼
                g2d.fillOval(headX + 14, headY + 20, 4, 2); // 嘴巴
            }
        }

        /**
         * 绘制玩家信息
         */
        private void drawPlayerInfo(java.awt.Graphics2D g2d, PlayerActivity activity, int rank, int y, int itemHeight) {
            int textX = 85;
            int nameY = y + itemHeight / 2 - 5;
            int timeY = y + itemHeight / 2 + 10;

            // 绘制玩家名称
            g2d.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
            g2d.setColor(rank <= 3 ? new java.awt.Color(0, 100, 0) : java.awt.Color.BLACK);
            g2d.drawString(activity.playerName, textX, nameY);

            // 绘制在线时长
            g2d.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 10));
            g2d.setColor(new java.awt.Color(100, 100, 100));
            String timeText;
            if (activity.hours > 0) {
                timeText = String.format("本次在线 %d小时%d分钟", activity.hours, activity.minutes);
            } else {
                timeText = String.format("本次在线 %d分钟", activity.minutes);
            }
            g2d.drawString(timeText, textX, timeY);
        }

        /**
         * 获取玩家头颅图像
         */
        private java.awt.Image getPlayerHeadImage(String playerName) {
            // 检查缓存
            if (playerHeadCache.containsKey(playerName)) {
                return playerHeadCache.get(playerName);
            }

            try {
                // 尝试从Minecraft皮肤API获取头颅
                // 这里可以实现真实的皮肤获取逻辑
                // 暂时返回null，使用默认头颅
                return null;
            } catch (Exception e) {
                return null;
            }
        }
    }

    /**
     * 更新性能统计
     */
    private void updatePerformanceStats() {
        try {
            // 记录当前TPS到历史数组
            double currentTps = getTPS();
            tpsHistory[tpsHistoryIndex] = currentTps;
            tpsHistoryIndex = (tpsHistoryIndex + 1) % tpsHistory.length;

            // 计算平均TPS
            double avgTps = calculateAverageTPS();
            double minTps = calculateMinTPS();

            // 获取服务器运行时间
            long uptimeMillis = System.currentTimeMillis() - getServerStartTime();
            String uptimeString = formatUptime(uptimeMillis);

            // 存储性能统计
            performanceStats.put("avgTps", avgTps);
            performanceStats.put("minTps", minTps);
            performanceStats.put("uptime", uptimeString);
            performanceStats.put("pluginCount", Bukkit.getPluginManager().getPlugins().length);
            performanceStats.put("worldCount", Bukkit.getWorlds().size());
            performanceStats.put("cpuUsage", getCPUUsage());

        } catch (Exception e) {
            System.err.println("更新性能统计失败: " + e.getMessage());
        }
    }

    // 性能统计数据存储
    private java.util.Map<String, Object> performanceStats = new java.util.HashMap<>();
    private long serverStartTime = System.currentTimeMillis();

    /**
     * 计算平均TPS
     */
    private double calculateAverageTPS() {
        double sum = 0;
        int count = 0;
        for (double tps : tpsHistory) {
            if (tps > 0) {
                sum += tps;
                count++;
            }
        }
        return count > 0 ? sum / count : 20.0;
    }

    /**
     * 计算最低TPS
     */
    private double calculateMinTPS() {
        double min = 20.0;
        for (double tps : tpsHistory) {
            if (tps > 0 && tps < min) {
                min = tps;
            }
        }
        return min;
    }

    /**
     * 获取服务器启动时间
     */
    private long getServerStartTime() {
        try {
            // 尝试从系统属性获取启动时间
            return java.lang.management.ManagementFactory.getRuntimeMXBean().getStartTime();
        } catch (Exception e) {
            return serverStartTime;
        }
    }

    /**
     * 格式化运行时间
     */
    private String formatUptime(long uptimeMillis) {
        long seconds = uptimeMillis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;

        hours = hours % 24;
        minutes = minutes % 60;

        if (days > 0) {
            return String.format("%d天%d小时%d分钟", days, hours, minutes);
        } else if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes);
        } else {
            return String.format("%d分钟", minutes);
        }
    }

    /**
     * 获取CPU使用率
     */
    private double getCPUUsage() {
        try {
            // 简化CPU使用率获取，避免访问限制问题
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            double memoryUsage = (double) (totalMemory - freeMemory) / totalMemory * 100;

            // 基于内存使用率估算CPU使用率
            return Math.min(memoryUsage * 0.8, 95.0);
        } catch (Exception e) {
            return 45.0; // 默认值
        }
    }

    // 套装检测相关变量
    private java.util.Map<String, String> lastPlayerSuits = new java.util.HashMap<>();
    private org.bukkit.scheduler.BukkitTask suitDetectionTask;

    /**
     * 启动套装检测任务
     */
    private void startSuitDetectionTask() {
        try {
            // 如果已有任务在运行，先停止
            if (suitDetectionTask != null) {
                suitDetectionTask.cancel();
            }

            // 启动新的检测任务，每2秒检测一次
            suitDetectionTask = Bukkit.getScheduler().runTaskTimerAsynchronously(
                    Cuilian.getInstance(),
                    this::checkPlayerSuitChanges,
                    40L, // 2秒后开始
                    40L // 每2秒执行一次
            );

            System.out.println("套装检测任务已启动");
        } catch (Exception e) {
            System.err.println("启动套装检测任务失败: " + e.getMessage());
        }
    }

    /**
     * 检查玩家套装变化
     */
    private void checkPlayerSuitChanges() {
        try {
            boolean hasChanges = false;
            java.util.Map<String, String> currentSuits = new java.util.HashMap<>();

            // 检查所有在线玩家的套装
            for (Player player : Bukkit.getOnlinePlayers()) {
                String currentSuit = getCurrentPlayerSuit(player);
                String playerName = player.getName();

                currentSuits.put(playerName, currentSuit != null ? currentSuit : "");

                // 检查是否有变化
                String lastSuit = lastPlayerSuits.get(playerName);
                if (!java.util.Objects.equals(lastSuit, currentSuit)) {
                    hasChanges = true;
                    // 移除频繁的套装变化日志
                }
            }

            // 检查是否有玩家离线
            for (String playerName : lastPlayerSuits.keySet()) {
                if (!currentSuits.containsKey(playerName)) {
                    hasChanges = true;
                    // 移除频繁的离线日志
                }
            }

            // 更新记录
            lastPlayerSuits = currentSuits;

            // 如果有变化，更新统计数据
            if (hasChanges) {
                // 在主线程中更新UI
                Bukkit.getScheduler().runTask(Cuilian.getInstance(), () -> {
                    updateSuitUsageStats();
                    updateStoneUsageStats();
                    refreshStatsDisplay();
                    // 移除频繁的自动更新日志
                });
            }

        } catch (Exception e) {
            System.err.println("检查套装变化失败: " + e.getMessage());
        }
    }

    /**
     * 停止套装检测任务
     */
    private void stopSuitDetectionTask() {
        try {
            if (suitDetectionTask != null) {
                suitDetectionTask.cancel();
                System.out.println("套装检测任务已停止");
            }
        } catch (Exception e) {
            System.err.println("停止套装检测任务失败: " + e.getMessage());
        }
    }

    // 界面组件引用，用于更新显示
    private JLabel totalSuitsLabel;
    private JLabel popularSuitLabel;
    private DefaultListModel<String> suitRankingModel;
    private java.util.Map<String, JLabel> stoneCountLabels = new java.util.HashMap<>();
    private JLabel avgSuccessLabel;
    private JLabel totalPlayersLabel;
    private JLabel activePlayersLabel;
    private JLabel newPlayersLabel;
    private JLabel avgOnlineLabel;
    private DefaultListModel<String> activeRankingModel;
    private ActivePlayerRankingPanel activeRankingPanel;
    private TopThreePanel topThreePanel;
    private NormalRankingPanel normalRankingPanel;
    private JLabel avgTpsLabel;
    private JLabel minTpsLabel;
    private JLabel uptimeLabel;
    private JLabel pluginCountLabel;
    private JLabel worldCountLabel;
    private JLabel cpuLabel;

    /**
     * 刷新统计数据显示
     */
    private void refreshStatsDisplay() {
        try {
            // 更新套装统计显示
            updateSuitStatsDisplay();

            // 更新淬炼石统计显示
            updateStoneStatsDisplay();

            // 更新玩家统计显示
            updatePlayerStatsDisplay();

            // 更新性能统计显示
            updatePerformanceStatsDisplay();

        } catch (Exception e) {
            System.err.println("刷新统计显示失败: " + e.getMessage());
        }
    }

    /**
     * 更新套装统计显示
     */
    private void updateSuitStatsDisplay() {
        try {
            if (totalSuitsLabel != null) {
                totalSuitsLabel.setText(String.valueOf(suitUsageStats.size()));
            }

            if (popularSuitLabel != null && !suitUsageStats.isEmpty()) {
                // 找出最受欢迎的套装
                java.util.Map.Entry<String, Integer> mostPopularEntry = suitUsageStats.entrySet().stream()
                        .max(java.util.Map.Entry.comparingByValue())
                        .orElse(null);

                if (mostPopularEntry != null) {
                    // 移除颜色代码，只显示纯文本
                    String cleanSuitName = removeColorCodes(mostPopularEntry.getKey());
                    String mostPopular = "<html><font color='#00AA00'>" + cleanSuitName +
                            "</font> <font color='#FFAA00'>(" + mostPopularEntry.getValue() + "人使用)</font></html>";
                    popularSuitLabel.setText(mostPopular);
                } else {
                    popularSuitLabel.setText("暂无数据");
                }
            }

            if (suitRankingModel != null) {
                suitRankingModel.clear();
                // 按使用人数排序，添加带品质颜色的套装名称
                suitUsageStats.entrySet().stream()
                        .sorted(java.util.Map.Entry.<String, Integer>comparingByValue().reversed())
                        .forEach(entry -> {
                            // 获取带品质颜色的套装名称
                            String coloredSuitName = getSuitWithQualityColor(entry.getKey());
                            String displayText = coloredSuitName + " - " + entry.getValue() + "人使用";
                            suitRankingModel.addElement(displayText);
                        });
            }

        } catch (Exception e) {
            System.err.println("更新套装统计显示失败: " + e.getMessage());
        }
    }

    /**
     * 更新淬炼石统计显示
     */
    private void updateStoneStatsDisplay() {
        try {
            for (java.util.Map.Entry<String, Integer> entry : stoneUsageStats.entrySet()) {
                JLabel label = stoneCountLabels.get(entry.getKey());
                if (label != null) {
                    label.setText(entry.getValue() + "个");
                }
            }

            if (avgSuccessLabel != null) {
                // 计算平均成功率
                double avgSuccess = calculateAverageSuccessRate();
                avgSuccessLabel.setText(String.format("%.1f%%", avgSuccess));

                // 根据成功率设置颜色
                if (avgSuccess >= 70.0) {
                    avgSuccessLabel.setForeground(new java.awt.Color(0, 128, 0)); // 绿色
                } else if (avgSuccess >= 50.0) {
                    avgSuccessLabel.setForeground(new java.awt.Color(255, 165, 0)); // 橙色
                } else {
                    avgSuccessLabel.setForeground(new java.awt.Color(255, 0, 0)); // 红色
                }
            }

        } catch (Exception e) {
            System.err.println("更新淬炼石统计显示失败: " + e.getMessage());
        }
    }

    /**
     * 计算平均成功率
     */
    private double calculateAverageSuccessRate() {
        // 如果没有使用任何淬炼石，返回0
        int totalAttempts = stoneUsageStats.values().stream().mapToInt(Integer::intValue).sum();
        if (totalAttempts == 0) {
            return 0.0;
        }

        // 从Probability.yml配置文件获取真实的成功率
        double totalWeightedSuccess = 0.0;
        int totalUsage = 0;

        for (java.util.Map.Entry<String, Integer> entry : stoneUsageStats.entrySet()) {
            String stoneType = entry.getKey();
            int usage = entry.getValue();
            if (usage > 0) {
                double avgSuccessRate = getStoneTypeAverageSuccessRate(stoneType);
                totalWeightedSuccess += avgSuccessRate * usage;
                totalUsage += usage;
            }
        }

        return totalUsage > 0 ? totalWeightedSuccess / totalUsage : 0.0;
    }

    /**
     * 获取指定淬炼石类型的平均成功率
     */
    private double getStoneTypeAverageSuccessRate(String stoneType) {
        try {
            // 从Probability.yml读取成功率配置
            String configKey = getStoneConfigKey(stoneType);
            if (configKey == null) {
                return 0.0;
            }

            // 读取Probability.yml文件
            java.io.File probabilityFile = new java.io.File("plugins/Cuilian/Probability.yml");
            if (!probabilityFile.exists()) {
                return getDefaultSuccessRate(stoneType);
            }

            org.bukkit.configuration.file.YamlConfiguration config = org.bukkit.configuration.file.YamlConfiguration
                    .loadConfiguration(probabilityFile);

            org.bukkit.configuration.ConfigurationSection section = config.getConfigurationSection(configKey);
            if (section == null) {
                return getDefaultSuccessRate(stoneType);
            }

            // 计算平均成功率（0-18级的平均值）
            double totalRate = 0.0;
            int count = 0;
            for (int level = 0; level <= 18; level++) {
                if (section.contains(String.valueOf(level))) {
                    totalRate += section.getDouble(String.valueOf(level), 0.0);
                    count++;
                }
            }

            return count > 0 ? totalRate / count : getDefaultSuccessRate(stoneType);

        } catch (Exception e) {
            System.err.println("获取淬炼石成功率失败: " + e.getMessage());
            return getDefaultSuccessRate(stoneType);
        }
    }

    /**
     * 启动Probability.yml文件监控
     */
    private void startProbabilityFileWatcher() {
        try {
            // 使用简单的定时检查方式监控文件变化
            fileWatcherExecutor = java.util.concurrent.Executors.newSingleThreadScheduledExecutor();
            fileWatcherExecutor.scheduleAtFixedRate(() -> {
                try {
                    checkProbabilityFileChanges();
                } catch (Exception e) {
                    System.err.println("检查Probability.yml文件变化失败: " + e.getMessage());
                }
            }, 5, 5, java.util.concurrent.TimeUnit.SECONDS); // 每5秒检查一次

            System.out.println("Probability.yml文件监控已启动");
        } catch (Exception e) {
            System.err.println("启动Probability.yml文件监控失败: " + e.getMessage());
        }
    }

    /**
     * 停止Probability.yml文件监控
     */
    private void stopProbabilityFileWatcher() {
        try {
            if (fileWatcherExecutor != null && !fileWatcherExecutor.isShutdown()) {
                fileWatcherExecutor.shutdown();
                System.out.println("Probability.yml文件监控已停止");
            }
        } catch (Exception e) {
            System.err.println("停止Probability.yml文件监控失败: " + e.getMessage());
        }
    }

    /**
     * 检查Probability.yml文件是否有变化
     */
    private void checkProbabilityFileChanges() {
        try {
            java.io.File probabilityFile = new java.io.File("plugins/Cuilian/Probability.yml");
            if (probabilityFile.exists()) {
                long currentModified = probabilityFile.lastModified();
                if (lastProbabilityFileModified == 0) {
                    // 首次检查，记录当前修改时间
                    lastProbabilityFileModified = currentModified;
                } else if (currentModified > lastProbabilityFileModified) {
                    // 文件已被修改
                    lastProbabilityFileModified = currentModified;
                    onProbabilityFileChanged();
                }
            }
        } catch (Exception e) {
            System.err.println("检查Probability.yml文件变化失败: " + e.getMessage());
        }
    }

    /**
     * 当Probability.yml文件发生变化时的处理
     */
    private void onProbabilityFileChanged() {
        try {
            System.out.println("检测到Probability.yml文件已更新，重新计算平均成功率...");

            // 在EDT线程中更新UI
            javax.swing.SwingUtilities.invokeLater(() -> {
                try {
                    // 重新计算并更新平均成功率显示
                    updateStoneStatsDisplay();

                    // 自动保存监控数据
                    saveMonitoringData();

                    System.out.println("平均成功率已根据新的Probability.yml配置更新");
                } catch (Exception e) {
                    System.err.println("更新平均成功率失败: " + e.getMessage());
                }
            });
        } catch (Exception e) {
            System.err.println("处理Probability.yml文件变化失败: " + e.getMessage());
        }
    }

    /**
     * 获取淬炼石类型对应的配置键
     */
    private String getStoneConfigKey(String stoneType) {
        switch (stoneType) {
            case "普通":
                return "putong";
            case "中等":
                return "zhongdeng";
            case "高等":
                return "gaodeng";
            case "上等":
                return "wanmei";
            case "符咒":
                return "yuangu";
            case "吞噬":
                return "tunshi";
            default:
                return null;
        }
    }

    /**
     * 获取默认成功率（当配置文件不存在时使用）
     */
    private double getDefaultSuccessRate(String stoneType) {
        switch (stoneType) {
            case "普通":
                return 50.0;
            case "中等":
                return 60.0;
            case "高等":
                return 70.0;
            case "上等":
                return 80.0;
            case "符咒":
                return 90.0;
            case "吞噬":
                return 95.0;
            default:
                return 50.0;
        }
    }

    /**
     * 更新玩家统计显示
     */
    private void updatePlayerStatsDisplay() {
        try {
            if (totalPlayersLabel != null) {
                Object totalPlayers = playerStats.getOrDefault("totalPlayers", 0);
                totalPlayersLabel.setText(String.valueOf(totalPlayers));
            }

            if (activePlayersLabel != null) {
                Object todayActive = playerStats.getOrDefault("todayActive", 0);
                activePlayersLabel.setText(String.valueOf(todayActive));
            }

            if (newPlayersLabel != null) {
                Object todayNew = playerStats.getOrDefault("todayNew", 0);
                newPlayersLabel.setText(String.valueOf(todayNew));
            }

            if (avgOnlineLabel != null) {
                // 获取真实的平均在线时长
                int realAvgTime = getRealAverageOnlineTime();

                // 如果没有玩家数据或者数据被清空，显示0
                if (realAvgTime <= 0 || playerStats.isEmpty()) {
                    avgOnlineLabel.setText("0分钟");
                } else {
                    int hours = realAvgTime / 60;
                    int remainingMinutes = realAvgTime % 60;
                    if (hours > 0) {
                        avgOnlineLabel.setText(hours + "小时" + remainingMinutes + "分钟");
                    } else {
                        avgOnlineLabel.setText(remainingMinutes + "分钟");
                    }
                }
            }

            if (activeRankingModel != null) {
                activeRankingModel.clear();
                // 获取真实的活跃玩家排行
                updateRealActivePlayerRanking();
            }

            // 更新新的排行榜面板
            if (topThreePanel != null && normalRankingPanel != null) {
                updateNewRankingPanels();
            }

        } catch (Exception e) {
            System.err.println("更新玩家统计显示失败: " + e.getMessage());
        }
    }

    /**
     * 更新性能统计显示
     */
    private void updatePerformanceStatsDisplay() {
        try {
            if (avgTpsLabel != null) {
                double avgTps = (Double) performanceStats.getOrDefault("avgTps", 20.0);
                avgTpsLabel.setText(String.format("%.1f", avgTps));

                // 设置颜色
                if (avgTps >= 18.0) {
                    avgTpsLabel.setForeground(new java.awt.Color(0, 128, 0));
                } else if (avgTps >= 15.0) {
                    avgTpsLabel.setForeground(new java.awt.Color(255, 165, 0));
                } else {
                    avgTpsLabel.setForeground(new java.awt.Color(255, 0, 0));
                }
            }

            if (minTpsLabel != null) {
                double minTps = (Double) performanceStats.getOrDefault("minTps", 20.0);
                minTpsLabel.setText(String.format("%.1f", minTps));

                // 设置颜色
                if (minTps >= 15.0) {
                    minTpsLabel.setForeground(new java.awt.Color(255, 165, 0));
                } else {
                    minTpsLabel.setForeground(new java.awt.Color(255, 0, 0));
                }
            }

            if (uptimeLabel != null) {
                uptimeLabel.setText((String) performanceStats.getOrDefault("uptime", "未知"));
            }

            if (pluginCountLabel != null) {
                pluginCountLabel.setText(String.valueOf(performanceStats.getOrDefault("pluginCount", 0)));
            }

            if (worldCountLabel != null) {
                worldCountLabel.setText(String.valueOf(performanceStats.getOrDefault("worldCount", 0)));
            }

            if (cpuLabel != null) {
                double cpuUsage = (Double) performanceStats.getOrDefault("cpuUsage", 0.0);
                cpuLabel.setText(String.format("%.1f%%", cpuUsage));

                // 设置颜色
                if (cpuUsage < 50.0) {
                    cpuLabel.setForeground(new java.awt.Color(0, 128, 0));
                } else if (cpuUsage < 80.0) {
                    cpuLabel.setForeground(new java.awt.Color(255, 165, 0));
                } else {
                    cpuLabel.setForeground(new java.awt.Color(255, 0, 0));
                }
            }

        } catch (Exception e) {
            System.err.println("更新性能统计显示失败: " + e.getMessage());
        }
    }

    /**
     * 清空本地存储记录
     */
    private void clearLocalStorageData() {
        try {
            // 确认操作
            int result = JOptionPane.showConfirmDialog(
                    this,
                    "确定要清空所有本地存储的监控记录吗？\n这将删除：\n- 套装使用统计\n- 淬炼石使用统计\n- 玩家活跃度统计\n- 性能监控历史\n\n此操作不可撤销！",
                    "确认清空本地记录",
                    JOptionPane.YES_NO_OPTION,
                    JOptionPane.WARNING_MESSAGE);

            if (result == JOptionPane.YES_OPTION) {
                // 清空所有统计数据
                suitUsageStats.clear();
                stoneUsageStats.clear();
                playerStats.clear();
                performanceStats.clear();

                // 重置TPS历史数据
                for (int i = 0; i < tpsHistory.length; i++) {
                    tpsHistory[i] = 0.0;
                }
                tpsHistoryIndex = 0;

                // 清空玩家登录时间记录
                playerLoginTimes.clear();

                // 删除本地存储文件
                java.io.File dataFile = new java.io.File(MONITORING_DATA_FILE);
                if (dataFile.exists()) {
                    dataFile.delete();
                }

                // 重置所有显示标签为默认值
                resetAllDisplayLabels();

                // 重新初始化默认数据
                initializeDefaultStatsData();

                // 刷新显示
                refreshStatsDisplay();

                JOptionPane.showMessageDialog(this, "本地存储记录已清空！", "提示", JOptionPane.INFORMATION_MESSAGE);
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "清空本地记录失败: " + e.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
            e.printStackTrace();
        }
    }

    /**
     * 清空所有统计数据
     */
    private void clearAllStats() {
        try {
            // 清空所有统计数据
            suitUsageStats.clear();
            stoneUsageStats.clear();
            playerStats.clear();
            performanceStats.clear();

            // 清空TPS历史
            for (int i = 0; i < tpsHistory.length; i++) {
                tpsHistory[i] = 0;
            }
            tpsHistoryIndex = 0;

            // 清空玩家登录时间记录
            playerLoginTimes.clear();

            // 重置所有显示标签为默认值
            resetAllDisplayLabels();

            // 刷新显示
            refreshStatsDisplay();

            JOptionPane.showMessageDialog(this, "所有统计数据已清空！", "提示", JOptionPane.INFORMATION_MESSAGE);
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "清空数据失败: " + e.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * 重置所有显示标签为默认值
     */
    private void resetAllDisplayLabels() {
        try {
            // 重置玩家统计标签
            if (totalPlayersLabel != null) {
                totalPlayersLabel.setText("0");
            }
            if (activePlayersLabel != null) {
                activePlayersLabel.setText("0");
            }
            if (newPlayersLabel != null) {
                newPlayersLabel.setText("0");
            }
            if (avgOnlineLabel != null) {
                avgOnlineLabel.setText("0分钟");
            }

            // 重置套装统计标签
            if (totalSuitsLabel != null) {
                totalSuitsLabel.setText("0");
            }
            if (popularSuitLabel != null) {
                popularSuitLabel.setText("暂无数据");
            }

            // 重置淬炼石统计标签
            for (JLabel label : stoneCountLabels.values()) {
                if (label != null) {
                    label.setText("0个");
                }
            }
            if (avgSuccessLabel != null) {
                avgSuccessLabel.setText("0.0%");
                avgSuccessLabel.setForeground(java.awt.Color.GRAY);
            }

            // 重置性能统计标签
            if (avgTpsLabel != null) {
                avgTpsLabel.setText("20.0");
                avgTpsLabel.setForeground(new java.awt.Color(0, 128, 0));
            }
            if (minTpsLabel != null) {
                minTpsLabel.setText("20.0");
                minTpsLabel.setForeground(new java.awt.Color(0, 128, 0));
            }
            if (uptimeLabel != null) {
                uptimeLabel.setText("0分钟");
            }
            if (cpuLabel != null) {
                cpuLabel.setText("0.0%");
                cpuLabel.setForeground(new java.awt.Color(0, 128, 0));
            }

            // 清空排行榜
            if (activeRankingModel != null) {
                activeRankingModel.clear();
                activeRankingModel.addElement("暂无在线玩家");
            }
            if (suitRankingModel != null) {
                suitRankingModel.clear();
            }

            // 清空自定义排行榜面板
            if (topThreePanel != null) {
                topThreePanel.updateTopThree(new java.util.ArrayList<>());
            }
            if (normalRankingPanel != null) {
                normalRankingPanel.updateNormalRanking(new java.util.ArrayList<>());
            }

        } catch (Exception e) {
            System.err.println("重置显示标签失败: " + e.getMessage());
        }
    }

    /**
     * 更新监控数据
     */
    private void updateMonitorData(JLabel tpsLabel, JLabel playersLabel, JLabel memoryLabel, JLabel statusLabel) {
        try {
            // 获取TPS
            double tps = getTPS();
            tpsLabel.setText(String.format("%.1f", tps));
            if (tps >= 18.0) {
                tpsLabel.setForeground(new java.awt.Color(0, 128, 0)); // 绿色
            } else if (tps >= 15.0) {
                tpsLabel.setForeground(new java.awt.Color(255, 165, 0)); // 橙色
            } else {
                tpsLabel.setForeground(new java.awt.Color(255, 0, 0)); // 红色
            }

            // 获取在线玩家数
            int onlinePlayers = Bukkit.getOnlinePlayers().size();
            int maxPlayers = Bukkit.getMaxPlayers();
            playersLabel.setText(onlinePlayers + "/" + maxPlayers);

            // 获取内存使用
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024;
            long maxMemory = runtime.maxMemory() / 1024 / 1024;
            memoryLabel.setText(usedMemory + "MB/" + maxMemory + "MB");

            // 更新插件状态
            statusLabel.setText("运行正常");
            statusLabel.setForeground(new java.awt.Color(0, 128, 0));

        } catch (Exception e) {
            statusLabel.setText("获取数据失败");
            statusLabel.setForeground(new java.awt.Color(255, 0, 0));
        }
    }

    /**
     * 获取服务器TPS
     */
    private double getTPS() {
        try {
            // 尝试使用反射获取TPS
            Object server = Bukkit.getServer();
            java.lang.reflect.Field tpsField = server.getClass().getDeclaredField("recentTps");
            tpsField.setAccessible(true);
            double[] tps = (double[]) tpsField.get(server);
            return tps[0];
        } catch (Exception e) {
            // 如果获取失败，返回默认值
            return 20.0;
        }
    }

    /**
     * 创建批量操作面板
     */
    private JPanel createBatchOperationPanel() {
        JPanel mainPanel = new JPanel(new java.awt.BorderLayout());
        mainPanel.setBackground(java.awt.Color.WHITE);

        // 创建左右分栏布局
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(400);
        splitPane.setResizeWeight(0.4);
        splitPane.setBorder(null);

        // 左侧面板 - 玩家选择
        JPanel leftPanel = createBatchLeftPanel();
        splitPane.setLeftComponent(leftPanel);

        // 右侧面板 - 批量操作
        JPanel rightPanel = createBatchRightPanel();
        splitPane.setRightComponent(rightPanel);

        mainPanel.add(splitPane, java.awt.BorderLayout.CENTER);

        return mainPanel;
    }

    /**
     * 创建批量操作左侧面板 - 玩家选择
     */
    private JPanel createBatchLeftPanel() {
        JPanel leftPanel = new JPanel(new java.awt.BorderLayout());
        leftPanel.setBackground(java.awt.Color.WHITE);
        leftPanel.setBorder(BorderFactory.createTitledBorder("批量玩家选择"));

        // 创建多选玩家列表
        this.batchPlayersModel = new DefaultListModel<>();
        JList<String> batchPlayersList = new JList<>(this.batchPlayersModel);
        batchPlayersList.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
        batchPlayersList.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));

        // 添加在线玩家到列表
        for (Player player : Bukkit.getOnlinePlayers()) {
            this.batchPlayersModel.addElement(player.getName());
        }

        JScrollPane scrollPane = new JScrollPane(batchPlayersList);
        scrollPane.setPreferredSize(new java.awt.Dimension(350, 400));
        leftPanel.add(scrollPane, java.awt.BorderLayout.CENTER);

        // 底部按钮
        JPanel buttonPanel = new JPanel(new java.awt.FlowLayout());
        buttonPanel.setBackground(java.awt.Color.WHITE);

        JButton selectAllBtn = new JButton("全选");
        selectAllBtn
                .addActionListener(e -> batchPlayersList.setSelectionInterval(0, this.batchPlayersModel.getSize() - 1));
        buttonPanel.add(selectAllBtn);

        JButton clearBtn = new JButton("清除选择");
        clearBtn.addActionListener(e -> batchPlayersList.clearSelection());
        buttonPanel.add(clearBtn);

        JButton refreshBtn = new JButton("刷新列表");
        refreshBtn.addActionListener(e -> {
            this.batchPlayersModel.clear();
            for (Player player : Bukkit.getOnlinePlayers()) {
                this.batchPlayersModel.addElement(player.getName());
            }
        });
        buttonPanel.add(refreshBtn);

        leftPanel.add(buttonPanel, java.awt.BorderLayout.SOUTH);

        return leftPanel;
    }

    /**
     * 创建批量操作右侧面板 - 批量操作
     */
    private JPanel createBatchRightPanel() {
        JPanel rightPanel = new JPanel(new java.awt.BorderLayout());
        rightPanel.setBackground(java.awt.Color.WHITE);
        rightPanel.setBorder(BorderFactory.createTitledBorder("批量操作"));

        // 创建标签页面板
        JTabbedPane batchTabPane = new JTabbedPane(JTabbedPane.TOP);
        batchTabPane.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));

        // 物品操作标签页
        JPanel itemOpsPanel = createBatchItemOpsPanel();
        batchTabPane.addTab("物品操作", itemOpsPanel);

        // 玩家管理标签页
        JPanel playerMgmtPanel = createBatchPlayerMgmtPanel();
        batchTabPane.addTab("玩家管理", playerMgmtPanel);

        // 服务器操作标签页
        JPanel serverOpsPanel = createBatchServerOpsPanel();
        batchTabPane.addTab("服务器操作", serverOpsPanel);

        // 定时任务标签页
        JPanel scheduledTasksPanel = createBatchScheduledTasksPanel();
        batchTabPane.addTab("定时任务", scheduledTasksPanel);

        rightPanel.add(batchTabPane, java.awt.BorderLayout.CENTER);

        return rightPanel;
    }

    /**
     * 创建批量物品操作面板
     */
    private JPanel createBatchItemOpsPanel() {
        JPanel panel = new JPanel(new java.awt.GridBagLayout());
        panel.setBackground(java.awt.Color.WHITE);

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 批量套装操作
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel suitLabel = new JLabel("批量套装操作");
        suitLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(suitLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("选择套装:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JComboBox<String> batchSuitComboBox = new JComboBox<>();
        // 这里需要重新加载套装列表
        panel.add(batchSuitComboBox, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JButton batchSendSuitBtn = new JButton("批量发送套装");
        batchSendSuitBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        panel.add(batchSendSuitBtn, gbc);

        // 批量淬炼石操作
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel stoneLabel = new JLabel("批量淬炼石操作");
        stoneLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(stoneLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("淬炼石类型:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JComboBox<String> batchStoneComboBox = new JComboBox<>(
                new String[] { "普通", "中等", "高等", "上等", "符咒", "吞噬" });
        panel.add(batchStoneComboBox, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("数量:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JTextField batchAmountField = new JTextField("1");
        panel.add(batchAmountField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JButton batchSendStoneBtn = new JButton("批量发送淬炼石");
        batchSendStoneBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        panel.add(batchSendStoneBtn, gbc);

        // 批量清理操作
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel cleanLabel = new JLabel("批量清理操作");
        cleanLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(cleanLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JButton clearInventoryBtn = new JButton("清空选中玩家背包");
        clearInventoryBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        clearInventoryBtn.setForeground(new java.awt.Color(255, 0, 0));
        panel.add(clearInventoryBtn, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JButton resetEnhanceBtn = new JButton("重置选中玩家淬炼等级");
        resetEnhanceBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        resetEnhanceBtn.setForeground(new java.awt.Color(255, 0, 0));
        panel.add(resetEnhanceBtn, gbc);

        return panel;
    }

    /**
     * 创建批量玩家管理面板
     */
    private JPanel createBatchPlayerMgmtPanel() {
        JPanel panel = new JPanel(new java.awt.GridBagLayout());
        panel.setBackground(java.awt.Color.WHITE);

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 批量消息操作
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel msgLabel = new JLabel("批量消息操作");
        msgLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(msgLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JTextField announceField = new JTextField();
        announceField.setToolTipText("输入要发送的消息内容");
        panel.add(announceField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.5;
        JButton sendMsgBtn = new JButton("发送私聊消息");
        sendMsgBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        panel.add(sendMsgBtn, gbc);

        gbc.gridx = 1;
        gbc.gridy = row;
        JButton broadcastBtn = new JButton("发送公告");
        broadcastBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        panel.add(broadcastBtn, gbc);

        // 批量传送操作
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        JLabel tpLabel = new JLabel("批量传送操作");
        tpLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(tpLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        panel.add(new JLabel("目标玩家:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JTextField targetPlayerField = new JTextField();
        targetPlayerField.setToolTipText("输入要传送到的目标玩家名称");
        panel.add(targetPlayerField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JButton batchTpBtn = new JButton("批量传送到目标玩家");
        batchTpBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        panel.add(batchTpBtn, gbc);

        // 批量权限操作
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel permLabel = new JLabel("批量权限操作");
        permLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(permLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        panel.add(new JLabel("权限节点:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JTextField permissionField = new JTextField();
        permissionField.setToolTipText("输入权限节点，如: cuilian.use");
        panel.add(permissionField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.5;
        JButton grantPermBtn = new JButton("批量授予权限");
        grantPermBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        grantPermBtn.setForeground(new java.awt.Color(0, 128, 0));
        panel.add(grantPermBtn, gbc);

        gbc.gridx = 1;
        gbc.gridy = row;
        JButton revokePermBtn = new JButton("批量撤销权限");
        revokePermBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        revokePermBtn.setForeground(new java.awt.Color(255, 0, 0));
        panel.add(revokePermBtn, gbc);

        return panel;
    }

    /**
     * 创建批量服务器操作面板
     */
    private JPanel createBatchServerOpsPanel() {
        JPanel panel = new JPanel(new java.awt.GridBagLayout());
        panel.setBackground(java.awt.Color.WHITE);

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 服务器公告
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel serverLabel = new JLabel("服务器公告操作");
        serverLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(serverLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JTextField serverAnnounceField = new JTextField();
        serverAnnounceField.setToolTipText("输入服务器公告内容");
        panel.add(serverAnnounceField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.5;
        JButton titleAnnounceBtn = new JButton("发送标题公告");
        titleAnnounceBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        panel.add(titleAnnounceBtn, gbc);

        gbc.gridx = 1;
        gbc.gridy = row;
        JButton chatAnnounceBtn = new JButton("发送聊天公告");
        chatAnnounceBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        panel.add(chatAnnounceBtn, gbc);

        // 批量命令执行
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        JLabel cmdLabel = new JLabel("批量命令执行");
        cmdLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(cmdLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JTextField commandField = new JTextField();
        commandField.setToolTipText("输入要执行的命令（不包含/），使用{player}代表玩家名");
        panel.add(commandField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JButton executeCommandBtn = new JButton("批量执行命令");
        executeCommandBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        executeCommandBtn.setForeground(new java.awt.Color(255, 0, 0));
        panel.add(executeCommandBtn, gbc);

        // 服务器维护操作
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel maintLabel = new JLabel("服务器维护操作");
        maintLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(maintLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.5;
        JButton kickAllBtn = new JButton("踢出所有玩家");
        kickAllBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        kickAllBtn.setForeground(new java.awt.Color(255, 0, 0));
        panel.add(kickAllBtn, gbc);

        gbc.gridx = 1;
        gbc.gridy = row;
        JButton saveAllBtn = new JButton("保存所有数据");
        saveAllBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        panel.add(saveAllBtn, gbc);

        return panel;
    }

    /**
     * 创建批量定时任务面板
     */
    private JPanel createBatchScheduledTasksPanel() {
        JPanel panel = new JPanel(new java.awt.GridBagLayout());
        panel.setBackground(java.awt.Color.WHITE);

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 定时奖励
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel rewardLabel = new JLabel("定时奖励设置");
        rewardLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(rewardLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        panel.add(new JLabel("奖励间隔(分钟):"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JTextField rewardIntervalField = new JTextField("60");
        panel.add(rewardIntervalField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("奖励套装:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JComboBox<String> rewardSuitComboBox = new JComboBox<>();
        panel.add(rewardSuitComboBox, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JButton startRewardTaskBtn = new JButton("启动定时奖励任务");
        startRewardTaskBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        startRewardTaskBtn.setForeground(new java.awt.Color(0, 128, 0));
        panel.add(startRewardTaskBtn, gbc);

        // 定时公告
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel announceTaskLabel = new JLabel("定时公告设置");
        announceTaskLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(announceTaskLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        panel.add(new JLabel("公告间隔(分钟):"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JTextField announceIntervalField = new JTextField("30");
        panel.add(announceIntervalField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JTextField scheduleAnnounceField = new JTextField();
        scheduleAnnounceField.setToolTipText("输入定时公告内容");
        panel.add(scheduleAnnounceField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JButton startAnnounceTaskBtn = new JButton("启动定时公告任务");
        startAnnounceTaskBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        startAnnounceTaskBtn.setForeground(new java.awt.Color(0, 128, 0));
        panel.add(startAnnounceTaskBtn, gbc);

        // 任务管理
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel taskMgmtLabel = new JLabel("任务管理");
        taskMgmtLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(taskMgmtLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.5;
        JButton stopAllTasksBtn = new JButton("停止所有任务");
        stopAllTasksBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        stopAllTasksBtn.setForeground(new java.awt.Color(255, 0, 0));
        panel.add(stopAllTasksBtn, gbc);

        gbc.gridx = 1;
        gbc.gridy = row;
        JButton viewTasksBtn = new JButton("查看运行任务");
        viewTasksBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        panel.add(viewTasksBtn, gbc);

        return panel;
    }

    /**
     * 创建权限与安全面板
     */
    private JPanel createSecurityPanel() {
        JPanel mainPanel = new JPanel(new java.awt.BorderLayout());
        mainPanel.setBackground(java.awt.Color.WHITE);

        // 创建左右分栏布局
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(500);
        splitPane.setResizeWeight(0.5);
        splitPane.setBorder(null);

        // 左侧面板 - 权限管理
        JPanel leftPanel = createSecurityLeftPanel();
        splitPane.setLeftComponent(leftPanel);

        // 右侧面板 - 安全监控
        JPanel rightPanel = createSecurityRightPanel();
        splitPane.setRightComponent(rightPanel);

        mainPanel.add(splitPane, java.awt.BorderLayout.CENTER);

        return mainPanel;
    }

    /**
     * 创建权限与安全左侧面板 - 权限管理
     */
    private JPanel createSecurityLeftPanel() {
        JPanel leftPanel = new JPanel(new java.awt.BorderLayout());
        leftPanel.setBackground(java.awt.Color.WHITE);
        leftPanel.setBorder(BorderFactory.createTitledBorder("权限管理"));

        // 创建标签页面板
        JTabbedPane permTabPane = new JTabbedPane(JTabbedPane.TOP);
        permTabPane.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));

        // 玩家权限标签页
        JPanel playerPermPanel = createPlayerPermissionPanel();
        permTabPane.addTab("玩家权限", playerPermPanel);

        // 权限组标签页
        JPanel groupPermPanel = createGroupPermissionPanel();
        permTabPane.addTab("权限组", groupPermPanel);

        // 插件权限标签页
        JPanel pluginPermPanel = createPluginPermissionPanel();
        permTabPane.addTab("插件权限", pluginPermPanel);

        leftPanel.add(permTabPane, java.awt.BorderLayout.CENTER);

        return leftPanel;
    }

    /**
     * 创建玩家权限面板
     */
    private JPanel createPlayerPermissionPanel() {
        JPanel panel = new JPanel(new java.awt.GridBagLayout());
        panel.setBackground(java.awt.Color.WHITE);

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(5, 5, 5, 5);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 玩家选择
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("选择玩家:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JComboBox<String> playerPermComboBox = new JComboBox<>();
        // 添加在线玩家
        for (Player player : Bukkit.getOnlinePlayers()) {
            playerPermComboBox.addItem(player.getName());
        }
        panel.add(playerPermComboBox, gbc);

        // 权限节点输入
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("权限节点:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JTextField permNodeField = new JTextField();
        permNodeField.setToolTipText("输入权限节点，如: cuilian.use");
        panel.add(permNodeField, gbc);

        // 权限操作按钮
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.5;
        JButton grantPermBtn = new JButton("授予权限");
        grantPermBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        grantPermBtn.setForeground(new java.awt.Color(0, 128, 0));
        panel.add(grantPermBtn, gbc);

        gbc.gridx = 1;
        gbc.gridy = row;
        JButton revokePermBtn = new JButton("撤销权限");
        revokePermBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        revokePermBtn.setForeground(new java.awt.Color(255, 0, 0));
        panel.add(revokePermBtn, gbc);

        // 玩家权限列表
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel permListLabel = new JLabel("玩家权限列表:");
        permListLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        panel.add(permListLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = java.awt.GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 1.0;

        DefaultListModel<String> playerPermModel = new DefaultListModel<>();
        JList<String> playerPermList = new JList<>(playerPermModel);
        playerPermList.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        JScrollPane playerPermScrollPane = new JScrollPane(playerPermList);
        playerPermScrollPane.setPreferredSize(new java.awt.Dimension(200, 200));
        panel.add(playerPermScrollPane, gbc);

        return panel;
    }

    /**
     * 创建权限组面板
     */
    private JPanel createGroupPermissionPanel() {
        JPanel panel = new JPanel(new java.awt.GridBagLayout());
        panel.setBackground(java.awt.Color.WHITE);

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(5, 5, 5, 5);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 权限组管理
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel groupLabel = new JLabel("权限组管理");
        groupLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(groupLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        panel.add(new JLabel("权限组名:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JTextField groupNameField = new JTextField();
        panel.add(groupNameField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JButton createGroupBtn = new JButton("创建权限组");
        createGroupBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        createGroupBtn.setForeground(new java.awt.Color(0, 128, 0));
        panel.add(createGroupBtn, gbc);

        // 玩家组分配
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel assignLabel = new JLabel("玩家组分配");
        assignLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(assignLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        panel.add(new JLabel("选择玩家:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JComboBox<String> groupPlayerComboBox = new JComboBox<>();
        panel.add(groupPlayerComboBox, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("选择组:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JComboBox<String> groupComboBox = new JComboBox<>();
        groupComboBox.addItem("admin");
        groupComboBox.addItem("moderator");
        groupComboBox.addItem("vip");
        groupComboBox.addItem("default");
        panel.add(groupComboBox, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.5;
        JButton addToGroupBtn = new JButton("加入组");
        addToGroupBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        addToGroupBtn.setForeground(new java.awt.Color(0, 128, 0));
        panel.add(addToGroupBtn, gbc);

        gbc.gridx = 1;
        gbc.gridy = row;
        JButton removeFromGroupBtn = new JButton("移出组");
        removeFromGroupBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        removeFromGroupBtn.setForeground(new java.awt.Color(255, 0, 0));
        panel.add(removeFromGroupBtn, gbc);

        return panel;
    }

    /**
     * 创建插件权限面板
     */
    private JPanel createPluginPermissionPanel() {
        JPanel panel = new JPanel(new java.awt.GridBagLayout());
        panel.setBackground(java.awt.Color.WHITE);

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(5, 5, 5, 5);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 淬炼插件权限
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel cuilianPermLabel = new JLabel("淬炼插件权限节点");
        cuilianPermLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(cuilianPermLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.fill = java.awt.GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 1.0;

        DefaultListModel<String> cuilianPermModel = new DefaultListModel<>();
        // 添加淬炼插件的权限节点
        cuilianPermModel.addElement("cuilian.use - 使用淬炼功能");
        cuilianPermModel.addElement("cuilian.admin - 管理员权限");
        cuilianPermModel.addElement("cuilian.gui - 使用GUI界面");
        cuilianPermModel.addElement("cuilian.suit.* - 所有套装权限");
        cuilianPermModel.addElement("cuilian.enhance - 淬炼装备权限");
        cuilianPermModel.addElement("cuilian.effect - 特效权限");
        cuilianPermModel.addElement("cuilian.charm - 符咒权限");
        cuilianPermModel.addElement("cuilian.reload - 重载配置权限");

        JList<String> cuilianPermList = new JList<>(cuilianPermModel);
        cuilianPermList.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        JScrollPane cuilianPermScrollPane = new JScrollPane(cuilianPermList);
        cuilianPermScrollPane.setPreferredSize(new java.awt.Dimension(200, 200));
        panel.add(cuilianPermScrollPane, gbc);

        return panel;
    }

    /**
     * 创建权限与安全右侧面板 - 安全监控
     */
    private JPanel createSecurityRightPanel() {
        JPanel rightPanel = new JPanel(new java.awt.BorderLayout());
        rightPanel.setBackground(java.awt.Color.WHITE);
        rightPanel.setBorder(BorderFactory.createTitledBorder("安全监控"));

        // 创建标签页面板
        JTabbedPane securityTabPane = new JTabbedPane(JTabbedPane.TOP);
        securityTabPane.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));

        // 操作日志标签页
        JPanel operationLogPanel = createOperationLogPanel();
        securityTabPane.addTab("操作日志", operationLogPanel);

        // 安全设置标签页
        JPanel securitySettingsPanel = createSecuritySettingsPanel();
        securityTabPane.addTab("安全设置", securitySettingsPanel);

        // IP管理标签页
        JPanel ipManagementPanel = createIPManagementPanel();
        securityTabPane.addTab("IP管理", ipManagementPanel);

        rightPanel.add(securityTabPane, java.awt.BorderLayout.CENTER);

        return rightPanel;
    }

    /**
     * 创建操作日志面板
     */
    private JPanel createOperationLogPanel() {
        JPanel panel = new JPanel(new java.awt.BorderLayout());
        panel.setBackground(java.awt.Color.WHITE);

        // 日志过滤器
        JPanel filterPanel = new JPanel(new java.awt.FlowLayout(java.awt.FlowLayout.LEFT));
        filterPanel.setBackground(java.awt.Color.WHITE);

        filterPanel.add(new JLabel("过滤:"));
        JComboBox<String> logFilterComboBox = new JComboBox<>(
                new String[] { "全部", "登录", "权限变更", "物品操作", "命令执行", "错误" });
        filterPanel.add(logFilterComboBox);

        JButton refreshLogBtn = new JButton("刷新日志");
        refreshLogBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        filterPanel.add(refreshLogBtn);

        panel.add(filterPanel, java.awt.BorderLayout.NORTH);

        // 日志列表
        DefaultListModel<String> logModel = new DefaultListModel<>();
        logModel.addElement("[2024-01-01 12:00:00] 玩家 TestPlayer 登录服务器");
        logModel.addElement("[2024-01-01 12:01:00] 管理员 Admin 给予 TestPlayer cuilian.use 权限");
        logModel.addElement("[2024-01-01 12:02:00] 玩家 TestPlayer 使用淬炼功能");
        logModel.addElement("[2024-01-01 12:03:00] 玩家 TestPlayer 获得神武套装");

        JList<String> logList = new JList<>(logModel);
        logList.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        JScrollPane logScrollPane = new JScrollPane(logList);
        panel.add(logScrollPane, java.awt.BorderLayout.CENTER);

        return panel;
    }

    /**
     * 创建安全设置面板
     */
    private JPanel createSecuritySettingsPanel() {
        JPanel panel = new JPanel(new java.awt.GridBagLayout());
        panel.setBackground(java.awt.Color.WHITE);

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 安全级别设置
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel securityLevelLabel = new JLabel("安全级别设置");
        securityLevelLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(securityLevelLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        panel.add(new JLabel("安全级别:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JComboBox<String> securityLevelComboBox = new JComboBox<>(
                new String[] { "低", "中", "高", "严格" });
        securityLevelComboBox.setSelectedIndex(1); // 默认中等
        panel.add(securityLevelComboBox, gbc);

        // 操作限制设置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel restrictionLabel = new JLabel("操作限制设置");
        restrictionLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(restrictionLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JCheckBox enableOpLogCheckBox = new JCheckBox("启用操作日志记录");
        enableOpLogCheckBox.setSelected(true);
        panel.add(enableOpLogCheckBox, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JCheckBox enableIPFilterCheckBox = new JCheckBox("启用IP过滤");
        panel.add(enableIPFilterCheckBox, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JCheckBox enablePermCheckCheckBox = new JCheckBox("启用权限检查");
        enablePermCheckCheckBox.setSelected(true);
        panel.add(enablePermCheckCheckBox, gbc);

        // 自动保护设置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JLabel autoProtectLabel = new JLabel("自动保护设置");
        autoProtectLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        panel.add(autoProtectLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        panel.add(new JLabel("最大失败尝试次数:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JTextField maxFailAttemptsField = new JTextField("5");
        panel.add(maxFailAttemptsField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        panel.add(new JLabel("封禁时长(分钟):"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        JTextField banDurationField = new JTextField("30");
        panel.add(banDurationField, gbc);

        // 保存按钮
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JButton saveSecurityBtn = new JButton("保存安全设置");
        saveSecurityBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        saveSecurityBtn.setForeground(new java.awt.Color(0, 128, 0));
        panel.add(saveSecurityBtn, gbc);

        return panel;
    }

    /**
     * 创建IP管理面板
     */
    private JPanel createIPManagementPanel() {
        JPanel panel = new JPanel(new java.awt.BorderLayout());
        panel.setBackground(java.awt.Color.WHITE);

        // 顶部操作面板
        JPanel topPanel = new JPanel(new java.awt.FlowLayout(java.awt.FlowLayout.LEFT));
        topPanel.setBackground(java.awt.Color.WHITE);

        topPanel.add(new JLabel("IP地址:"));
        JTextField ipField = new JTextField(15);
        topPanel.add(ipField);

        JButton addWhitelistBtn = new JButton("添加白名单");
        addWhitelistBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        addWhitelistBtn.setForeground(new java.awt.Color(0, 128, 0));
        topPanel.add(addWhitelistBtn);

        JButton addBlacklistBtn = new JButton("添加黑名单");
        addBlacklistBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        addBlacklistBtn.setForeground(new java.awt.Color(255, 0, 0));
        topPanel.add(addBlacklistBtn);

        panel.add(topPanel, java.awt.BorderLayout.NORTH);

        // 中间分栏面板
        JSplitPane ipSplitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        ipSplitPane.setDividerLocation(200);
        ipSplitPane.setResizeWeight(0.5);

        // 白名单
        JPanel whitelistPanel = new JPanel(new java.awt.BorderLayout());
        whitelistPanel.setBorder(BorderFactory.createTitledBorder("IP白名单"));
        DefaultListModel<String> whitelistModel = new DefaultListModel<>();
        whitelistModel.addElement("127.0.0.1");
        whitelistModel.addElement("*************");
        JList<String> whitelistList = new JList<>(whitelistModel);
        whitelistList.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        whitelistPanel.add(new JScrollPane(whitelistList), java.awt.BorderLayout.CENTER);

        // 黑名单
        JPanel blacklistPanel = new JPanel(new java.awt.BorderLayout());
        blacklistPanel.setBorder(BorderFactory.createTitledBorder("IP黑名单"));
        DefaultListModel<String> blacklistModel = new DefaultListModel<>();
        blacklistModel.addElement("*************");
        JList<String> blacklistList = new JList<>(blacklistModel);
        blacklistList.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        blacklistPanel.add(new JScrollPane(blacklistList), java.awt.BorderLayout.CENTER);

        ipSplitPane.setLeftComponent(whitelistPanel);
        ipSplitPane.setRightComponent(blacklistPanel);
        panel.add(ipSplitPane, java.awt.BorderLayout.CENTER);

        // 底部操作按钮
        JPanel bottomPanel = new JPanel(new java.awt.FlowLayout());
        bottomPanel.setBackground(java.awt.Color.WHITE);

        JButton removeSelectedBtn = new JButton("移除选中");
        removeSelectedBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        removeSelectedBtn.setForeground(new java.awt.Color(255, 0, 0));
        bottomPanel.add(removeSelectedBtn);

        JButton clearAllBtn = new JButton("清空列表");
        clearAllBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        clearAllBtn.setForeground(new java.awt.Color(255, 0, 0));
        bottomPanel.add(clearAllBtn);

        panel.add(bottomPanel, java.awt.BorderLayout.SOUTH);

        return panel;
    }

    /**
     * 创建游戏内GUI管理面板
     */
    private JPanel createInGameGUIPanel() {
        JPanel mainPanel = new JPanel(new java.awt.BorderLayout());
        mainPanel.setBackground(java.awt.Color.WHITE);

        // 创建左右分栏布局
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(400);
        splitPane.setResizeWeight(0.4);
        splitPane.setBorder(null);

        // 左侧面板 - 在线玩家选择
        JPanel leftPanel = createInGameGUILeftPanel();
        splitPane.setLeftComponent(leftPanel);

        // 右侧面板 - GUI功能管理
        JPanel rightPanel = createInGameGUIRightPanel();
        splitPane.setRightComponent(rightPanel);

        mainPanel.add(splitPane, java.awt.BorderLayout.CENTER);

        return mainPanel;
    }

    /**
     * 创建游戏内GUI左侧面板 - 在线玩家列表（与玩家管理一模一样）
     */
    private JPanel createInGameGUILeftPanel() {
        JPanel leftPanel = new JPanel(new java.awt.BorderLayout());
        leftPanel.setBackground(java.awt.Color.WHITE);
        leftPanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(), "在线玩家列表",
                javax.swing.border.TitledBorder.DEFAULT_JUSTIFICATION,
                javax.swing.border.TitledBorder.DEFAULT_POSITION,
                new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14)));

        // 创建多列玩家网格面板（与玩家管理完全一样）
        guiPlayersGridPanel = new PlayerGridPanel();
        JScrollPane scrollPane = new JScrollPane(guiPlayersGridPanel);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_NEVER);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setPreferredSize(new java.awt.Dimension(350, 450));
        leftPanel.add(scrollPane, java.awt.BorderLayout.CENTER);

        // 底部操作按钮（与玩家管理完全一样）
        JPanel buttonPanel = new JPanel(new java.awt.FlowLayout(java.awt.FlowLayout.CENTER, 10, 5));
        buttonPanel.setBackground(java.awt.Color.WHITE);

        JButton refreshBtn = new JButton("刷新在线玩家");
        refreshBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        refreshBtn.setPreferredSize(new java.awt.Dimension(120, 30));
        refreshBtn.addActionListener(e -> refreshGUIPlayersList());
        buttonPanel.add(refreshBtn);

        JButton clearBtn = new JButton("清除选择");
        clearBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        clearBtn.setPreferredSize(new java.awt.Dimension(100, 30));
        clearBtn.addActionListener(e -> {
            if (guiPlayersGridPanel != null) {
                guiPlayersGridPanel.clearSelection();
            }
            if (guiPlayerNameField != null) {
                guiPlayerNameField.setText("");
            }
        });
        buttonPanel.add(clearBtn);

        leftPanel.add(buttonPanel, java.awt.BorderLayout.SOUTH);

        // 初始刷新玩家列表
        refreshGUIPlayersList();

        // 启动定时器，每30秒自动刷新一次玩家列表
        javax.swing.Timer guiRefreshTimer = new javax.swing.Timer(30000, e -> refreshGUIPlayersList());
        guiRefreshTimer.start();

        return leftPanel;
    }

    /**
     * 创建游戏内GUI右侧面板
     */
    private JPanel createInGameGUIRightPanel() {
        JPanel rightPanel = new JPanel();
        rightPanel.setLayout(new java.awt.GridBagLayout());
        rightPanel.setBackground(java.awt.Color.WHITE);
        rightPanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createLineBorder(new java.awt.Color(200, 200, 200), 1),
                "游戏内GUI功能管理",
                javax.swing.border.TitledBorder.DEFAULT_JUSTIFICATION,
                javax.swing.border.TitledBorder.DEFAULT_POSITION,
                new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14),
                new java.awt.Color(60, 60, 60)));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(10, 10, 10, 10);
        gbc.anchor = java.awt.GridBagConstraints.WEST;

        // 手动输入玩家名称区域
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.fill = java.awt.GridBagConstraints.NONE;
        JLabel offlineLabel = new JLabel("离线玩家名称:");
        offlineLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        rightPanel.add(offlineLabel, gbc);

        gbc.gridx = 1;
        gbc.gridwidth = 1;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        guiPlayerNameField = new JTextField();
        guiPlayerNameField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        guiPlayerNameField.setPreferredSize(new java.awt.Dimension(300, 30));
        guiPlayerNameField.setToolTipText("如果玩家列表为空，可以手动输入玩家名称");
        guiPlayerNameField.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyTyped(java.awt.event.KeyEvent e) {
                if (!guiPlayerNameField.getText().trim().isEmpty()) {
                    if (guiPlayersGridPanel != null) {
                        guiPlayersGridPanel.clearSelection();
                    }
                }
            }
        });
        rightPanel.add(guiPlayerNameField, gbc);

        // GUI功能说明
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        JLabel infoLabel = new JLabel("<html><div style='width:400px;'>" +
                "<h3 style='color:#2E8B57;'>游戏内GUI功能说明</h3>" +
                "<p><b>装备管理界面：</b>玩家可以查看当前装备信息、套装状态等</p>" +
                "<p><b>套装预览界面：</b>玩家可以预览所有可用套装的属性和效果</p>" +
                "<p><b>特效设置界面：</b>玩家可以开关特效、查看当前特效状态</p>" +
                "<p><b>淬炼信息界面：</b>玩家可以查看装备和武器的淬炼等级</p>" +
                "</div></html>");
        infoLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        rightPanel.add(infoLabel, gbc);

        // GUI功能按钮区域
        gbc.gridwidth = 1;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        // 为选中玩家打开装备管理界面
        JButton openEquipmentGUIBtn = new JButton("打开装备管理界面");
        openEquipmentGUIBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        openEquipmentGUIBtn.setBackground(new java.awt.Color(46, 139, 87));
        openEquipmentGUIBtn.setForeground(java.awt.Color.WHITE);
        openEquipmentGUIBtn.setFocusPainted(false);
        openEquipmentGUIBtn.addActionListener(e -> openGUIForSelectedPlayer("equipment"));
        gbc.gridx = 0;
        gbc.gridy = 2;
        rightPanel.add(openEquipmentGUIBtn, gbc);

        // 为选中玩家打开套装预览界面
        JButton openSuitPreviewGUIBtn = new JButton("打开套装预览界面");
        openSuitPreviewGUIBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        openSuitPreviewGUIBtn.setBackground(new java.awt.Color(30, 144, 255));
        openSuitPreviewGUIBtn.setForeground(java.awt.Color.WHITE);
        openSuitPreviewGUIBtn.setFocusPainted(false);
        openSuitPreviewGUIBtn.addActionListener(e -> openGUIForSelectedPlayer("suit"));
        gbc.gridx = 1;
        gbc.gridy = 2;
        rightPanel.add(openSuitPreviewGUIBtn, gbc);

        // 为选中玩家打开特效设置界面
        JButton openEffectGUIBtn = new JButton("打开特效设置界面");
        openEffectGUIBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        openEffectGUIBtn.setBackground(new java.awt.Color(255, 140, 0));
        openEffectGUIBtn.setForeground(java.awt.Color.WHITE);
        openEffectGUIBtn.setFocusPainted(false);
        openEffectGUIBtn.addActionListener(e -> openGUIForSelectedPlayer("effect"));
        gbc.gridx = 0;
        gbc.gridy = 3;
        rightPanel.add(openEffectGUIBtn, gbc);

        // 为选中玩家打开淬炼信息界面
        JButton openEnhanceGUIBtn = new JButton("打开淬炼信息界面");
        openEnhanceGUIBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        openEnhanceGUIBtn.setBackground(new java.awt.Color(220, 20, 60));
        openEnhanceGUIBtn.setForeground(java.awt.Color.WHITE);
        openEnhanceGUIBtn.setFocusPainted(false);
        openEnhanceGUIBtn.addActionListener(e -> openGUIForSelectedPlayer("enhance"));
        gbc.gridx = 1;
        gbc.gridy = 3;
        rightPanel.add(openEnhanceGUIBtn, gbc);

        // 添加命令说明
        JLabel commandLabel = new JLabel("<html><div style='width:400px;'>" +
                "<h3 style='color:#B22222;'>相关命令</h3>" +
                "<p><b>/cuilian gui equipment</b> - 打开装备管理界面</p>" +
                "<p><b>/cuilian gui suit</b> - 打开套装预览界面</p>" +
                "<p><b>/cuilian gui effect</b> - 打开特效设置界面</p>" +
                "<p><b>/cuilian gui enhance</b> - 打开淬炼信息界面</p>" +
                "</div></html>");
        commandLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        gbc.gridx = 0;
        gbc.gridy = 4;
        gbc.gridwidth = 2;
        rightPanel.add(commandLabel, gbc);

        return rightPanel;
    }

    /**
     * 刷新游戏内GUI管理的玩家列表
     */
    private void refreshGUIPlayersList() {
        if (guiPlayersGridPanel == null) {
            return;
        }

        java.util.List<String> players = new java.util.ArrayList<>();
        try {
            for (org.bukkit.entity.Player player : org.bukkit.Bukkit.getOnlinePlayers()) {
                players.add(player.getName());
            }
        } catch (Exception e) {
            // 如果Bukkit不可用，添加测试数据
            for (int i = 1; i <= 10; i++) {
                players.add("测试玩家" + i);
            }
        }

        guiPlayersGridPanel.updatePlayers(players);
    }

    /**
     * 为选中的玩家打开指定的GUI界面
     */
    private void openGUIForSelectedPlayer(String guiType) {
        // 获取选中的玩家
        String tempSelectedPlayer = null;

        // 优先使用网格面板中选中的玩家
        if (guiPlayersGridPanel != null) {
            tempSelectedPlayer = guiPlayersGridPanel.getSelectedPlayer();
        }

        // 如果没有选中玩家，使用手动输入的玩家名称
        if (tempSelectedPlayer == null || tempSelectedPlayer.isEmpty()) {
            if (guiPlayerNameField != null) {
                tempSelectedPlayer = guiPlayerNameField.getText().trim();
            }
        }

        if (tempSelectedPlayer == null || tempSelectedPlayer.isEmpty()) {
            JOptionPane.showMessageDialog(null, "请先选择一个玩家或在输入框中输入玩家名称！");
            return;
        }

        // 创建final变量供lambda使用
        final String selectedPlayer = tempSelectedPlayer;

        try {
            org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(selectedPlayer);
            if (player != null && player.isOnline()) {
                // 先关闭玩家当前打开的界面，避免冲突
                player.closeInventory();

                // 延迟一小段时间再打开新界面，确保旧界面完全关闭
                javax.swing.Timer delayTimer = new javax.swing.Timer(100, e -> {
                    try {
                        switch (guiType) {
                            case "equipment":
                                cn.winde.cuilian.gui.InGameGUI.openEquipmentGUI(player);
                                javax.swing.SwingUtilities.invokeLater(() -> JOptionPane.showMessageDialog(null,
                                        "已为玩家 " + selectedPlayer + " 打开装备管理界面"));
                                break;
                            case "suit":
                                cn.winde.cuilian.gui.InGameGUI.openSuitPreviewGUI(player);
                                javax.swing.SwingUtilities.invokeLater(() -> JOptionPane.showMessageDialog(null,
                                        "已为玩家 " + selectedPlayer + " 打开套装预览界面"));
                                break;
                            case "effect":
                                cn.winde.cuilian.gui.InGameGUI.openEffectSettingsGUI(player);
                                javax.swing.SwingUtilities.invokeLater(() -> JOptionPane.showMessageDialog(null,
                                        "已为玩家 " + selectedPlayer + " 打开特效设置界面"));
                                break;
                            case "enhance":
                                cn.winde.cuilian.gui.InGameGUI.openEnhanceInfoGUI(player);
                                javax.swing.SwingUtilities.invokeLater(() -> JOptionPane.showMessageDialog(null,
                                        "已为玩家 " + selectedPlayer + " 打开淬炼信息界面"));
                                break;
                        }
                    } catch (Exception ex) {
                        javax.swing.SwingUtilities.invokeLater(
                                () -> JOptionPane.showMessageDialog(null, "打开GUI界面时出现错误: " + ex.getMessage()));
                    }
                });
                delayTimer.setRepeats(false);
                delayTimer.start();

            } else {
                JOptionPane.showMessageDialog(null, "玩家 " + selectedPlayer + " 不在线或不存在！");
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "操作失败: " + e.getMessage());
        }
    }

    /**
     * 重新加载所有配置文件
     */
    private void reloadAllConfigs() throws Exception {
        try {
            Cuilian instance = Cuilian.getInstance();
            if (instance != null) {
                // 重新加载主配置文件
                instance.reloadConfig();

                // 重新加载套装配置
                if (Cuilian.Suit != null) {
                    Cuilian.Suit = org.bukkit.configuration.file.YamlConfiguration.loadConfiguration(
                            new java.io.File(instance.getDataFolder(), "Suit.yml"));
                }

                // 重新加载武器配置
                if (Cuilian.Weapon != null) {
                    Cuilian.Weapon = org.bukkit.configuration.file.YamlConfiguration.loadConfiguration(
                            new java.io.File(instance.getDataFolder(), "Weapon.yml"));
                }

                // 重新加载配置到内存
                instance.loadyml();

                // 更新UI显示
                javax.swing.SwingUtilities.invokeLater(() -> {
                    // 刷新套装列表
                    loadAvailableSuits();
                    loadDeleteSuitList();
                    // 更新特效开关状态
                    updateEffectCheckboxState();
                });

                // 通知所有打开特效设置界面的玩家更新界面
                if (org.bukkit.Bukkit.getServer() != null) {
                    org.bukkit.Bukkit.getScheduler().runTaskLater(
                            cn.winde.cuilian.Cuilian.getInstance(), () -> {
                                cn.winde.cuilian.gui.InGameGUIListener.notifyConfigReload();
                            }, 2L);
                }

                System.out.println("所有配置文件已重新加载");
            } else {
                throw new Exception("插件实例不可用");
            }

        } catch (Exception e) {
            System.err.println("重新加载配置文件时出现错误: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 更新特效开关状态
     */
    private void updateEffectCheckboxState() {
        if (checkBox != null && Cuilian.config != null) {
            boolean effectsEnabled = Cuilian.config.getBoolean("effects", true);
            checkBox.setSelected(effectsEnabled);
        }

        // 同时更新TPS自动特效开关状态
        if (tpsAutoEffectCheckBox != null && Cuilian.config != null) {
            boolean tpsAutoEnabled = Cuilian.config.getBoolean("tps_auto_effect.enabled", false);
            tpsAutoEffectCheckBox.setSelected(tpsAutoEnabled);
        }

        // 更新TPS阈值显示
        if (tpsThresholdField != null && Cuilian.config != null) {
            double threshold = Cuilian.config.getDouble("tps_auto_effect.threshold", 15.0);
            tpsThresholdField.setText(String.valueOf(threshold));
        }

        // 更新TPS恢复延迟显示
        if (tpsDelayField != null && Cuilian.config != null) {
            int delay = Cuilian.config.getInt("tps_auto_effect.recovery_delay", 30);
            tpsDelayField.setText(String.valueOf(delay));
        }

        // 更新预览冷却时间显示
        if (previewCooldownField != null && Cuilian.config != null) {
            int cooldown = Cuilian.config.getInt("suit_preview.cooldown", 60);
            previewCooldownField.setText(String.valueOf(cooldown));
        }

        // 更新套装特效消息强制禁用开关状态
        if (forceDisableMessagesCheckBox != null && Cuilian.config != null) {
            boolean forceDisabled = Cuilian.config.getBoolean("suit_effect_messages.force_disabled", false);
            forceDisableMessagesCheckBox.setSelected(forceDisabled);
        }
    }

    /**
     * 保存TPS阈值设置
     */
    private void saveTpsThreshold() {
        try {
            String thresholdText = tpsThresholdField.getText().trim();
            double threshold = Double.parseDouble(thresholdText);

            if (threshold < 1.0 || threshold > 20.0) {
                JOptionPane.showMessageDialog(null, "TPS阈值必须在1.0到20.0之间！");
                tpsThresholdField.setText(String.valueOf(Cuilian.config.getDouble("tps_auto_effect.threshold", 15.0)));
                return;
            }

            Cuilian instance = Cuilian.getInstance();
            if (instance != null) {
                instance.getConfig().set("tps_auto_effect.threshold", threshold);
                instance.saveConfig();
                Cuilian.config.set("tps_auto_effect.threshold", threshold);

                System.out.println("TPS阈值已设置为: " + threshold);
            } else {
                JOptionPane.showMessageDialog(null, "插件实例不可用，无法保存配置");
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请输入有效的数字！");
            tpsThresholdField.setText(String.valueOf(Cuilian.config.getDouble("tps_auto_effect.threshold", 15.0)));
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "保存TPS阈值失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 保存TPS恢复延迟设置
     */
    private void saveTpsDelay() {
        try {
            String delayText = tpsDelayField.getText().trim();
            int delay = Integer.parseInt(delayText);

            if (delay < 5 || delay > 300) {
                JOptionPane.showMessageDialog(null, "恢复延迟必须在5到300秒之间！");
                tpsDelayField.setText(String.valueOf(Cuilian.config.getInt("tps_auto_effect.recovery_delay", 30)));
                return;
            }

            Cuilian instance = Cuilian.getInstance();
            if (instance != null) {
                instance.getConfig().set("tps_auto_effect.recovery_delay", delay);
                instance.saveConfig();
                Cuilian.config.set("tps_auto_effect.recovery_delay", delay);

                System.out.println("TPS恢复延迟已设置为: " + delay + "秒");
            } else {
                JOptionPane.showMessageDialog(null, "插件实例不可用，无法保存配置");
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请输入有效的数字！");
            tpsDelayField.setText(String.valueOf(Cuilian.config.getInt("tps_auto_effect.recovery_delay", 30)));
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "保存TPS恢复延迟失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 保存TPS恢复延迟设置
     */
    private void saveTpsRecoveryDelay() {
        try {
            String delayText = tpsRecoveryDelayField.getText().trim();
            int delay = Integer.parseInt(delayText);

            if (delay < 5 || delay > 300) {
                JOptionPane.showMessageDialog(null, "恢复延迟必须在5到300秒之间！");
                tpsRecoveryDelayField
                        .setText(String.valueOf(Cuilian.config.getInt("tps_auto_effect.recovery_delay", 10)));
                return;
            }

            Cuilian instance = Cuilian.getInstance();
            if (instance != null) {
                instance.getConfig().set("tps_auto_effect.recovery_delay", delay);
                instance.saveConfig();
                Cuilian.config.set("tps_auto_effect.recovery_delay", delay);

                System.out.println("TPS恢复延迟已设置为: " + delay + "秒");
            } else {
                JOptionPane.showMessageDialog(null, "插件实例不可用，无法保存配置");
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请输入有效的数字！");
            tpsRecoveryDelayField.setText(String.valueOf(Cuilian.config.getInt("tps_auto_effect.recovery_delay", 10)));
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "保存TPS恢复延迟失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 保存套装预览时间设置
     */
    private void savePreviewDuration() {
        try {
            String durationText = previewDurationField.getText().trim();
            int duration = Integer.parseInt(durationText);

            if (duration < 5 || duration > 300) {
                JOptionPane.showMessageDialog(null, "预览时间必须在5到300秒之间！");
                previewDurationField.setText(String.valueOf(Cuilian.config.getInt("suit_preview.duration", 30)));
                return;
            }

            Cuilian instance = Cuilian.getInstance();
            if (instance != null) {
                instance.getConfig().set("suit_preview.duration", duration);
                instance.saveConfig();
                Cuilian.config.set("suit_preview.duration", duration);

                System.out.println("套装预览时间已设置为: " + duration + "秒");
            } else {
                JOptionPane.showMessageDialog(null, "插件实例不可用，无法保存配置");
            }

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请输入有效的数字！");
            previewDurationField.setText(String.valueOf(Cuilian.config.getInt("suit_preview.duration", 30)));
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "保存预览时间失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 保存套装预览冷却时间设置
     */
    private void savePreviewCooldown() {
        try {
            String cooldownText = previewCooldownField.getText().trim();
            int cooldown = Integer.parseInt(cooldownText);

            if (cooldown < 0 || cooldown > 3600) {
                JOptionPane.showMessageDialog(null, "预览冷却时间必须在0到3600秒之间！");
                previewCooldownField.setText(String.valueOf(Cuilian.config.getInt("suit_preview.cooldown", 60)));
                return;
            }

            Cuilian instance = Cuilian.getInstance();
            if (instance != null) {
                instance.getConfig().set("suit_preview.cooldown", cooldown);
                instance.saveConfig();
                Cuilian.config.set("suit_preview.cooldown", cooldown);

                System.out.println("套装预览冷却时间已设置为: " + cooldown + "秒");
            } else {
                JOptionPane.showMessageDialog(null, "插件实例不可用，无法保存配置");
            }

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请输入有效的数字！");
            previewCooldownField.setText(String.valueOf(Cuilian.config.getInt("suit_preview.cooldown", 60)));
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "保存预览冷却时间失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 发送特效开关公告
     */
    private void sendEffectToggleAnnouncement(boolean enabled) {
        try {
            if (enabled) {
                // 特效启用公告
                org.bukkit.Bukkit.broadcastMessage("§6§l===========================================");
                org.bukkit.Bukkit.broadcastMessage("§a§l[特效] 服务器特效系统已启用！");
                org.bukkit.Bukkit.broadcastMessage("§e§l[公告] 所有套装特效现在可以正常显示");
                org.bukkit.Bukkit.broadcastMessage("§c§l[注意] 需要重新穿戴装备才能激活特效");
                org.bukkit.Bukkit.broadcastMessage("§7§l[提示] 脱下装备再重新穿上即可激活特效");
                org.bukkit.Bukkit.broadcastMessage("§6§l===========================================");

                // 给所有在线玩家发送标题提示
                for (org.bukkit.entity.Player player : org.bukkit.Bukkit.getOnlinePlayers()) {
                    player.sendTitle("§a§l特效已启用", "§e重新穿戴装备激活特效");
                }
            } else {
                // 特效禁用公告
                org.bukkit.Bukkit.broadcastMessage("§6§l===========================================");
                org.bukkit.Bukkit.broadcastMessage("§c§l[特效] 服务器特效系统已禁用！");
                org.bukkit.Bukkit.broadcastMessage("§e§l[公告] 所有套装特效已停止显示");
                org.bukkit.Bukkit.broadcastMessage("§7§l[系统] 管理员已暂时关闭特效功能");
                org.bukkit.Bukkit.broadcastMessage("§6§l===========================================");

                // 给所有在线玩家发送标题提示
                for (org.bukkit.entity.Player player : org.bukkit.Bukkit.getOnlinePlayers()) {
                    player.sendTitle("§c§l特效已禁用", "§7服务器已关闭特效显示");
                }
            }

            System.out.println("已发送特效" + (enabled ? "启用" : "禁用") + "公告给所有在线玩家");
        } catch (Exception e) {
            System.err.println("发送特效公告时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建底部面板
     */
    private JPanel createBottomPanel() {
        JPanel bottomPanel = new JPanel(new java.awt.FlowLayout(java.awt.FlowLayout.LEFT));
        bottomPanel.setBackground(java.awt.Color.WHITE);
        bottomPanel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));

        // 特效开关
        this.checkBox = new JCheckBox("启用特效");
        this.checkBox.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        // 从配置文件读取当前状态
        this.checkBox.setSelected(Cuilian.config.getBoolean("effects", true));
        this.checkBox.addActionListener(e -> {
            boolean enabled = checkBox.isSelected();
            try {
                // 直接更新配置文件
                Cuilian instance = Cuilian.getInstance();
                if (instance != null) {
                    instance.getConfig().set("effects", enabled);
                    instance.saveConfig();

                    // 更新内存中的配置
                    Cuilian.config.set("effects", enabled);

                    // 显示状态消息
                    String message = enabled ? "全服特效已启用！" : "全服特效已禁用！";
                    JOptionPane.showMessageDialog(null, message);

                    // 发送全服公告
                    sendEffectToggleAnnouncement(enabled);

                    // 如果禁用特效，清空所有玩家的特效
                    if (!enabled) {
                        Cuilian.lizi.clear();
                        System.out.println("已清空所有玩家特效");
                    }

                    System.out.println("特效全局开关已" + (enabled ? "启用" : "禁用"));
                } else {
                    JOptionPane.showMessageDialog(null, "插件实例不可用，无法保存配置");
                }
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(null, "保存配置失败: " + ex.getMessage());
                ex.printStackTrace();
            }
        });
        bottomPanel.add(this.checkBox);

        // TPS自动特效开关
        tpsAutoEffectCheckBox = new JCheckBox("TPS自动特效开关");
        tpsAutoEffectCheckBox.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        tpsAutoEffectCheckBox.setSelected(Cuilian.config.getBoolean("tps_auto_effect.enabled", false));
        tpsAutoEffectCheckBox.setToolTipText("当服务器TPS过低时自动禁用特效");
        tpsAutoEffectCheckBox.addActionListener(e -> {
            boolean enabled = tpsAutoEffectCheckBox.isSelected();
            try {
                Cuilian instance = Cuilian.getInstance();
                if (instance != null) {
                    instance.getConfig().set("tps_auto_effect.enabled", enabled);
                    instance.saveConfig();
                    Cuilian.config.set("tps_auto_effect.enabled", enabled);

                    // 启动或停止TPS监控器
                    if (enabled) {
                        cn.winde.cuilian.tps.TPSMonitor.startMonitoring();
                    } else {
                        cn.winde.cuilian.tps.TPSMonitor.stopMonitoring();
                    }

                    String message = enabled ? "TPS自动特效开关已启用！" : "TPS自动特效开关已禁用！";
                    JOptionPane.showMessageDialog(null, message);

                    System.out.println("TPS自动特效开关已" + (enabled ? "启用" : "禁用"));
                } else {
                    JOptionPane.showMessageDialog(null, "插件实例不可用，无法保存配置");
                }
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(null, "保存配置失败: " + ex.getMessage());
                ex.printStackTrace();
            }
        });
        bottomPanel.add(tpsAutoEffectCheckBox);

        // TPS阈值设置
        JLabel tpsLabel = new JLabel("TPS阈值:");
        tpsLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        bottomPanel.add(tpsLabel);

        tpsThresholdField = new JTextField(5);
        tpsThresholdField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        tpsThresholdField.setText(String.valueOf(Cuilian.config.getDouble("tps_auto_effect.threshold", 15.0)));
        tpsThresholdField.setToolTipText("当TPS低于此值时自动禁用特效");
        tpsThresholdField.addActionListener(e -> saveTpsThreshold());
        tpsThresholdField.addFocusListener(new java.awt.event.FocusAdapter() {
            @Override
            public void focusLost(java.awt.event.FocusEvent e) {
                saveTpsThreshold();
            }
        });
        bottomPanel.add(tpsThresholdField);

        // TPS恢复延迟设置
        JLabel recoveryDelayLabel = new JLabel("恢复延迟(秒):");
        recoveryDelayLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        bottomPanel.add(recoveryDelayLabel);

        tpsRecoveryDelayField = new JTextField(5);
        tpsRecoveryDelayField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        tpsRecoveryDelayField.setText(String.valueOf(Cuilian.config.getInt("tps_auto_effect.recovery_delay", 10)));
        tpsRecoveryDelayField.setToolTipText("TPS恢复正常后等待多少秒再重新启用特效");
        tpsRecoveryDelayField.addActionListener(e -> saveTpsRecoveryDelay());
        tpsRecoveryDelayField.addFocusListener(new java.awt.event.FocusAdapter() {
            @Override
            public void focusLost(java.awt.event.FocusEvent e) {
                saveTpsRecoveryDelay();
            }
        });
        bottomPanel.add(tpsRecoveryDelayField);

        // 套装预览时间设置
        JLabel previewDurationLabel = new JLabel("预览时间(秒):");
        previewDurationLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        bottomPanel.add(previewDurationLabel);

        previewDurationField = new JTextField(5);
        previewDurationField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        previewDurationField.setText(String.valueOf(Cuilian.config.getInt("suit_preview.duration", 30)));
        previewDurationField.setToolTipText("套装预览特效持续时间");
        previewDurationField.addActionListener(e -> savePreviewDuration());
        previewDurationField.addFocusListener(new java.awt.event.FocusAdapter() {
            @Override
            public void focusLost(java.awt.event.FocusEvent e) {
                savePreviewDuration();
            }
        });
        bottomPanel.add(previewDurationField);

        // 套装预览冷却时间设置
        JLabel previewCooldownLabel = new JLabel("预览冷却(秒):");
        previewCooldownLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        bottomPanel.add(previewCooldownLabel);

        previewCooldownField = new JTextField(5);
        previewCooldownField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        previewCooldownField.setText(String.valueOf(Cuilian.config.getInt("suit_preview.cooldown", 60)));
        previewCooldownField.setToolTipText("套装预览功能冷却时间，设置为0表示无冷却");
        previewCooldownField.addActionListener(e -> savePreviewCooldown());
        previewCooldownField.addFocusListener(new java.awt.event.FocusAdapter() {
            @Override
            public void focusLost(java.awt.event.FocusEvent e) {
                savePreviewCooldown();
            }
        });
        bottomPanel.add(previewCooldownField);

        // 套装特效消息强制禁用开关
        this.forceDisableMessagesCheckBox = new JCheckBox("强制禁用套装消息");
        this.forceDisableMessagesCheckBox.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        this.forceDisableMessagesCheckBox
                .setSelected(Cuilian.config.getBoolean("suit_effect_messages.force_disabled", false));
        this.forceDisableMessagesCheckBox.addActionListener(e -> {
            boolean forceDisabled = forceDisableMessagesCheckBox.isSelected();
            try {
                Cuilian instance = Cuilian.getInstance();
                if (instance != null) {
                    instance.getConfig().set("suit_effect_messages.force_disabled", forceDisabled);
                    instance.saveConfig();
                    Cuilian.config.set("suit_effect_messages.force_disabled", forceDisabled);

                    String message = forceDisabled ? "套装特效消息已强制禁用！玩家无法在UI中开启消息。" : "套装特效消息强制禁用已关闭！玩家可以在UI中控制消息显示。";
                    JOptionPane.showMessageDialog(null, message);

                    // 延迟通知所有打开特效设置界面的玩家更新界面
                    if (org.bukkit.Bukkit.getServer() != null) {
                        org.bukkit.Bukkit.getScheduler().runTaskLater(
                                cn.winde.cuilian.Cuilian.getInstance(), () -> {
                                    System.out.println("[Mygui] 强制禁用开关状态改变，通知界面更新: " + forceDisabled);
                                    cn.winde.cuilian.gui.InGameGUIListener.notifyConfigReload();
                                }, 1L);
                    }
                }
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(null, "保存配置失败: " + ex.getMessage());
                ex.printStackTrace();
            }
        });
        bottomPanel.add(this.forceDisableMessagesCheckBox);

        // 玩家装备预览按钮
        JButton playerPreviewBtn = new JButton("玩家装备预览");
        playerPreviewBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        playerPreviewBtn.addActionListener(e -> openPlayerEquipmentPreview());
        bottomPanel.add(playerPreviewBtn);

        // 游戏风格装备预览按钮
        JButton gameStylePreviewBtn = new JButton("游戏风格预览");
        gameStylePreviewBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        gameStylePreviewBtn.setBackground(new java.awt.Color(72, 61, 139));
        gameStylePreviewBtn.setForeground(java.awt.Color.WHITE);
        gameStylePreviewBtn.setFocusPainted(false);
        gameStylePreviewBtn.setToolTipText("直接打开游戏风格的装备预览界面，显示详细特效信息");
        gameStylePreviewBtn.addActionListener(e -> openGameStyleEquipmentPreview());
        bottomPanel.add(gameStylePreviewBtn);

        // 重载按钮
        JButton reloadBtn = new JButton("重载配置");
        reloadBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        reloadBtn.addActionListener(e -> {
            try {
                // 直接重新加载配置文件
                reloadAllConfigs();
                JOptionPane.showMessageDialog(null, "配置文件重载成功！");
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(null, "配置文件重载失败: " + ex.getMessage());
                ex.printStackTrace();
            }
        });
        bottomPanel.add(reloadBtn);

        // 重载贴图按钮
        JButton reloadTexturesBtn = new JButton("重载贴图");
        reloadTexturesBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        reloadTexturesBtn.setBackground(new java.awt.Color(34, 139, 34));
        reloadTexturesBtn.setForeground(java.awt.Color.WHITE);
        reloadTexturesBtn.setFocusPainted(false);
        reloadTexturesBtn.setToolTipText("重新扫描和加载装备贴图文件");
        reloadTexturesBtn.addActionListener(e -> {
            try {
                cn.winde.cuilian.texture.TextureManager.reloadTextures();
                String stats = cn.winde.cuilian.texture.TextureManager.getStatistics();
                JOptionPane.showMessageDialog(null, "装备贴图重载成功！\n" + stats);
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(null, "装备贴图重载失败: " + ex.getMessage());
                ex.printStackTrace();
            }
        });
        bottomPanel.add(reloadTexturesBtn);

        return bottomPanel;
    }

    /**
     * 打开玩家装备预览窗口
     */
    private void openPlayerEquipmentPreview() {
        // 创建玩家选择对话框
        PlayerSelectionDialog selectionDialog = new PlayerSelectionDialog(this);
        selectionDialog.setVisible(true);

        String selectedPlayer = selectionDialog.getSelectedPlayer();
        if (selectedPlayer == null || selectedPlayer.trim().isEmpty()) {
            return;
        }

        // 检查玩家是否在线
        org.bukkit.entity.Player onlinePlayer = org.bukkit.Bukkit.getPlayer(selectedPlayer);

        if (onlinePlayer != null && onlinePlayer.isOnline()) {
            // 在线玩家 - 使用实时数据
            PlayerEquipmentPreviewDialog dialog = new PlayerEquipmentPreviewDialog(this, onlinePlayer, true);
            dialog.setVisible(true);
        } else {
            // 离线玩家 - 使用离线数据
            org.bukkit.OfflinePlayer offlinePlayer = org.bukkit.Bukkit.getOfflinePlayer(selectedPlayer);
            if (offlinePlayer.hasPlayedBefore()) {
                PlayerEquipmentPreviewDialog dialog = new PlayerEquipmentPreviewDialog(this, offlinePlayer);
                dialog.setVisible(true);
            } else {
                JOptionPane.showMessageDialog(this,
                        "玩家 " + selectedPlayer + " 从未进入过服务器！",
                        "错误",
                        JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * 打开游戏风格装备预览窗口
     */
    private void openGameStyleEquipmentPreview() {
        // 创建玩家选择对话框
        PlayerSelectionDialog selectionDialog = new PlayerSelectionDialog(this);
        selectionDialog.setVisible(true);

        String selectedPlayer = selectionDialog.getSelectedPlayer();
        if (selectedPlayer == null || selectedPlayer.trim().isEmpty()) {
            return;
        }

        // 检查玩家是否在线
        org.bukkit.entity.Player onlinePlayer = org.bukkit.Bukkit.getPlayer(selectedPlayer);

        if (onlinePlayer != null && onlinePlayer.isOnline()) {
            // 在线玩家 - 直接打开游戏风格预览
            GameStyleEquipmentPreview gamePreview = new GameStyleEquipmentPreview(this, onlinePlayer);
            gamePreview.setVisible(true);
        } else {
            // 离线玩家 - 使用离线数据
            org.bukkit.OfflinePlayer offlinePlayer = org.bukkit.Bukkit.getOfflinePlayer(selectedPlayer);
            if (offlinePlayer.hasPlayedBefore()) {
                GameStyleEquipmentPreview gamePreview = new GameStyleEquipmentPreview(this, offlinePlayer);
                gamePreview.setVisible(true);
            } else {
                JOptionPane.showMessageDialog(this,
                        "玩家 " + selectedPlayer + " 从未进入过服务器！",
                        "错误",
                        JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * 初始化数据
     */
    private void initializeData() {
        try {
            // 先加载保存的监控数据
            loadMonitoringData();

            // 如果没有保存的数据，则初始化默认数据
            if (stoneUsageStats.isEmpty()) {
                initializeDefaultStatsData();
            }

            // 初始化性能统计数据
            updatePerformanceStats();

            System.out.println("数据初始化完成");
        } catch (Exception e) {
            System.err.println("初始化数据时出现错误: " + e.getMessage());
            e.printStackTrace();
        }

        // 添加窗口监听器，确保在窗口显示时刷新在线玩家列表
        this.addWindowListener(new java.awt.event.WindowAdapter() {
            @Override
            public void windowOpened(java.awt.event.WindowEvent e) {
                // 窗口打开时，延迟刷新在线玩家列表
                javax.swing.SwingUtilities.invokeLater(() -> {
                    if (charmOnlinePlayersModel != null) {
                        // 重新初始化符咒在线玩家列表
                        initializeCharmOnlinePlayersList();
                    }
                });
            }

            @Override
            public void windowActivated(java.awt.event.WindowEvent e) {
                // 窗口激活时，也刷新一次在线玩家列表
                if (charmOnlinePlayersModel != null && charmOnlinePlayersModel.getSize() == 0) {
                    // 如果列表为空，重新加载
                    javax.swing.SwingUtilities.invokeLater(() -> {
                        refreshCharmOnlinePlayersList();
                    });
                }
            }
        });
    }

    /**
     * 初始化默认统计数据
     */
    private void initializeDefaultStatsData() {
        try {
            // 确保淬炼石统计默认为0
            if (stoneUsageStats.isEmpty()) {
                stoneUsageStats.put("普通", 0);
                stoneUsageStats.put("中等", 0);
                stoneUsageStats.put("高等", 0);
                stoneUsageStats.put("上等", 0);
                stoneUsageStats.put("符咒", 0);
                stoneUsageStats.put("吞噬", 0);
            }

            // 刷新显示
            refreshStatsDisplay();
        } catch (Exception e) {
            System.err.println("初始化默认统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 加载监控数据
     */
    private void loadMonitoringData() {
        try {
            java.io.File dataFile = new java.io.File(MONITORING_DATA_FILE);
            if (!dataFile.exists()) {
                System.out.println("监控数据文件不存在，使用默认值");
                return;
            }

            org.bukkit.configuration.file.YamlConfiguration config = org.bukkit.configuration.file.YamlConfiguration
                    .loadConfiguration(dataFile);

            // 加载套装统计数据
            if (config.contains("suitUsageStats")) {
                suitUsageStats.clear();
                org.bukkit.configuration.ConfigurationSection suitSection = config
                        .getConfigurationSection("suitUsageStats");
                if (suitSection != null) {
                    for (String key : suitSection.getKeys(false)) {
                        suitUsageStats.put(key, suitSection.getInt(key, 0));
                    }
                }
            }

            // 加载淬炼石统计数据
            if (config.contains("stoneUsageStats")) {
                stoneUsageStats.clear();
                org.bukkit.configuration.ConfigurationSection stoneSection = config
                        .getConfigurationSection("stoneUsageStats");
                if (stoneSection != null) {
                    for (String key : stoneSection.getKeys(false)) {
                        stoneUsageStats.put(key, stoneSection.getInt(key, 0));
                    }
                }
            }

            // 加载玩家统计数据
            if (config.contains("playerStats")) {
                playerStats.clear();
                org.bukkit.configuration.ConfigurationSection playerSection = config
                        .getConfigurationSection("playerStats");
                if (playerSection != null) {
                    for (String key : playerSection.getKeys(false)) {
                        Object value = playerSection.get(key);
                        playerStats.put(key, value);
                    }
                }
            }

            // 加载性能统计数据
            if (config.contains("performanceStats")) {
                performanceStats.clear();
                org.bukkit.configuration.ConfigurationSection perfSection = config
                        .getConfigurationSection("performanceStats");
                if (perfSection != null) {
                    for (String key : perfSection.getKeys(false)) {
                        Object value = perfSection.get(key);
                        performanceStats.put(key, value);
                    }
                }
            }

            // 加载TPS历史数据
            if (config.contains("tpsHistory")) {
                java.util.List<Double> tpsList = config.getDoubleList("tpsHistory");
                for (int i = 0; i < Math.min(tpsList.size(), tpsHistory.length); i++) {
                    tpsHistory[i] = tpsList.get(i);
                }
                tpsHistoryIndex = config.getInt("tpsHistoryIndex", 0);
            }

            System.out.println("监控数据加载完成");
        } catch (Exception e) {
            System.err.println("加载监控数据失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 保存监控数据
     */
    private void saveMonitoringData() {
        try {
            java.io.File dataFile = new java.io.File(MONITORING_DATA_FILE);

            // 确保目录存在
            dataFile.getParentFile().mkdirs();

            org.bukkit.configuration.file.YamlConfiguration config = new org.bukkit.configuration.file.YamlConfiguration();

            // 保存套装统计数据
            if (!suitUsageStats.isEmpty()) {
                for (java.util.Map.Entry<String, Integer> entry : suitUsageStats.entrySet()) {
                    config.set("suitUsageStats." + entry.getKey(), entry.getValue());
                }
            }

            // 保存淬炼石统计数据
            if (!stoneUsageStats.isEmpty()) {
                for (java.util.Map.Entry<String, Integer> entry : stoneUsageStats.entrySet()) {
                    config.set("stoneUsageStats." + entry.getKey(), entry.getValue());
                }
            }

            // 保存玩家统计数据
            if (!playerStats.isEmpty()) {
                for (java.util.Map.Entry<String, Object> entry : playerStats.entrySet()) {
                    config.set("playerStats." + entry.getKey(), entry.getValue());
                }
            }

            // 保存性能统计数据
            if (!performanceStats.isEmpty()) {
                for (java.util.Map.Entry<String, Object> entry : performanceStats.entrySet()) {
                    config.set("performanceStats." + entry.getKey(), entry.getValue());
                }
            }

            // 保存TPS历史数据
            java.util.List<Double> tpsList = new java.util.ArrayList<>();
            for (double tps : tpsHistory) {
                tpsList.add(tps);
            }
            config.set("tpsHistory", tpsList);
            config.set("tpsHistoryIndex", tpsHistoryIndex);

            // 保存时间戳
            config.set("lastSaved", System.currentTimeMillis());

            config.save(dataFile);
            // 移除频繁的保存日志信息
        } catch (Exception e) {
            System.err.println("保存监控数据失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 启动自动保存任务
     */
    private void startAutoSaveTask() {
        try {
            // 创建自动保存定时器，每5分钟保存一次
            autoSaveTimer = new javax.swing.Timer(300000, e -> {
                try {
                    // 自动保存监控数据
                    saveMonitoringData();
                } catch (Exception ex) {
                }
            });

            autoSaveTimer.start();

        } catch (Exception e) {
        }
    }

    /**
     * 停止自动保存任务
     */
    private void stopAutoSaveTask() {
        try {
            if (autoSaveTimer != null && autoSaveTimer.isRunning()) {
                autoSaveTimer.stop();
            }
        } catch (Exception e) {
        }
    }

    /**
     * 启动自动更新监控数据任务
     */
    private void startAutoUpdateTask() {
        try {
            // 创建自动更新定时器，每1秒更新一次监控数据
            autoUpdateTimer = new javax.swing.Timer(1000, e -> {
                try {
                    // 自动更新实时监控数据
                    updateRealTimeMonitoringData();
                } catch (Exception ex) {
                    // 忽略错误，避免影响其他功能
                }
            });

            autoUpdateTimer.start();
            System.out.println("自动更新监控数据任务已启动，每1秒更新一次");

        } catch (Exception e) {
            System.err.println("启动自动更新任务失败: " + e.getMessage());
        }
    }

    /**
     * 停止自动更新监控数据任务
     */
    private void stopAutoUpdateTask() {
        try {
            if (autoUpdateTimer != null && autoUpdateTimer.isRunning()) {
                autoUpdateTimer.stop();
                System.out.println("自动更新监控数据任务已停止");
            }
        } catch (Exception e) {
            System.err.println("停止自动更新任务失败: " + e.getMessage());
        }
    }

    /**
     * 多列玩家网格面板，支持自动换列和滚动
     */
    private static class PlayerGridPanel extends JPanel {
        private static final int PLAYER_BUTTON_WIDTH = 130;
        private static final int PLAYER_BUTTON_HEIGHT = 32; // 固定按钮高度，适应28px头颅
        private static final int SPACING = 5;

        private final java.util.List<PlayerButton> playerButtons = new java.util.ArrayList<>();
        private String selectedPlayer = null;

        public PlayerGridPanel() {
            // 使用null布局，手动控制组件位置以实现列优先排列
            setLayout(null);
            setBackground(java.awt.Color.WHITE);
            setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        }

        /**
         * 更新玩家列表
         */
        public void updatePlayers(java.util.List<String> players) {
            // 清除现有按钮
            playerButtons.clear();
            selectedPlayer = null;

            // 创建新的玩家按钮
            for (String playerName : players) {
                PlayerButton playerButton = new PlayerButton(playerName);
                playerButtons.add(playerButton);
            }

            // 重新布局所有按钮
            relayoutButtons();
        }

        /**
         * 获取选中的玩家
         */
        public String getSelectedPlayer() {
            return selectedPlayer;
        }

        /**
         * 清除选择
         */
        public void clearSelection() {
            selectedPlayer = null;
            for (PlayerButton button : playerButtons) {
                button.setSelected(false);
            }
        }

        /**
         * 添加单个玩家到列表
         */
        public void addPlayer(String playerName) {
            // 检查玩家是否已存在
            for (PlayerButton button : playerButtons) {
                if (button.playerName.equals(playerName)) {
                    return; // 玩家已存在，不重复添加
                }
            }

            // 创建新的玩家按钮
            PlayerButton playerButton = new PlayerButton(playerName);
            playerButtons.add(playerButton);

            // 重新布局所有按钮
            relayoutButtons();
        }

        /**
         * 从列表中移除单个玩家
         */
        public void removePlayer(String playerName) {
            // 查找并移除玩家按钮
            PlayerButton toRemove = null;
            for (PlayerButton button : playerButtons) {
                if (button.playerName.equals(playerName)) {
                    toRemove = button;
                    break;
                }
            }

            if (toRemove != null) {
                // 从列表和面板中移除
                playerButtons.remove(toRemove);
                remove(toRemove);

                // 如果移除的是当前选中的玩家，清除选择
                if (playerName.equals(selectedPlayer)) {
                    selectedPlayer = null;
                }

                // 重新布局剩余按钮
                relayoutButtons();
            }
        }

        /**
         * 重新布局所有按钮
         */
        private void relayoutButtons() {
            // 清除所有组件
            removeAll();

            if (playerButtons.isEmpty()) {
                // 显示无玩家在线的提示
                JLabel noPlayersLabel = new JLabel("暂无在线玩家", JLabel.CENTER);
                noPlayersLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
                noPlayersLabel.setForeground(java.awt.Color.GRAY);
                noPlayersLabel.setBounds(50, 100, 200, 30);
                add(noPlayersLabel);

                // 设置面板的首选大小
                setPreferredSize(new java.awt.Dimension(280, 580));
            } else {
                // 按列优先排列玩家按钮，基于底部边界自动换列
                int currentColumn = 0;
                int currentY = SPACING;

                for (PlayerButton playerButton : playerButtons) {
                    // 检查当前列是否已经有16个按钮
                    // 计算当前列的按钮数量
                    int buttonsInCurrentColumn = (currentY - SPACING) / (PLAYER_BUTTON_HEIGHT + SPACING);
                    if (buttonsInCurrentColumn >= 16) {
                        // 换到下一列
                        currentColumn++;
                        currentY = SPACING;
                    }

                    // 计算实际坐标
                    int x = SPACING + currentColumn * (PLAYER_BUTTON_WIDTH + SPACING);
                    int y = currentY;

                    // 设置按钮位置和大小
                    playerButton.setBounds(x, y, PLAYER_BUTTON_WIDTH, PLAYER_BUTTON_HEIGHT);
                    add(playerButton);

                    // 更新下一个按钮的Y坐标
                    currentY += PLAYER_BUTTON_HEIGHT + SPACING;
                }

                // 计算并设置面板的首选大小，支持水平滚动
                int totalWidth = SPACING + (currentColumn + 1) * (PLAYER_BUTTON_WIDTH + SPACING);
                setPreferredSize(new java.awt.Dimension(totalWidth, 580));
            }

            revalidate();
            repaint();
        }

        /**
         * 玩家按钮类
         */
        private class PlayerButton extends JPanel {
            private final String playerName;
            private boolean selected = false;
            private ImageIcon headIcon = null;

            public PlayerButton(String playerName) {
                this.playerName = playerName;
                setLayout(new java.awt.FlowLayout(java.awt.FlowLayout.LEFT, 4, 2));
                setBorder(BorderFactory.createRaisedBevelBorder());
                setBackground(java.awt.Color.WHITE);
                setCursor(java.awt.Cursor.getPredefinedCursor(java.awt.Cursor.HAND_CURSOR));

                // 异步加载玩家头颅
                loadPlayerHead();

                // 添加点击事件
                addMouseListener(new MouseAdapter() {
                    @Override
                    public void mouseClicked(MouseEvent e) {
                        if (e.getClickCount() == 1) {
                            // 单击切换选择状态
                            toggleSelection();
                        }
                    }

                    @Override
                    public void mouseEntered(MouseEvent e) {
                        if (!selected) {
                            setBackground(new java.awt.Color(230, 230, 230));
                        }
                    }

                    @Override
                    public void mouseExited(MouseEvent e) {
                        if (!selected) {
                            setBackground(java.awt.Color.WHITE);
                        }
                    }
                });

                updateDisplay();
            }

            private void toggleSelection() {
                if (selected) {
                    // 如果当前已选中，则取消选择
                    setSelected(false);
                    selectedPlayer = null;

                    // 清空离线玩家输入框
                    if (instance != null && instance.offlinePlayerField != null) {
                        instance.offlinePlayerField.setText("");
                    }
                    // 清空符咒管理的离线玩家输入框
                    if (instance != null && instance.charmTargetPlayerField != null) {
                        instance.charmTargetPlayerField.setText("");
                    }
                } else {
                    // 如果当前未选中，则选中当前玩家
                    selectPlayer();
                }
            }

            private void selectPlayer() {
                // 清除其他按钮的选择状态
                for (PlayerButton button : playerButtons) {
                    button.setSelected(false);
                }

                // 设置当前按钮为选中状态
                setSelected(true);
                selectedPlayer = playerName;

                // 更新玩家管理的离线玩家输入框
                if (instance != null && instance.offlinePlayerField != null) {
                    instance.offlinePlayerField.setText(playerName);
                }
                // 更新符咒管理的离线玩家输入框
                if (instance != null && instance.charmTargetPlayerField != null) {
                    instance.charmTargetPlayerField.setText(playerName);
                }
            }

            public void setSelected(boolean selected) {
                this.selected = selected;
                if (selected) {
                    setBackground(new java.awt.Color(173, 216, 230)); // 浅蓝色
                    setBorder(BorderFactory.createLoweredBevelBorder());
                } else {
                    setBackground(java.awt.Color.WHITE);
                    setBorder(BorderFactory.createRaisedBevelBorder());
                }
            }

            private void loadPlayerHead() {
                // 异步加载玩家头颅
                CompletableFuture.supplyAsync(() -> {
                    try {
                        // 使用 minotar.net 网站通过玩家名查询皮肤
                        String url = "https://minotar.net/avatar/" + playerName + "/28";
                        URL imageUrl = new URL(url);
                        BufferedImage image = ImageIO.read(imageUrl);
                        if (image != null) {
                            Image scaledImage = image.getScaledInstance(28, 28, Image.SCALE_SMOOTH);
                            return new ImageIcon(scaledImage);
                        }
                    } catch (Exception e) {
                        // 网络错误时使用默认头颅
                        System.out.println("加载玩家头颅失败: " + playerName + " - " + e.getMessage());
                    }
                    return createDefaultHead();
                }).thenAccept(icon -> {
                    javax.swing.SwingUtilities.invokeLater(() -> {
                        headIcon = icon;
                        updateDisplay();
                    });
                });
            }

            private ImageIcon createDefaultHead() {
                try {
                    BufferedImage defaultHead = new BufferedImage(28, 28, BufferedImage.TYPE_INT_ARGB);
                    Graphics2D g2d = defaultHead.createGraphics();
                    g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

                    // 绘制简单的头颅形状
                    g2d.setColor(new Color(139, 69, 19)); // 棕色皮肤
                    g2d.fillRect(0, 0, 28, 28);

                    // 绘制眼睛
                    g2d.setColor(Color.BLACK);
                    g2d.fillRect(7, 8, 3, 3);
                    g2d.fillRect(18, 8, 3, 3);

                    // 绘制嘴巴
                    g2d.fillRect(11, 18, 6, 2);

                    g2d.dispose();
                    return new ImageIcon(defaultHead);
                } catch (Exception e) {
                    return null;
                }
            }

            private void updateDisplay() {
                removeAll();

                // 添加头颅图标
                if (headIcon != null) {
                    JLabel iconLabel = new JLabel(headIcon);
                    add(iconLabel);
                }

                // 添加玩家名称
                JLabel nameLabel = new JLabel(playerName);
                nameLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
                add(nameLabel);

                revalidate();
                repaint();
            }
        }
    }

    /**
     * 清空当前星级的所有特效
     */
    private void clearAllEffectsForLevel() {
        String selectedLevel = (String) this.effectStarLevelComboBox.getSelectedItem();
        if (selectedLevel == null || selectedLevel.equals("选择星级")) {
            JOptionPane.showMessageDialog(null, "请先选择星级！");
            return;
        }

        int result = JOptionPane.showConfirmDialog(null,
                "确定要清空星级 " + selectedLevel + " 的所有特效吗？\n此操作会立即保存到配置文件！",
                "确认清空",
                JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            try {
                // 清空界面列表
                this.currentEffectsModel.clear();

                // 立即保存到配置文件
                String levelStr = selectedLevel.replace("星", "");
                String configPath = "eff.leve" + levelStr + ".effects";

                // 设置为空列表
                Cuilian.config.set(configPath, new java.util.ArrayList<>());

                // 保存配置文件
                Cuilian.config.save(Cuilian.filess);

                this.statusLabel.setText("已清空星级 " + selectedLevel + " 的所有特效并保存到配置文件");
                this.statusLabel.setForeground(new java.awt.Color(0, 128, 0));

                JOptionPane.showMessageDialog(null, "星级 " + selectedLevel + " 的特效已清空并保存到配置文件！");

            } catch (Exception e) {
                this.statusLabel.setText("清空特效失败: " + e.getMessage());
                this.statusLabel.setForeground(new java.awt.Color(255, 0, 0));
                JOptionPane.showMessageDialog(null, "清空特效失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 保存所有特效配置
     */
    private void saveAllEffectConfigurations() {
        try {
            // 保存当前星级的特效配置到config.yml
            String selectedLevel = (String) this.effectStarLevelComboBox.getSelectedItem();
            if (selectedLevel == null || selectedLevel.equals("选择星级")) {
                JOptionPane.showMessageDialog(null, "请先选择要保存的星级！");
                return;
            }

            // 提取星级数字
            String levelStr = selectedLevel.replace("星", "");

            // 收集当前特效列表的数据
            java.util.List<java.util.Map<String, Object>> effectsList = new java.util.ArrayList<>();

            for (int i = 0; i < this.currentEffectsModel.getSize(); i++) {
                String effectText = this.currentEffectsModel.getElementAt(i);

                // 解析特效文本: "type - 中文 [启用/禁用] (color1,color2,color3)"
                String[] parts = effectText.split(" \\[");
                if (parts.length >= 2) {
                    String typeDisplay = parts[0];
                    // 提取英文部分作为实际的特效类型
                    String type = typeDisplay.split(" - ")[0];

                    String[] statusAndColors = parts[1].split("\\] \\(");
                    if (statusAndColors.length >= 2) {
                        boolean enabled = statusAndColors[0].equals("启用");
                        String colorsStr = statusAndColors[1].replace(")", "");
                        String[] colors = colorsStr.split(",");

                        java.util.Map<String, Object> effectMap = new java.util.HashMap<>();
                        effectMap.put("type", type);
                        effectMap.put("enabled", enabled);
                        effectMap.put("colore1", colors.length > 0 ? colors[0] : "红");
                        effectMap.put("colore2", colors.length > 1 ? colors[1] : "橙");
                        effectMap.put("colore3", colors.length > 2 ? colors[2] : "黄");

                        effectsList.add(effectMap);
                    }
                }
            }

            // 保存到配置文件
            String configPath = "eff.leve" + levelStr + ".effects";
            Cuilian.config.set(configPath, effectsList);

            // 保存文件
            Cuilian.config.save(Cuilian.filess);

            this.statusLabel.setText("特效配置保存成功！");
            this.statusLabel.setForeground(new java.awt.Color(0, 128, 0));
            JOptionPane.showMessageDialog(null, "星级 " + selectedLevel + " 的特效配置已保存到config.yml！");
        } catch (Exception e) {
            this.statusLabel.setText("保存失败: " + e.getMessage());
            this.statusLabel.setForeground(new java.awt.Color(255, 0, 0));
            JOptionPane.showMessageDialog(null, "保存特效配置失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建新星级
     */
    private void createNewStarLevel() {
        String levelText = this.newStarLevelField.getText().trim();
        if (levelText.isEmpty()) {
            JOptionPane.showMessageDialog(null, "请输入星级数字！");
            return;
        }

        try {
            int level = Integer.parseInt(levelText);
            if (level <= 0) {
                JOptionPane.showMessageDialog(null, "星级必须是正整数！");
                return;
            }

            // 检查星级是否已存在
            String configPath = "eff.leve" + level;
            if (Cuilian.config.contains(configPath)) {
                int result = JOptionPane.showConfirmDialog(null,
                        "星级 " + level + " 已存在，是否覆盖？",
                        "确认覆盖",
                        JOptionPane.YES_NO_OPTION);
                if (result != JOptionPane.YES_OPTION) {
                    return;
                }
            }

            // 创建新星级配置
            Cuilian.config.set(configPath + ".effects", new java.util.ArrayList<>());

            // 保存配置文件
            Cuilian.config.save(Cuilian.filess);

            // 更新星级下拉框
            refreshStarLevelComboBox();

            // 选择新创建的星级
            this.effectStarLevelComboBox.setSelectedItem(level + "星");

            // 清空输入框
            this.newStarLevelField.setText("");

            this.statusLabel.setText("星级 " + level + " 创建成功！");
            this.statusLabel.setForeground(new java.awt.Color(0, 128, 0));
            JOptionPane.showMessageDialog(null, "星级 " + level + " 创建成功！");

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请输入有效的数字！");
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "创建星级失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 删除选中的星级
     */
    private void deleteSelectedStarLevel() {
        String selectedLevel = (String) this.effectStarLevelComboBox.getSelectedItem();
        if (selectedLevel == null || selectedLevel.equals("选择星级")) {
            JOptionPane.showMessageDialog(null, "请先选择要删除的星级！");
            return;
        }

        int result = JOptionPane.showConfirmDialog(null,
                "确定要删除星级 " + selectedLevel + " 及其所有特效配置吗？\n此操作不可撤销！",
                "确认删除",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.WARNING_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            try {
                // 提取星级数字
                String levelStr = selectedLevel.replace("星", "");
                String configPath = "eff.leve" + levelStr;

                // 从配置文件中删除
                Cuilian.config.set(configPath, null);

                // 保存配置文件
                Cuilian.config.save(Cuilian.filess);

                // 更新星级下拉框
                refreshStarLevelComboBox();

                // 清空特效列表
                this.currentEffectsModel.clear();

                this.statusLabel.setText("星级 " + selectedLevel + " 删除成功！");
                this.statusLabel.setForeground(new java.awt.Color(0, 128, 0));
                JOptionPane.showMessageDialog(null, "星级 " + selectedLevel + " 删除成功！");

            } catch (Exception e) {
                JOptionPane.showMessageDialog(null, "删除星级失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 刷新星级下拉框
     */
    private void refreshStarLevelComboBox() {
        this.effectStarLevelComboBox.removeAllItems();
        this.effectStarLevelComboBox.addItem("选择星级");

        // 动态读取已配置的星级
        try {
            if (Cuilian.config != null && Cuilian.config.isConfigurationSection("eff")) {
                org.bukkit.configuration.ConfigurationSection effSection = Cuilian.config
                        .getConfigurationSection("eff");
                java.util.Set<String> levelKeys = effSection.getKeys(false);

                // 提取数字并排序
                java.util.List<Integer> levelNumbers = new java.util.ArrayList<>();
                for (String key : levelKeys) {
                    if (key.startsWith("leve")) {
                        try {
                            int level = Integer.parseInt(key.substring(4));
                            levelNumbers.add(level);
                        } catch (NumberFormatException e) {
                            // 忽略无效的星级配置
                        }
                    }
                }

                // 排序并添加到下拉框
                java.util.Collections.sort(levelNumbers);
                for (Integer level : levelNumbers) {
                    this.effectStarLevelComboBox.addItem(level + "星");
                }
            }
        } catch (Exception e) {
            System.out.println("刷新星级列表时出错: " + e.getMessage());
        }
    }

    /**
     * 前三名宝座面板
     */
    private class TopThreePanel extends JPanel {
        private java.util.List<PlayerActivity> topThree = new java.util.ArrayList<>();

        public TopThreePanel() {
            setLayout(null);
            setBackground(new java.awt.Color(248, 248, 255));
            setPreferredSize(new java.awt.Dimension(280, 150)); // 增加高度到150
        }

        public void updateTopThree(java.util.List<PlayerActivity> activities) {
            this.topThree.clear();
            for (int i = 0; i < Math.min(3, activities.size()); i++) {
                this.topThree.add(activities.get(i));
            }
            repaint();
        }

        @Override
        protected void paintComponent(java.awt.Graphics g) {
            super.paintComponent(g);
            java.awt.Graphics2D g2d = (java.awt.Graphics2D) g;
            g2d.setRenderingHint(java.awt.RenderingHints.KEY_ANTIALIASING, java.awt.RenderingHints.VALUE_ANTIALIAS_ON);

            if (topThree.isEmpty()) {
                // 显示无玩家提示
                g2d.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
                g2d.setColor(java.awt.Color.GRAY);
                String noPlayerText = "暂无在线玩家";
                java.awt.FontMetrics fm = g2d.getFontMetrics();
                int textWidth = fm.stringWidth(noPlayerText);
                g2d.drawString(noPlayerText, (getWidth() - textWidth) / 2, getHeight() / 2);
                return;
            }

            // 绘制宝座背景
            drawPodium(g2d);

            // 绘制前三名玩家
            int[] xPositions = { getWidth() / 2 - 30, 20, getWidth() - 80 }; // 第一名居中，第二名左侧，第三名右侧
            int[] yPositions = { 25, 50, 50 }; // 第一名最高，第二三名稍低，增加Y位置给时长更多空间
            String[] medals = { "第一名", "第二名", "第三名" };
            java.awt.Color[] colors = {
                    new java.awt.Color(255, 215, 0), // 金色
                    new java.awt.Color(192, 192, 192), // 银色
                    new java.awt.Color(205, 127, 50) // 铜色
            };

            for (int i = 0; i < topThree.size(); i++) {
                PlayerActivity activity = topThree.get(i);
                int x = xPositions[i];
                int y = yPositions[i];

                // 绘制宝座背景
                g2d.setColor(new java.awt.Color(colors[i].getRed(), colors[i].getGreen(), colors[i].getBlue(), 100));
                g2d.fillRoundRect(x - 5, y - 5, 70, 80, 10, 10);

                // 绘制边框
                g2d.setColor(colors[i]);
                g2d.setStroke(new java.awt.BasicStroke(2));
                g2d.drawRoundRect(x - 5, y - 5, 70, 80, 10, 10);

                // 绘制玩家头颅
                int headX = x + 15;
                int headY = y + 30;
                int headSize = 24;
                drawPlayerHead(g2d, activity.playerName, headX, headY, headSize);

                // 绘制排名标识（在头像上方居中）
                g2d.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
                g2d.setColor(colors[i]);
                java.awt.FontMetrics medalFm = g2d.getFontMetrics();
                int medalWidth = medalFm.stringWidth(medals[i]);
                int medalX = headX + (headSize - medalWidth) / 2;
                int medalY = headY - 5;
                g2d.drawString(medals[i], medalX, medalY);

                // 绘制玩家名称
                g2d.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
                g2d.setColor(java.awt.Color.BLACK);
                String name = activity.playerName.length() > 8 ? activity.playerName.substring(0, 8) + "..."
                        : activity.playerName;
                java.awt.FontMetrics fm = g2d.getFontMetrics();
                int nameWidth = fm.stringWidth(name);
                g2d.drawString(name, x + (60 - nameWidth) / 2, headY + headSize + 15);

                // 绘制在线时长
                g2d.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
                g2d.setColor(new java.awt.Color(100, 100, 100));
                String timeText;
                if (activity.hours > 0) {
                    timeText = activity.hours + "小时" + activity.minutes + "分钟";
                } else {
                    if (activity.minutes == 0) {
                        timeText = "刚上线";
                    } else {
                        timeText = activity.minutes + "分钟";
                    }
                }
                java.awt.FontMetrics timeFm = g2d.getFontMetrics();
                int timeWidth = timeFm.stringWidth(timeText);
                g2d.drawString(timeText, x + (60 - timeWidth) / 2, headY + headSize + 35); // 增加Y位置，避免被遮挡
            }
        }

        private void drawPodium(java.awt.Graphics2D g2d) {
            // 绘制宝座台阶效果
            g2d.setColor(new java.awt.Color(220, 220, 220));

            // 第一名台阶（最高）
            g2d.fillRect(getWidth() / 2 - 35, 90, 70, 25);

            // 第二名台阶（中等）
            g2d.fillRect(15, 100, 70, 15);

            // 第三名台阶（最低）
            g2d.fillRect(getWidth() - 85, 105, 70, 10);
        }

        private void drawPlayerHead(java.awt.Graphics2D g2d, String playerName, int x, int y, int size) {
            // 绘制改进的玩家头颅
            g2d.setColor(new java.awt.Color(139, 69, 19)); // 棕色皮肤
            g2d.fillRect(x, y, size, size);

            // 绘制头颅边框
            g2d.setColor(java.awt.Color.BLACK);
            g2d.drawRect(x, y, size, size);

            // 绘制眼睛
            g2d.setColor(java.awt.Color.WHITE);
            int eyeSize = size / 8;
            g2d.fillOval(x + size / 4, y + size / 3, eyeSize, eyeSize);
            g2d.fillOval(x + size * 3 / 4 - eyeSize, y + size / 3, eyeSize, eyeSize);

            // 绘制瞳孔
            g2d.setColor(java.awt.Color.BLACK);
            int pupilSize = eyeSize / 2;
            g2d.fillOval(x + size / 4 + pupilSize / 2, y + size / 3 + pupilSize / 2, pupilSize, pupilSize);
            g2d.fillOval(x + size * 3 / 4 - eyeSize + pupilSize / 2, y + size / 3 + pupilSize / 2, pupilSize,
                    pupilSize);

            // 绘制嘴巴
            g2d.setColor(new java.awt.Color(139, 69, 19));
            g2d.fillOval(x + size / 2 - size / 8, y + size * 2 / 3, size / 4, size / 8);
        }
    }

    /**
     * 普通排名面板
     */
    private class NormalRankingPanel extends JPanel {
        private java.util.List<PlayerActivity> normalRanking = new java.util.ArrayList<>();

        public NormalRankingPanel() {
            setLayout(new java.awt.BorderLayout());
            setBackground(java.awt.Color.WHITE);
        }

        public void updateNormalRanking(java.util.List<PlayerActivity> activities) {
            this.normalRanking.clear();
            // 添加第4名及以后的玩家
            for (int i = 3; i < activities.size(); i++) {
                this.normalRanking.add(activities.get(i));
            }

            // 重新创建列表
            removeAll();

            if (normalRanking.isEmpty()) {
                JLabel noDataLabel = new JLabel("暂无其他排名", JLabel.CENTER);
                noDataLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
                noDataLabel.setForeground(java.awt.Color.GRAY);
                add(noDataLabel, java.awt.BorderLayout.CENTER);
            } else {
                JPanel listPanel = new JPanel();
                listPanel.setLayout(new java.awt.GridLayout(normalRanking.size(), 1, 0, 2));
                listPanel.setBackground(java.awt.Color.WHITE);

                for (int i = 0; i < normalRanking.size(); i++) {
                    PlayerActivity activity = normalRanking.get(i);
                    int rank = i + 4; // 从第4名开始

                    JPanel itemPanel = createRankingItem(activity, rank);
                    listPanel.add(itemPanel);
                }

                add(listPanel, java.awt.BorderLayout.NORTH);
            }

            revalidate();
            repaint();
        }

        private JPanel createRankingItem(PlayerActivity activity, int rank) {
            JPanel itemPanel = new JPanel(new java.awt.FlowLayout(java.awt.FlowLayout.LEFT, 5, 2));
            itemPanel.setBackground(java.awt.Color.WHITE);
            itemPanel.setPreferredSize(new java.awt.Dimension(260, 30));

            // 排名数字方块
            JLabel rankLabel = new JLabel(String.valueOf(rank));
            rankLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
            rankLabel.setForeground(java.awt.Color.WHITE);
            rankLabel.setBackground(new java.awt.Color(100, 100, 100));
            rankLabel.setOpaque(true);
            rankLabel.setHorizontalAlignment(JLabel.CENTER);
            rankLabel.setVerticalAlignment(JLabel.CENTER);
            rankLabel.setPreferredSize(new java.awt.Dimension(28, 28));
            rankLabel.setBorder(BorderFactory.createRaisedBevelBorder());
            itemPanel.add(rankLabel);

            // 玩家头颅（改进的方块样式）
            JLabel headLabel = createPlayerHeadLabel(activity.playerName);
            itemPanel.add(headLabel);

            // 玩家名称
            JLabel nameLabel = new JLabel(activity.playerName);
            nameLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
            nameLabel.setPreferredSize(new java.awt.Dimension(80, 20));
            itemPanel.add(nameLabel);

            // 在线时长
            String timeText;
            if (activity.hours > 0) {
                timeText = String.format("在线: %d小时%d分钟", activity.hours, activity.minutes);
            } else {
                if (activity.minutes == 0) {
                    timeText = "在线: 刚上线";
                } else {
                    timeText = String.format("在线: %d分钟", activity.minutes);
                }
            }
            JLabel timeLabel = new JLabel(timeText);
            timeLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
            timeLabel.setForeground(new java.awt.Color(100, 100, 100));
            itemPanel.add(timeLabel);

            return itemPanel;
        }

        /**
         * 创建玩家头颅标签
         */
        private JLabel createPlayerHeadLabel(String playerName) {
            JLabel headLabel = new JLabel();
            headLabel.setPreferredSize(new java.awt.Dimension(24, 24));
            headLabel.setOpaque(true);
            headLabel.setBackground(new java.awt.Color(139, 69, 19)); // 棕色皮肤
            headLabel.setBorder(BorderFactory.createLineBorder(java.awt.Color.BLACK, 1));

            // 创建简单的头颅图标
            headLabel.setIcon(createSimpleHeadIcon());

            return headLabel;
        }

        /**
         * 创建简单的头颅图标
         */
        private javax.swing.ImageIcon createSimpleHeadIcon() {
            try {
                java.awt.image.BufferedImage headImage = new java.awt.image.BufferedImage(24, 24,
                        java.awt.image.BufferedImage.TYPE_INT_ARGB);
                java.awt.Graphics2D g2d = headImage.createGraphics();
                g2d.setRenderingHint(java.awt.RenderingHints.KEY_ANTIALIASING,
                        java.awt.RenderingHints.VALUE_ANTIALIAS_ON);

                // 绘制头颅背景
                g2d.setColor(new java.awt.Color(139, 69, 19)); // 棕色皮肤
                g2d.fillRect(0, 0, 24, 24);

                // 绘制眼睛
                g2d.setColor(java.awt.Color.WHITE);
                g2d.fillOval(6, 7, 3, 3);
                g2d.fillOval(15, 7, 3, 3);

                // 绘制瞳孔
                g2d.setColor(java.awt.Color.BLACK);
                g2d.fillOval(7, 8, 1, 1);
                g2d.fillOval(16, 8, 1, 1);

                // 绘制嘴巴
                g2d.setColor(new java.awt.Color(139, 69, 19));
                g2d.fillOval(10, 16, 4, 2);

                g2d.dispose();
                return new javax.swing.ImageIcon(headImage);
            } catch (Exception e) {
                // 如果创建失败，返回null
                return null;
            }
        }
    }

    /**
     * 更新实时监控数据
     */
    private void updateRealTimeMonitoringData() {
        try {
            // 更新性能统计数据
            updatePerformanceStats();

            // 更新性能统计显示
            updatePerformanceStatsDisplay();

            // 不在实时更新中更新淬炼石统计，避免频繁计算
            // 淬炼石统计只在手动刷新或有实际使用时更新

            // 更新监控面板的实时数据（左侧实时监控面板）
            updateMonitoringPanelData();

            // 如果有监控标签的引用，直接更新左侧监控面板
            if (monitoringTpsLabel != null && monitoringPlayersLabel != null &&
                    monitoringMemoryLabel != null && monitoringStatusLabel != null) {
                updateMonitorData(monitoringTpsLabel, monitoringPlayersLabel,
                        monitoringMemoryLabel, monitoringStatusLabel);
            }

        } catch (Exception e) {
            System.err.println("更新实时监控数据失败: " + e.getMessage());
        }
    }

    /**
     * 更新监控面板的实时数据
     */
    private void updateMonitoringPanelData() {
        try {
            // 更新监控面板中的实时数据
            if (this.getContentPane() != null) {
                updateMonitoringLabels(this.getContentPane());
            }
        } catch (Exception e) {
            System.err.println("更新监控面板数据失败: " + e.getMessage());
        }
    }

    /**
     * 递归更新监控面板中的标签
     */
    private void updateMonitoringLabels(java.awt.Container container) {
        try {
            for (java.awt.Component component : container.getComponents()) {
                if (component instanceof JLabel) {
                    JLabel label = (JLabel) component;
                    String text = label.getText();
                    if (text != null) {
                        // 更新在线玩家数
                        if (text.matches("\\d+/\\d+") || text.contains("在线玩家")) {
                            int onlinePlayers = Bukkit.getOnlinePlayers().size();
                            int maxPlayers = Bukkit.getMaxPlayers();
                            label.setText(onlinePlayers + "/" + maxPlayers);
                        }
                        // 更新TPS（匹配数字格式）
                        else if (text.matches("\\d+\\.\\d+") && !text.contains("MB") && !text.contains("%")) {
                            double currentTps = getTPS();
                            label.setText(String.format("%.1f", currentTps));
                            // 根据TPS设置颜色
                            if (currentTps >= 18.0) {
                                label.setForeground(new java.awt.Color(0, 128, 0)); // 绿色
                            } else if (currentTps >= 15.0) {
                                label.setForeground(new java.awt.Color(255, 165, 0)); // 橙色
                            } else {
                                label.setForeground(new java.awt.Color(255, 0, 0)); // 红色
                            }
                        }
                        // 更新内存使用
                        else if (text.contains("MB/") && text.contains("MB")) {
                            Runtime runtime = Runtime.getRuntime();
                            long usedMemory = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024;
                            long maxMemory = runtime.maxMemory() / 1024 / 1024;
                            label.setText(usedMemory + "MB/" + maxMemory + "MB");
                        }
                        // 更新CPU使用率
                        else if (text.contains("%") && !text.contains("MB")) {
                            double cpuUsage = getCPUUsage();
                            label.setText(String.format("%.1f%%", cpuUsage));
                            // 根据CPU使用率设置颜色
                            if (cpuUsage < 50.0) {
                                label.setForeground(new java.awt.Color(0, 128, 0)); // 绿色
                            } else if (cpuUsage < 80.0) {
                                label.setForeground(new java.awt.Color(255, 165, 0)); // 橙色
                            } else {
                                label.setForeground(new java.awt.Color(255, 0, 0)); // 红色
                            }
                        }
                        // 更新服务器运行时间
                        else if (text.contains("小时") || text.contains("分钟") || text.contains("天")) {
                            long uptimeMillis = System.currentTimeMillis() - getServerStartTime();
                            String uptimeString = formatUptime(uptimeMillis);
                            label.setText(uptimeString);
                        }
                        // 更新插件数量
                        else if (text.matches("\\d+") && component.getParent() != null) {
                            // 通过父容器的其他标签来判断这是插件数量还是世界数量
                            java.awt.Container parent = component.getParent();
                            boolean isPluginCount = false;
                            boolean isWorldCount = false;

                            for (java.awt.Component sibling : parent.getComponents()) {
                                if (sibling instanceof JLabel) {
                                    String siblingText = ((JLabel) sibling).getText();
                                    if (siblingText != null) {
                                        if (siblingText.contains("插件数量")) {
                                            isPluginCount = true;
                                            break;
                                        } else if (siblingText.contains("世界数量")) {
                                            isWorldCount = true;
                                            break;
                                        }
                                    }
                                }
                            }

                            if (isPluginCount) {
                                label.setText(String.valueOf(Bukkit.getPluginManager().getPlugins().length));
                            } else if (isWorldCount) {
                                label.setText(String.valueOf(Bukkit.getWorlds().size()));
                            }
                        }
                    }
                } else if (component instanceof java.awt.Container) {
                    updateMonitoringLabels((java.awt.Container) component);
                }
            }
        } catch (Exception e) {
            // 忽略错误，避免影响其他功能
        }
    }
}