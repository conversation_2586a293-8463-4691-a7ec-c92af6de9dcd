import java.util.HashMap;
import java.util.Map;

/**
 * 测试淇炼石统计逻辑
 */
public class StoneStatsTest {
    
    private static Map<String, Integer> stoneUsageStats = new HashMap<>();
    
    /**
     * 模拟 Cuilian.getTodayStoneUsage() 方法
     */
    private static HashMap<String, Integer> getTodayStoneUsage(boolean hasUsage) {
        HashMap<String, Integer> usage = new HashMap<>();
        if (hasUsage) {
            // 模拟有使用数据的情况
            usage.put("普通", 5);
            usage.put("中等", 3);
            usage.put("高等", 0);
            usage.put("上等", 0);
            usage.put("符咒", 0);
            usage.put("吞噬", 0);
        } else {
            // 模拟没有使用数据的情况（空或全为0）
            usage.put("普通", 0);
            usage.put("中等", 0);
            usage.put("高等", 0);
            usage.put("上等", 0);
            usage.put("符咒", 0);
            usage.put("吞噬", 0);
        }
        return usage;
    }
    
    /**
     * 更新真实的淬炼石统计
     */
    private static void updateRealStoneStats(boolean hasUsage) {
        try {
            // 从Cuilian插件获取今日淬炼石使用统计
            HashMap<String, Integer> todayUsage = getTodayStoneUsage(hasUsage);

            // 只有当有真实使用数据时才更新
            if (todayUsage != null && !todayUsage.isEmpty()) {
                // 检查是否有非零的使用数据
                boolean hasRealUsage = todayUsage.values().stream().anyMatch(count -> count > 0);
                if (hasRealUsage) {
                    stoneUsageStats.clear();
                    stoneUsageStats.putAll(todayUsage);
                }
            }

            // 如果没有真实使用数据，确保统计为0
            if (stoneUsageStats.isEmpty()) {
                stoneUsageStats.put("普通", 0);
                stoneUsageStats.put("中等", 0);
                stoneUsageStats.put("高等", 0);
                stoneUsageStats.put("上等", 0);
                stoneUsageStats.put("符咒", 0);
                stoneUsageStats.put("吞噬", 0);
            }

        } catch (Exception e) {
            System.err.println("更新真实淬炼石统计失败: " + e.getMessage());
            // 出错时确保统计为0
            stoneUsageStats.clear();
            stoneUsageStats.put("普通", 0);
            stoneUsageStats.put("中等", 0);
            stoneUsageStats.put("高等", 0);
            stoneUsageStats.put("上等", 0);
            stoneUsageStats.put("符咒", 0);
            stoneUsageStats.put("吞噬", 0);
        }
    }
    
    /**
     * 计算平均成功率
     */
    private static double calculateAverageSuccessRate() {
        // 如果没有使用任何淬炼石，返回0
        int totalAttempts = stoneUsageStats.values().stream().mapToInt(Integer::intValue).sum();
        if (totalAttempts == 0) {
            return 0.0;
        }

        // 模拟从Probability.yml配置文件获取真实的成功率
        double totalWeightedSuccess = 0.0;
        int totalUsage = 0;

        for (Map.Entry<String, Integer> entry : stoneUsageStats.entrySet()) {
            String stoneType = entry.getKey();
            int usage = entry.getValue();
            if (usage > 0) {
                double avgSuccessRate = getStoneTypeAverageSuccessRate(stoneType);
                totalWeightedSuccess += avgSuccessRate * usage;
                totalUsage += usage;
            }
        }

        return totalUsage > 0 ? totalWeightedSuccess / totalUsage : 0.0;
    }
    
    /**
     * 获取指定淬炼石类型的平均成功率
     */
    private static double getStoneTypeAverageSuccessRate(String stoneType) {
        switch (stoneType) {
            case "普通": return 47.4;
            case "中等": return 60.5;
            case "高等": return 71.1;
            case "上等": return 80.0;
            case "符咒": return 90.0;
            case "吞噬": return 95.0;
            default: return 50.0;
        }
    }
    
    public static void main(String[] args) {
        System.out.println("淇炼石统计逻辑测试");
        System.out.println("==================");
        
        // 测试1：没有使用淇炼石的情况
        System.out.println("1. 测试没有使用淇炼石的情况:");
        stoneUsageStats.clear();
        updateRealStoneStats(false);
        double avgRate1 = calculateAverageSuccessRate();
        System.out.println("   淇炼石使用统计: " + stoneUsageStats);
        System.out.println("   平均成功率: " + String.format("%.1f%%", avgRate1));
        System.out.println("   预期结果: 0.0% (没有使用任何淇炼石)");
        System.out.println();
        
        // 测试2：有使用淇炼石的情况
        System.out.println("2. 测试有使用淇炼石的情况:");
        stoneUsageStats.clear();
        updateRealStoneStats(true);
        double avgRate2 = calculateAverageSuccessRate();
        System.out.println("   淇炼石使用统计: " + stoneUsageStats);
        System.out.println("   平均成功率: " + String.format("%.1f%%", avgRate2));
        System.out.println("   预期结果: 根据实际使用量计算的成功率");
        System.out.println();
        
        // 测试3：多次调用updateRealStoneStats（模拟实时更新）
        System.out.println("3. 测试多次调用updateRealStoneStats（模拟实时更新）:");
        stoneUsageStats.clear();
        
        // 第一次调用 - 没有使用数据
        updateRealStoneStats(false);
        double rate1 = calculateAverageSuccessRate();
        System.out.println("   第1次调用 - 平均成功率: " + String.format("%.1f%%", rate1));
        
        // 第二次调用 - 仍然没有使用数据
        updateRealStoneStats(false);
        double rate2 = calculateAverageSuccessRate();
        System.out.println("   第2次调用 - 平均成功率: " + String.format("%.1f%%", rate2));
        
        // 第三次调用 - 仍然没有使用数据
        updateRealStoneStats(false);
        double rate3 = calculateAverageSuccessRate();
        System.out.println("   第3次调用 - 平均成功率: " + String.format("%.1f%%", rate3));
        
        System.out.println("   预期结果: 所有调用都应该返回0.0%，不应该自动增加");
        System.out.println();
        
        System.out.println("测试完成！");
        System.out.println("修复后的逻辑确保：");
        System.out.println("1. 没有使用淇炼石时，平均成功率显示0.0%");
        System.out.println("2. 多次调用不会导致成功率自动增加");
        System.out.println("3. 只有真实使用数据时才会计算实际成功率");
    }
}
