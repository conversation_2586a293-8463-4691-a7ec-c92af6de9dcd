package cn.winde.cuilian.clbh;

import org.bukkit.entity.Player;
import org.bukkit.OfflinePlayer;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import cn.winde.cuilian.Cuilian;
import cn.winde.cuilian.suit.SuitManager;
import cn.winde.cuilian.util.SuitMessageManager;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * 玩家装备预览对话框
 */
public class PlayerEquipmentPreviewDialog extends JDialog {
    private final Player player;
    private final OfflinePlayer offlinePlayer;
    private final boolean isOnline;
    private final String playerName;

    private JTextArea equipmentInfoArea;
    private JTextArea weaponInfoArea;
    private JTextArea effectInfoArea;
    private JTextArea suitInfoArea;

    // 在线玩家构造函数
    public PlayerEquipmentPreviewDialog(JFrame parent, Player player, boolean isOnline) {
        super(parent, "玩家装备预览 - " + player.getName() + " [在线]", true);
        this.player = player;
        this.offlinePlayer = null;
        this.isOnline = true;
        this.playerName = player.getName();
        initializeComponents();
        loadPlayerData();
        setLocationRelativeTo(parent);
    }

    // 离线玩家构造函数
    public PlayerEquipmentPreviewDialog(JFrame parent, OfflinePlayer offlinePlayer) {
        super(parent, "玩家装备预览 - " + offlinePlayer.getName() + " [离线]", true);
        this.player = null;
        this.offlinePlayer = offlinePlayer;
        this.isOnline = false;
        this.playerName = offlinePlayer.getName();
        initializeComponents();
        loadPlayerData();
        setLocationRelativeTo(parent);
    }

    private void initializeComponents() {
        setLayout(new BorderLayout());
        setSize(800, 600);
        setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);

        // 创建主面板
        JPanel mainPanel = new JPanel(new GridLayout(2, 2, 10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // 装备信息面板
        JPanel equipmentPanel = createInfoPanel("装备信息", equipmentInfoArea = new JTextArea());
        mainPanel.add(equipmentPanel);

        // 武器信息面板
        JPanel weaponPanel = createInfoPanel("武器信息", weaponInfoArea = new JTextArea());
        mainPanel.add(weaponPanel);

        // 特效信息面板
        JPanel effectPanel = createInfoPanel("特效信息", effectInfoArea = new JTextArea());
        mainPanel.add(effectPanel);

        // 套装信息面板
        JPanel suitPanel = createInfoPanel("套装信息", suitInfoArea = new JTextArea());
        mainPanel.add(suitPanel);

        add(mainPanel, BorderLayout.CENTER);

        // 底部按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout());
        JButton refreshButton = new JButton("刷新");
        refreshButton.addActionListener(e -> loadPlayerData());

        JButton gameStyleButton = new JButton("游戏风格预览");
        gameStyleButton.addActionListener(e -> openGameStylePreview());

        JButton closeButton = new JButton("关闭");
        closeButton.addActionListener(e -> dispose());

        buttonPanel.add(refreshButton);
        buttonPanel.add(gameStyleButton);
        buttonPanel.add(closeButton);
        add(buttonPanel, BorderLayout.SOUTH);
    }

    private JPanel createInfoPanel(String title, JTextArea textArea) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(),
                title,
                TitledBorder.LEFT,
                TitledBorder.TOP,
                new Font("微软雅黑", Font.BOLD, 12)));

        textArea.setEditable(false);
        textArea.setFont(new Font("微软雅黑", Font.PLAIN, 11));
        textArea.setBackground(new Color(248, 248, 248));
        textArea.setMargin(new Insets(5, 5, 5, 5));

        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);

        panel.add(scrollPane, BorderLayout.CENTER);
        return panel;
    }

    private void loadPlayerData() {
        if (isOnline && (player == null || !player.isOnline())) {
            JOptionPane.showMessageDialog(this, "玩家已离线！", "错误", JOptionPane.ERROR_MESSAGE);
            dispose();
            return;
        }

        // 加载装备信息
        loadEquipmentInfo();

        // 加载武器信息
        loadWeaponInfo();

        // 加载特效信息
        loadEffectInfo();

        // 加载套装信息
        loadSuitInfo();
    }

    private int getPlayerEquipmentLevel() {
        if (isOnline && player != null) {
            return Cuilian.checkPlayerZBCL(player);
        } else {
            // 离线玩家返回0，因为无法获取实时装备信息
            return 0;
        }
    }

    private int getPlayerWeaponLevel() {
        if (isOnline && player != null) {
            return Cuilian.checkPlayerWQCL(player);
        } else {
            // 离线玩家返回0，因为无法获取实时武器信息
            return 0;
        }
    }

    private void loadEquipmentInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 装备详情 ===\n\n");

        if (isOnline && player != null) {
            // 在线玩家 - 获取实时装备信息
            ItemStack helmet = player.getInventory().getHelmet();
            sb.append("头盔: ").append(getItemInfo(helmet, "头盔")).append("\n\n");

            ItemStack chestplate = player.getInventory().getChestplate();
            sb.append("胸甲: ").append(getItemInfo(chestplate, "胸甲")).append("\n\n");

            ItemStack leggings = player.getInventory().getLeggings();
            sb.append("护腿: ").append(getItemInfo(leggings, "护腿")).append("\n\n");

            ItemStack boots = player.getInventory().getBoots();
            sb.append("靴子: ").append(getItemInfo(boots, "靴子")).append("\n\n");

            // 装备等级统计
            int equipmentLevel = Cuilian.checkPlayerZBCL(player);
            sb.append("=== 装备等级统计 ===\n");
            sb.append("装备等级: ").append(equipmentLevel).append("星\n");
            sb.append("(取四件装备中最低等级)");
        } else {
            // 离线玩家 - 显示提示信息
            sb.append("玩家当前离线，无法获取实时装备信息。\n\n");
            sb.append("=== 离线玩家信息 ===\n");
            sb.append("玩家名称: ").append(playerName).append("\n");
            sb.append("最后在线时间: ");
            if (offlinePlayer != null && offlinePlayer.getLastPlayed() > 0) {
                java.util.Date lastPlayed = new java.util.Date(offlinePlayer.getLastPlayed());
                sb.append(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(lastPlayed));
            } else {
                sb.append("未知");
            }
            sb.append("\n\n");
            sb.append("提示: 装备信息需要玩家在线时才能查看。");
        }

        equipmentInfoArea.setText(sb.toString());
        equipmentInfoArea.setCaretPosition(0);
    }

    private void loadWeaponInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 武器详情 ===\n\n");

        if (isOnline && player != null) {
            // 在线玩家 - 获取实时武器信息
            ItemStack weapon = player.getInventory().getItemInHand();
            if (weapon != null && !weapon.getType().name().equals("AIR")) {
                sb.append("手持物品: ").append(getItemInfo(weapon, "武器")).append("\n\n");

                // 检查是否是武器
                boolean isWeapon = Cuilian.isWeapon(weapon);
                sb.append("是否为武器: ").append(isWeapon ? "是" : "否").append("\n\n");

                if (isWeapon) {
                    int weaponLevel = Cuilian.getCuilianlevel(weapon);
                    sb.append("武器淬炼等级: ").append(weaponLevel).append("星\n");
                } else {
                    sb.append("此物品不是武器，不计入套装等级\n");
                }
            } else {
                sb.append("未手持任何物品\n");
            }

            // 武器等级统计
            int weaponLevel = Cuilian.checkPlayerWQCL(player);
            sb.append("\n=== 武器等级统计 ===\n");
            sb.append("武器等级: ").append(weaponLevel).append("星\n");
            sb.append("(只有真正的武器才计入等级)");
        } else {
            // 离线玩家 - 显示提示信息
            sb.append("玩家当前离线，无法获取实时武器信息。\n\n");
            sb.append("=== 支持的武器类型 ===\n");
            sb.append("• 各种剑类 (WOOD_SWORD, STONE_SWORD, IRON_SWORD, 等)\n");
            sb.append("• 弓 (BOW)\n");
            sb.append("• 弩 (CROSSBOW)\n");
            sb.append("• 三叉戟 (TRIDENT)\n\n");
            sb.append("提示: 武器信息需要玩家在线时才能查看。");
        }

        weaponInfoArea.setText(sb.toString());
        weaponInfoArea.setCaretPosition(0);
    }

    private void loadEffectInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 特效状态 ===\n\n");

        // 全局特效开关
        boolean globalEffects = Cuilian.config.getBoolean("effects", true);
        sb.append("全局特效开关: ").append(globalEffects ? "开启" : "关闭").append("\n");

        // 玩家个人特效开关
        boolean playerEffects = SuitManager.isPlayerEffectEnabled(playerName);
        sb.append("玩家特效开关: ").append(playerEffects ? "开启" : "关闭").append("\n");

        // 消息提示设置
        boolean forceDisabled = SuitMessageManager.isForceDisabled();
        sb.append("强制禁用消息: ").append(forceDisabled ? "是" : "否").append("\n");

        if (!forceDisabled) {
            boolean messageEnabled = SuitMessageManager.isPlayerMessageEnabled(playerName);
            sb.append("玩家消息开关: ").append(messageEnabled ? "开启" : "关闭").append("\n");
        }

        // 当前激活的特效
        Integer currentLevel = Cuilian.lizi.get(playerName);
        sb.append("\n=== 当前特效 ===\n");
        if (isOnline && currentLevel != null && currentLevel != 0) {
            if (currentLevel == -1) {
                // 命名套装特效
                String suitName = SuitManager.getPlayerSuit(playerName);
                if (suitName != null) {
                    sb.append("激活特效类型: 命名套装 (").append(suitName).append(")\n");
                } else {
                    sb.append("激活特效类型: 命名套装\n");
                }
                sb.append("特效状态: 激活中\n");

                // 显示命名套装的特效信息
                sb.append("\n=== 特效详情 ===\n");
                loadNamedSuitEffectDetails(sb, suitName);
            } else if (currentLevel > 0) {
                // 淬炼套装特效
                sb.append("激活特效等级: ").append(currentLevel).append("星\n");
                sb.append("特效状态: 激活中\n");

                // 显示具体的特效信息
                sb.append("\n=== 特效详情 ===\n");
                loadEffectDetails(sb, currentLevel);
            }
        } else {
            if (!isOnline) {
                sb.append("特效状态: 离线 (无法获取实时特效状态)\n");
                sb.append("说明: 特效只在玩家在线时激活\n\n");
                sb.append("=== 特效配置预览 ===\n");
                sb.append("以下是6星特效的配置信息:\n");
                loadEffectDetails(sb, 6);
            } else {
                sb.append("特效状态: 未激活\n");
                sb.append("原因: ");

                // 分析未激活的原因
                if (!globalEffects) {
                    sb.append("全局特效已关闭");
                } else if (!playerEffects) {
                    sb.append("玩家特效已关闭");
                } else {
                    int equipmentLevel = getPlayerEquipmentLevel();
                    int weaponLevel = getPlayerWeaponLevel();
                    int suitLevel = Math.min(equipmentLevel, weaponLevel);
                    if (suitLevel < 6) {
                        sb.append("套装等级不足6星 (当前: ").append(suitLevel).append("星)");
                    } else {
                        sb.append("未知原因");
                    }
                }
            }
        }

        effectInfoArea.setText(sb.toString());
        effectInfoArea.setCaretPosition(0);
    }

    private void loadEffectDetails(StringBuilder sb, int level) {
        try {
            // 获取特效配置
            String configPath = "eff.leve" + level + ".effects";
            if (Cuilian.config.contains(configPath)) {
                List<?> effectsList = Cuilian.config.getList(configPath);
                if (effectsList != null && !effectsList.isEmpty()) {
                    sb.append("特效列表:\n");
                    for (int i = 0; i < effectsList.size(); i++) {
                        if (effectsList.get(i) instanceof java.util.Map) {
                            @SuppressWarnings("unchecked")
                            java.util.Map<String, Object> effectMap = (java.util.Map<String, Object>) effectsList
                                    .get(i);

                            String type = (String) effectMap.get("type");
                            boolean enabled = (Boolean) effectMap.getOrDefault("enabled", true);
                            String color1 = (String) effectMap.getOrDefault("colore1", "");
                            String color2 = (String) effectMap.getOrDefault("colore2", "");
                            String color3 = (String) effectMap.getOrDefault("colore3", "");

                            // 获取中文名称
                            String displayName = SuitManager.getEffectDisplayName(type);
                            String statusText = enabled ? "启用" : "禁用";

                            sb.append("  ").append(i + 1).append(". ").append(displayName)
                                    .append(" [").append(statusText).append("]\n");

                            if (!color1.isEmpty() || !color2.isEmpty() || !color3.isEmpty()) {
                                sb.append("     颜色: ").append(color1.isEmpty() ? "默认" : color1)
                                        .append(", ").append(color2.isEmpty() ? "默认" : color2)
                                        .append(", ").append(color3.isEmpty() ? "默认" : color3).append("\n");
                            }
                        }
                    }
                } else {
                    sb.append("该等级暂无配置的特效\n");
                }
            } else {
                // 检查旧格式配置
                String oldConfigPath = "eff.leve" + level;
                if (Cuilian.config.contains(oldConfigPath + ".type")) {
                    String type = Cuilian.config.getString(oldConfigPath + ".type");
                    String color1 = Cuilian.config.getString(oldConfigPath + ".colore1", "");
                    String color2 = Cuilian.config.getString(oldConfigPath + ".colore2", "");
                    String color3 = Cuilian.config.getString(oldConfigPath + ".colore3", "");

                    String displayName = SuitManager.getEffectDisplayName(type);
                    sb.append("特效类型: ").append(displayName).append(" [启用]\n");

                    if (!color1.isEmpty() || !color2.isEmpty() || !color3.isEmpty()) {
                        sb.append("颜色配置: ").append(color1.isEmpty() ? "默认" : color1)
                                .append(", ").append(color2.isEmpty() ? "默认" : color2)
                                .append(", ").append(color3.isEmpty() ? "默认" : color3).append("\n");
                    }
                } else {
                    sb.append("该等级暂无配置的特效\n");
                }
            }
        } catch (Exception e) {
            sb.append("获取特效信息时出错: ").append(e.getMessage()).append("\n");
        }
    }

    private void loadNamedSuitEffectDetails(StringBuilder sb, String suitName) {
        try {
            if (suitName == null) {
                sb.append("命名套装信息不可用\n");
                return;
            }

            // 获取命名套装的特效配置
            String basePath = "suit." + suitName + ".effect";
            if (!Cuilian.Suit.contains(basePath)) {
                sb.append("该套装暂无配置的特效\n");
                return;
            }

            // 检查是否有新格式的effects数组
            if (Cuilian.Suit.contains(basePath + ".effects")) {
                List<?> effectsList = Cuilian.Suit.getList(basePath + ".effects");
                boolean enableStacking = Cuilian.Suit.getBoolean(basePath + ".enable_stacking", false);

                if (effectsList != null && !effectsList.isEmpty()) {
                    if (enableStacking && effectsList.size() > 1) {
                        sb.append("多特效叠加模式 (共 ").append(effectsList.size()).append(" 种特效):\n");
                        for (int i = 0; i < effectsList.size(); i++) {
                            if (effectsList.get(i) instanceof java.util.Map) {
                                @SuppressWarnings("unchecked")
                                java.util.Map<String, Object> effectMap = (java.util.Map<String, Object>) effectsList.get(i);
                                String type = (String) effectMap.get("type");
                                boolean enabled = (Boolean) effectMap.getOrDefault("enabled", true);
                                String color1 = (String) effectMap.getOrDefault("colore1", "");
                                String color2 = (String) effectMap.getOrDefault("colore2", "");
                                String color3 = (String) effectMap.getOrDefault("colore3", "");

                                String displayName = SuitManager.getEffectDisplayName(type);
                                String statusText = enabled ? "启用" : "禁用";

                                sb.append("  ").append(i + 1).append(". ").append(displayName)
                                        .append(" [").append(statusText).append("]\n");

                                if (!color1.isEmpty() || !color2.isEmpty() || !color3.isEmpty()) {
                                    sb.append("     颜色: ").append(color1.isEmpty() ? "默认" : color1)
                                            .append(", ").append(color2.isEmpty() ? "默认" : color2)
                                            .append(", ").append(color3.isEmpty() ? "默认" : color3).append("\n");
                                }
                            }
                        }
                    } else {
                        // 单一特效（新格式）
                        if (effectsList.get(0) instanceof java.util.Map) {
                            @SuppressWarnings("unchecked")
                            java.util.Map<String, Object> effectMap = (java.util.Map<String, Object>) effectsList.get(0);
                            String type = (String) effectMap.get("type");
                            boolean enabled = (Boolean) effectMap.getOrDefault("enabled", true);
                            String color1 = (String) effectMap.getOrDefault("colore1", "");
                            String color2 = (String) effectMap.getOrDefault("colore2", "");
                            String color3 = (String) effectMap.getOrDefault("colore3", "");

                            String displayName = SuitManager.getEffectDisplayName(type);
                            String statusText = enabled ? "启用" : "禁用";

                            sb.append("特效类型: ").append(displayName).append(" [").append(statusText).append("]\n");

                            if (!color1.isEmpty() || !color2.isEmpty() || !color3.isEmpty()) {
                                sb.append("颜色配置: ").append(color1.isEmpty() ? "默认" : color1)
                                        .append(", ").append(color2.isEmpty() ? "默认" : color2)
                                        .append(", ").append(color3.isEmpty() ? "默认" : color3).append("\n");
                            }
                        }
                    }
                } else {
                    sb.append("该套装暂无配置的特效\n");
                }
            } else if (Cuilian.Suit.contains(basePath + ".type")) {
                // 旧格式的特效配置
                String type = Cuilian.Suit.getString(basePath + ".type");
                String color1 = Cuilian.Suit.getString(basePath + ".color1", "");
                String color2 = Cuilian.Suit.getString(basePath + ".color2", "");
                String color3 = Cuilian.Suit.getString(basePath + ".color3", "");

                String displayName = SuitManager.getEffectDisplayName(type);
                sb.append("特效类型: ").append(displayName).append(" [启用]\n");

                if (!color1.isEmpty() || !color2.isEmpty() || !color3.isEmpty()) {
                    sb.append("颜色配置: ").append(color1.isEmpty() ? "默认" : color1)
                            .append(", ").append(color2.isEmpty() ? "默认" : color2)
                            .append(", ").append(color3.isEmpty() ? "默认" : color3).append("\n");
                }
            } else {
                sb.append("该套装暂无配置的特效\n");
            }
        } catch (Exception e) {
            sb.append("获取命名套装特效信息时出错: ").append(e.getMessage()).append("\n");
        }
    }

    private void loadSuitInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 套装计算 ===\n\n");

        if (isOnline && player != null) {
            // 在线玩家 - 计算实时套装等级
            int equipmentLevel = Cuilian.checkPlayerZBCL(player);
            int weaponLevel = Cuilian.checkPlayerWQCL(player);
            int suitLevel = Cuilian.getCLTZ(equipmentLevel, weaponLevel);

            sb.append("装备等级: ").append(equipmentLevel).append("星\n");
            sb.append("武器等级: ").append(weaponLevel).append("星\n");
            sb.append("套装等级: ").append(suitLevel).append("星\n");
            sb.append("(取装备和武器中较低的等级)\n\n");

            // 套装激活条件
            sb.append("=== 激活条件 ===\n");
            sb.append("需要套装等级 ≥ 6星才能激活特效\n");
            sb.append("当前状态: ").append(suitLevel >= 6 ? "满足条件" : "不满足条件").append("\n\n");
        } else {
            // 离线玩家 - 显示说明信息
            sb.append("玩家当前离线，无法计算实时套装等级。\n\n");
            sb.append("=== 套装等级计算规则 ===\n");
            sb.append("1. 装备等级 = 头盔、胸甲、护腿、靴子中最低的淬炼等级\n");
            sb.append("2. 武器等级 = 手持武器的淬炼等级 (非武器物品不计入)\n");
            sb.append("3. 套装等级 = 装备等级和武器等级中较低的等级\n");
            sb.append("4. 激活条件 = 套装等级 ≥ 6星\n\n");
        }

        // 命名套装检查
        String namedSuit = SuitManager.getPlayerSuit(playerName);
        sb.append("=== 命名套装 ===\n");
        if (namedSuit != null) {
            sb.append("当前套装: ").append(namedSuit).append("\n");
            sb.append("套装类型: 命名套装\n");
            sb.append("说明: 使用自定义套装名称，不依赖淬炼等级\n");
        } else {
            sb.append("当前套装: 淬炼等级套装\n");
            sb.append("套装类型: 等级套装\n");
            sb.append("说明: 根据装备和武器的淬炼等级自动计算\n");
        }

        suitInfoArea.setText(sb.toString());
        suitInfoArea.setCaretPosition(0);
    }

    private String getItemInfo(ItemStack item, String type) {
        if (item == null || item.getType().name().equals("AIR")) {
            return "无";
        }

        StringBuilder info = new StringBuilder();

        // 物品类型和材质
        info.append(item.getType().name());

        // 物品名称
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            info.append(" (").append(item.getItemMeta().getDisplayName()).append(")");
        }

        // 淬炼等级
        int level = Cuilian.getCuilianlevel(item);
        if (level > 0) {
            info.append("\n  淬炼等级: ").append(level).append("星");
        } else {
            info.append("\n  淬炼等级: 无");
        }

        // 数量
        info.append("\n  数量: ").append(item.getAmount());

        // Lore信息（简化显示）
        if (item.hasItemMeta() && item.getItemMeta().hasLore()) {
            List<String> lore = item.getItemMeta().getLore();
            info.append("\n  附加信息: ");
            int count = 0;
            for (String line : lore) {
                if (count >= 3) {
                    info.append("\n    ...(还有").append(lore.size() - 3).append("行)");
                    break;
                }
                info.append("\n    ").append(line.replaceAll("§[0-9a-fk-or]", ""));
                count++;
            }
        }

        return info.toString();
    }

    /**
     * 打开游戏风格装备预览
     */
    private void openGameStylePreview() {
        try {
            if (isOnline && player != null) {
                // 在线玩家
                GameStyleEquipmentPreview gamePreview = new GameStyleEquipmentPreview((JFrame) getOwner(), player);
                gamePreview.setVisible(true);
            } else if (offlinePlayer != null) {
                // 离线玩家
                GameStyleEquipmentPreview gamePreview = new GameStyleEquipmentPreview((JFrame) getOwner(), offlinePlayer);
                gamePreview.setVisible(true);
            } else {
                JOptionPane.showMessageDialog(this, "无法获取玩家信息！", "错误", JOptionPane.ERROR_MESSAGE);
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "打开游戏风格预览时出错: " + e.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
            e.printStackTrace();
        }
    }
}
