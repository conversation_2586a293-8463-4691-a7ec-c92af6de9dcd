#################################可用附魔ID################################
#保护(0)、火焰保护(1)、掉落保护(2)、爆炸保护(3)、弹射物保护(4)            #
#水下呼吸(5)、水下挖掘(6)、荆棘(7)、锋利(16)、亡灵杀手(17)                #
#节肢杀手(18)、击退(19)、火焰附加(20)、抢夺(21)、效率(32)                 #
#精准采集(33)、耐久(34)、时运(35)、力量(48)、冲击(49)、火矢(50)           #
#无限(51)                                                                 #
###########################################################################

#################################可用怪物ID################################
#(50)爬行者 (51)骷髅 (52)蜘蛛 (53)巨人 (54)僵尸 (55)史莱姆                #
#(56)恶魂 (57)僵尸猪人 (58)末影人 (59)洞穴蜘蛛 (60)蠹虫                   #
#(61)烈焰人 (62)岩浆怪 (63)末影龙 (64)凋灵 (66)女巫                       #
#################################可用怪物ID################################

###############
#####权 限#####
###############

Admin: "per.qh.admin"

###############
#####配 置#####
###############

id:
  items:
#格式: <物品ID>: <附魔ID>
#例子 276: 16
#强化276的物品 附魔效果为16(ID观看配置文件顶部)
    268: 16
    272: 16
    267: 16
    283: 16
    276: 16
    399: 16
    352: 17
    298: 0
    299: 0
    300: 0
    301: 0
    302: 0
    303: 0
    304: 0
    305: 0
    306: 0
    307: 0
    308: 0
    309: 0
    310: 0
    311: 0
    312: 0
    313: 0
    314: 0
    315: 0
    316: 0
    317: 0
    261: 48
    346: 34
    269: 32
    270: 32
    271: 32
    273: 32
    274: 32
    275: 32
    277: 32
    278: 32
    279: 32
    284: 32
    285: 32
    286: 32
    256: 32
    257: 32
    258: 32
    359: 32
    262: 18
    369: 20

chance:
  # 强化几率设置
  # 基础强化几率 (不使用强化石时的几率)
  # 支持两种格式的混合配置:
  # 1. 整数格式: "等级: 几率" - 指定具体等级的基础几率
  # 2. 字符串格式: "等级范围: 几率" - 支持范围和单个等级混合
  #
  # 范围格式说明:
  # - "1-5: 100": 表示1到5级的基础几率都是100%
  # - "10: 50": 表示只有10级的基础几率是50%
  # - "15-20: 30": 表示15到20级的基础几率都是30%
  #
  # 如果某个等级没有设置，会使用最接近的较低等级的几率
  #
  # 示例配置:
  # default: {"1-5": 100, "6-10": 50, "15": 30, "20-30": 10} # 混合格式
  # default: {1: 100, 2: 100, 3: 85, 4: 70} # 具体等级格式
  default:
    "1-2": 100   # 1-2级基础几率100%
    3: 85        # 3级基础几率85%
    4: 70        # 4级基础几率70%
    "5-6": 55    # 5-6级基础几率55%
    7: 40        # 7级基础几率40%
    "8-9": 25    # 8-9级基础几率25%
    10: 15       # 10级基础几率15%
    "11-15": 10  # 11-15级基础几率10%
    "16-20": 5   # 16-20级基础几率5%
    "21+": 1     # 21级及以上基础几率1%
    # 可以继续添加更多设置
    # "30-40": 0.5  # 30-40级基础几率0.5%
    # 50: 0.1       # 50级基础几率0.1%

  # 强化石加成 (会与基础几率相加)
  # 普通强化石加成
  normal: 0
  # 幸运强化石加成
  luck: 15
  # 安全强化石加成
  safe: 20
  # VIP强化石加成
  vip: 30
  # 管理员强化石加成
  admin: 100
  # 直升符咒成功几率，默认80%
  direct_upgrade: 80

# 公告设置
broadcast:
  # 强化石成功公告的等级设置
  # 支持两种格式:
  # 1. 整数列表: [6, 10, 15, 20, 25, 30] - 只有这些等级会发送公告
  # 2. 字符串列表: ["1-5", "10-15", "20", "30-40"] - 支持范围和单个等级混合
  #
  # 范围格式说明:
  # - "1-10": 表示1到10级之间的所有等级都发送公告
  # - "20": 表示只有20级发送公告
  # - "30-50": 表示30到50级之间的所有等级都发送公告
  # - ["0"]: 表示所有等级都发送公告
  # - []: 空列表表示不发送任何公告
  #
  # 示例配置:
  # enhancement_levels: ["1-10", "15-25", "30", "40-50"] # 纯范围格式
  # enhancement_levels: ["6", "10", "15", "20", "25", "30"] # 纯具体等级格式
  # enhancement_levels: ["1-5", "10", "15-20", "25", "30-40"] # 混合格式（推荐）
  # enhancement_levels: [6, 10, 15, 20, 25, 30] # 旧的整数格式（仍然支持）
  enhancement_levels: ["1-5", "10", "15-20", "25", "30-40"]

# 突破等级设置
breakthrough:
  # 普通强化石最高可强化到的等级
  max_normal_level: 29
  # 突破等级设置，格式为 "突破石等级: 装备需要达到的等级"
  levels:
    30: 29  # 使用30级突破石时，装备需要达到29级
    50: 49  # 使用50级突破石时，装备需要达到49级
    70: 69  # 使用70级突破石时，装备需要达到69级
    100: 99 # 使用100级突破石时，装备需要达到99级

  # 突破石失败惩罚设置
  failure_penalty:
    # 失败后降级几率（百分比）
    downgrade_chance: 70
    # 降级数量
    downgrade_levels: 1
    # 是否有几率装备自毁（百分比，0表示不会自毁）
    destroy_chance: 0

  # 不同等级突破石的成功几率设置
  # 格式: "突破石等级: 成功几率百分比"
  success_chance:
    30: 80   # 30级突破石成功几率80%
    50: 70   # 50级突破石成功几率70%
    70: 60   # 70级突破石成功几率60%
    100: 50  # 100级突破石成功几率50%
    default: 50  # 其他等级默认成功几率50%

# 装备最高强化等级限制
# 格式: <物品ID>: <最高强化等级>
# 不在此列表中的装备将没有强化等级限制
max_enhancement_levels:
  # 示例: 钻石剑最高强化到50级
  276: 120
  268: 120
  272: 120
  267: 120
  283: 120
  399: 200
  # 示例: 钻石头盔最高强化到30级
  310: 150
  # 示例: 钻石胸甲最高强化到30级
  311: 150
  # 示例: 钻石护腿最高强化到30级
  312: 150
  # 示例: 钻石靴子最高强化到30级
  313: 150
  298: 150
  299: 150
  300: 150
  301: 150
  302: 150
  303: 150
  304: 150
  305: 150
  306: 150
  307: 150
  308: 150
  309: 150
  314: 150
  315: 150
  316: 150
  317: 150
  261: 120
  352: 20
  280: 20
  346: 20
  269: 20
  270: 20
  271: 20
  273: 20
  274: 20
  275: 20
  277: 20
  278: 20
  279: 20
  280: 19
  284: 20
  285: 20
  286: 20
  256: 20
  257: 20
  258: 20
  359: 20
  262: 20
  369: 20

# 战力系统设置
power:
  # 每级强化对应的战力值
  per_level: 10
  # 战力指令冷却时间（秒）
  cooldown: 10
  # 排行榜更新间隔（分钟）
  ranking_update_interval: 5
  # 是否显示调试日志
  debug_logs: false

# 日志设置
logging:
  # 是否在控制台显示详细的强化操作日志
  console_logs: false

# 消息设置
messages:
  # 强化失败时是否显示恭喜消息
  show_congratulation_on_failure: false

drop:
#掉落设置
  block: true
#打破方块掉落是否启用
  blocks:
#方块掉落配置
    - "17 normal 5"
    - "1 normal 5"
#格式: <方块ID> <类型> <几率 > ID请看最上面
#例子: - "1 normal 10"
#为打破ID为1的方块,有10%的几率掉落普通强化石
#类型: normal luck safe vip (大小写不敏感)
  mob: true
#怪物掉落启用
  mobs:
#怪物掉落配置
    - "50 normal 10"
    - "51 normal 10"
    - "52 normal 10"
    - "53 normal 10"
    - "54 normal 10"
    - "55 normal 10"
    - "56 luck 10"
    - "57 luck 10"
    - "58 luck 10"
    - "59 normal 10"
    - "60 luck 10"
    - "61 luck 10"
    - "62 luck 10"
    - "63 safe 100"
    - "64 safe 100"
    - "65 normal 10"
    - "66 luck 10"
#格式: <怪物ID> <类型> <几率 > ID请看最上面
#例子: - "54 normal 10"
#干掉ID为54的怪物,有10%的几率掉落普通强化石
#类型: normal luck safe vip (大小写不敏感)

style:
#强化风格设置
  color: ""
#颜色
  a: "§c§l◆"
#有等级
  b: "§7§l◇"
#无等级
  # 每10级的符号颜色设置
  colors:
    level_10: "§a"  # 1-10级：绿色
    level_20: "§b"  # 11-20级：青色
    level_30: "§e"  # 21-30级：黄色
    level_40: "§6"  # 31-40级：金色
    level_50: "§c"  # 41-50级：红色
    level_60: "§d"  # 51-60级：粉色
    level_70: "§5"  # 61-70级：紫色
    level_80: "§9"  # 71-80级：蓝色
    level_90: "§1"  # 81-90级：深蓝色
    level_100: "§4" # 91-100级：深红色
  # 是否显示强化符号相关的调试日志
  debug_logs: false

###############
#####语 言#####
###############

lang_1: "§c此命令只能由玩家发出"
lang_2: "§7==------->> §e【§c强化系统§e】§7 <<-------=="
lang_3: "§b-->§6/qh get <类型> <数量> §7- §a§l获得一个强化素材"
lang_4: "§b-->§6/qh power             §7- §a§l查看自己的战力值"
lang_5: "§b-->§6/qh reload            §7- §a§l重载插件"
lang_6: "§e【§c强化系统§e】 §7已发送到背包.."
lang_7: "§e【§c强化系统§e】 §e重载成功！"
lang_8: "§e【§c强化系统§e】 §7已掉落 §a§l1 §7颗强化素材！"
lang_9: "§e【§c强化系统§e】 §e恭喜你！！  强化成功.. 增加 §a§l1 §e级... 当前装备等级: §a§l{0}"
lang_10: "§e【§c强化系统§e】 §c很遗憾....强化失败... 损失 §e1 §c级"
lang_11: "§e【§c强化系统§e】 §7已经是最高等级了.. §4(继续强化装备消失)."
lang_12: "§e【§c强化系统§e】 §7恭喜玩家 §b{0} §7把装备强化到了 §a§l{1} §7级！！！"
lang_13: "§c§l【强化信息】:"
lang_14: "§f§l等级: §a§l+{0} §f§l级.."
lang_15: "§e【§c强化系统§e】 §7已经是最高等级了.. §4(继续强化装备消失)."
lang_16: "§e【§c强化系统§e】 §c很遗憾....强化失败... 损失 §e&l0 §c级"
lang_17: "§e【§c战力系统§e】 §b您的战力值为: §6{0}"
lang_18: "§e【§c战力系统§e】 §c战力查询冷却中，请等待{0}秒后再试"
lang_19: "§e【§c战力系统§e】 §7战力详情:"
lang_20: "§e【§c战力系统§e】 §7- {0}: §a+{1} §7(等级: {2})"
lang_21: "§e【§c战力系统§e】 §7总战力: §6{0}"
lang_22: "§e【§c强化系统§e】 §4警告：装备强化等级过高，强化失败导致装备自毁！"
lang_23: "§e【§c强化系统§e】 §c玩家 §e{0} §c的装备因强化失败而自毁了！"

# 公告消息配置
# 可用变量:
#   {player} - 玩家名称
#   {level} - 强化等级 (会自动应用对应等级的颜色，每10级一个颜色)
#   {item} - 装备名称
#
# 等级颜色说明:
#   {level} 变量会自动根据等级应用对应的颜色:
#   1-10级: 绿色 (§a)    11-20级: 青色 (§b)    21-30级: 黄色 (§e)
#   31-40级: 金色 (§6)   41-50级: 红色 (§c)    51-60级: 粉色 (§d)
#   61-70级: 紫色 (§5)   71-80级: 蓝色 (§9)    81-90级: 深蓝色 (§1)
#   91-100级: 深红色 (§4)
#   颜色可在 style.colors 部分自定义
broadcast_messages:
  # 普通强化成功公告
  enhancement_success: "§e【§c强化系统§e】 §7恭喜玩家 §b{player} §7把装备强化到了 {level} §7级！！！"

  # 直升符咒成功公告
  direct_upgrade_success: "§e【§c强化系统§e】 §6恭喜玩家 §b{player} §6使用直升符咒将装备直升到了 {level} §6级！！！"

  # 突破石成功公告
  breakthrough_success: "§e【§c强化系统§e】 §d恭喜玩家 §b{player} §d使用突破石将装备突破到了 {level} §d级！！！"

  # 强化棒成功公告
  enhancement_rod_success: "§e【§c强化系统§e】 §9恭喜玩家 §b{player} §9使用强化棒将装备提升到了 {level} §9级！！！"
###############
#####物 品#####
###############

items:
#强化物品设置
  main_normal:
    ==: org.bukkit.inventory.ItemStack
    type: WOOD
    meta:
      ==: ItemMeta
      meta-type: UNSPECIFIC
      display-name: §7§l【粗糙的强化素材】
      lore:
      - ""
      - "§f -->> §f§l普通几率 §f<<--"
      - ""
      - "§e【§c使用方法§e】:"
      - "§f§l - §7放到熔炉里面,烧制武器/装备."
      - "§f§l - §d(必须是满耐久装备)"
      - ""
      - "§4【强化 +6 装备失败后: 装备自毁】"
  main_luck:
    ==: org.bukkit.inventory.ItemStack
    type: LOG
    meta:
      ==: ItemMeta
      meta-type: UNSPECIFIC
      display-name: §f§l【普通的强化素材】
      lore:
      - ""
      - "§f -->> §a§l额外增加 §e§l15% §a§l几率 §f<<--"
      - ""
      - "§e【§c使用方法§e】:"
      - "§f§l - §7放到熔炉里面,烧制武器/装备."
      - "§f§l - §d(必须是满耐久装备)"
      - ""
      - "§4【强化 +6 装备失败后: 装备自毁】"
  main_safe:
    ==: org.bukkit.inventory.ItemStack
    type: COAL
    meta:
      ==: ItemMeta
      meta-type: UNSPECIFIC
      display-name: §a§l【优秀的强化素材】
      lore:
      - ""
      - "§f -->> §b§l额外增加 §e§l20% §b§l几率 §f<<--"
      - ""
      - "§e【§c使用方法§e】:"
      - "§f§l - §7放到熔炉里面,烧制武器/装备."
      - "§f§l - §d(必须是满耐久装备)"
      - ""
      - "§e【强化装备失败后,不会自毁】"
  main_vip:
    ==: org.bukkit.inventory.ItemStack
    type: COAL_BLOCK
    meta:
      ==: ItemMeta
      meta-type: UNSPECIFIC
      display-name: §b§l【超级强化素材】
      lore:
      - ""
      - "§f -->> §6§l额外增加 §e§l30% §6§l几率 §f<<--"
      - ""
      - "§e【§c使用方法§e】:"
      - "§f§l - §7放到熔炉里面,烧制武器/装备."
      - "§f§l - §d(必须是满耐久装备)"
      - ""
      - "§e【强化装备失败后,不会自毁】"
  main_admin:
    ==: org.bukkit.inventory.ItemStack
    type: COAL_BLOCK
    meta:
      ==: ItemMeta
      meta-type: UNSPECIFIC
      display-name: §c§l管理强化石
      lore:
      - §a【管理强化石】
      - §5(直接强化到最高等级)
      - §a【用法】
      - §c放到熔炉,燃烧武器/装备

  # 直升符咒配置
  direct_upgrade:
    # 物品材质 (Minecraft材质名称，如: STICK, COAL, DIAMOND等)
    material: STICK

    # 物品名称格式
    # 可用变量:
    #   {level} - 直升等级 (如: 29, 49, 69, 99)
    #   {chance} - 成功几率 (从配置文件中读取)
    #   {required_level} - 装备需要达到的等级 (从level_requirements中读取)
    name: "§e『§6强化直升符咒§e』 §f- §6§l{level}"

    # 物品描述 (支持多行)
    # 可用变量:
    #   {level} - 直升等级
    #   {chance} - 成功几率
    #   {required_level} - 装备需要达到的等级
    lore:
      - "§7========================"
      - "§f - §e成功几率: §a{chance}%"
      - "§7========================"
      - "§f - §e成功后, 直升 §b§l{level} §e级"
      - "§7========================"
      - "§e【§c使用方法§e】:"
      - "§f - §7放到熔炉内,炼制武器,装备"
      - "§f - §d(必须是满耐久装备)"
      - "§7========================"

    # 是否添加发光效果 (true/false)
    glow: true

    # 等级要求配置
    # 格式: "直升等级: 装备需要达到的等级"
    # 说明: 使用指定等级的直升符咒时，装备必须达到对应的等级才能使用
    level_requirements:
      10: 0   # 使用10级直升符咒时，装备无需达到任何等级（0级即可）
      29: 20  # 使用29级直升符咒时，装备需要达到20级
      49: 30  # 使用49级直升符咒时，装备需要达到30级
      69: 50  # 使用69级直升符咒时，装备需要达到50级
      99: 70  # 使用99级直升符咒时，装备需要达到70级

    # 公告设置
    # 直升符咒成功公告的等级设置
    # 支持两种格式:
    # 1. 整数列表: [29, 49, 69, 99] - 只有这些等级会发送公告
    # 2. 字符串列表: ["20-30", "40-50", "60-70", "90-100"] - 支持范围格式
    #
    # 范围格式说明:
    # - "20-30": 表示20到30级之间的所有等级都发送公告
    # - "50": 表示只有50级发送公告
    # - ["0"]: 表示所有等级都发送公告
    # - []: 空列表表示不发送任何公告
    #
    # 示例: ["20-30", "40-50", "60-70", "90-100"] 表示这些范围内的等级会发送公告
    # 混合格式示例: ["10", "25-30", "49", "65-70", "99"] 表示10级单独、25-30级范围、49级单独、65-70级范围、99级单独
    broadcast_levels: ["10", "25-30", "49", "65-70", "99"]

  # 突破石配置
  breakthrough_stone:
    # 物品材质 (Minecraft材质名称，如: COAL_BLOCK, OBSIDIAN, DIAMOND_BLOCK等)
    material: COAL_BLOCK

    # 物品名称格式
    # 可用变量:
    #   {level} - 突破等级 (如: 30, 50, 70, 100)
    #   {rarity} - 稀有度文本 (从rarity_text中读取)
    #   {chance} - 成功几率 (从配置文件中读取)
    #   {required_level} - 装备需要达到的等级
    #   {downgrade_chance} - 失败后降级几率
    #   {downgrade_levels} - 降级数量
    name: '{rarity}'

    # 物品描述 (支持多行)
    # 可用变量:
    #   {level} - 突破等级
    #   {rarity} - 稀有度文本
    #   {chance} - 成功几率
    #   {required_level} - 装备需要达到的等级
    #   {downgrade_chance} - 失败后降级几率
    #   {downgrade_levels} - 降级数量
    lore:
      - "§7============================"
      - "§f §l - §e突破上限 §c§l{level} §e级....."
      - "§7============================"
      - "§e【§c使用方法§e】："
      - "§f -  §7放到熔炉里面烧制,武器/装备"
      - "§f -  §d(必须是满耐久装备)"
      - "§f -  §4(无限耐久,无法强化？ 请修理一下后强化...)"
      - "§7============================"
      - "§f -  §9烧制失败后 §b50% §9几率, 掉一级.."
      - "§f -  §9烧制失败后 §b50% §9几率, 不掉任何等级"
      - "§7============================"

    # 是否添加发光效果 (true/false)
    glow: true

    # 稀有度文本配置
    # 格式: "突破等级: 显示文本"
    # 说明: 根据突破石等级显示不同的稀有度文本，用于{rarity}变量
    rarity_text:
      30: "§a『§b强化突破石§a』 §f- §9稀有"    # 30级突破石
      50: "§9『§5强化突破石§9』 §f- §d史诗"    # 50级突破石
      70: "§e『§c强化突破石§e』 §f- §6传说"    # 70级突破石
      100: "§d『§4强化突破石§d』 §f- §c神级至尊" # 100级突破石
      default: "§4§l无上至尊" # 其他等级的默认文本

    # 公告设置
    # 突破石成功公告的等级设置
    # 支持两种格式:
    # 1. 整数列表: [30, 50, 70, 100] - 只有这些等级会发送公告
    # 2. 字符串列表: ["30-35", "50-55", "70-75", "100"] - 支持范围格式
    #
    # 范围格式说明:
    # - "30-40": 表示30到40级之间的所有等级都发送公告
    # - "100": 表示只有100级发送公告
    # - ["0"]: 表示所有等级都发送公告
    # - []: 空列表表示不发送任何公告
    #
    # 示例: ["30-35", "50-55", "70-75", "100"] 表示这些范围内的等级会发送公告
    # 混合格式示例: ["30", "45-55", "70", "90-100"] 表示30级单独、45-55级范围、70级单独、90-100级范围
    broadcast_levels: ["30", "45-55", "70", "90-100"]

  # 强化棒配置
  enhancement_rod:
    # 物品材质 (Minecraft材质名称，如: BLAZE_ROD, STICK, DIAMOND等)
    material: BLAZE_ROD

    # 物品名称格式
    # 可用变量:
    #   {level} - 提升等级 (如: 1, 2, 3, 5, 10等)
    #   {chance} - 成功几率 (从配置文件中读取)
    #   {downgrade_chance} - 失败后降级几率
    #   {downgrade_levels} - 降级数量
    #   {destroy_chance} - 装备自毁几率
    name: "§e『§b强化棒§e』 §f- §6{level}"

    # 物品描述 (支持多行)
    # 可用变量:
    #   {level} - 提升等级
    #   {chance} - 成功几率
    #   {downgrade_chance} - 失败后降级几率
    #   {downgrade_levels} - 降级数量
    #   {destroy_chance} - 装备自毁几率
    lore:
      - "§7============================"
      - "§f - §e成功几率: §a{chance}%"
      - "§7============================"
      - "§f - §e成功后, 提升 §b§l{level} §e级"
      - "§7============================"
      - "§e【§c使用方法§e】："
      - "§f -  §7放到熔炉里面烧制,武器/装备"
      - "§f -  §d(必须是满耐久装备)"
      - "§f -  §4(无限耐久,无法强化？ 请修理一下后强化...)"
      - "§7============================"
      - "§f -  §c注意: 不能突破需要突破的等级限制"
      - "§f -  §9强化失败后 §b50% §9几率, 掉一级.."
      - "§f -  §9强化失败后 §b50% §9几率, 不掉任何等级"
      - "§7============================"

    # 是否添加发光效果 (true/false)
    glow: true

    # 默认成功几率 (0-100的整数)
    # 说明: 当使用指令创建强化棒时，如果没有指定几率，则使用此默认值
    default_chance: 80

    # 失败惩罚设置
    failure_penalty:
      # 失败后降级几率（百分比，0-100的整数）
      # 说明: 强化棒使用失败时，有此几率降低装备等级
      downgrade_chance: 70

      # 降级数量 (正整数)
      # 说明: 当触发降级惩罚时，装备等级降低的数量
      downgrade_levels: 1

      # 装备自毁几率（百分比，0-100的整数，0表示不会自毁）
      # 说明: 当触发降级惩罚时，有此几率直接销毁装备
      destroy_chance: 0

    # 公告设置
    # 强化棒成功公告的等级设置
    # 支持两种格式:
    # 1. 整数列表: [10, 20, 30, 40, 50] - 只有这些等级会发送公告
    # 2. 字符串列表: ["10-15", "20-25", "30-35", "40-50"] - 支持范围格式
    #
    # 范围格式说明:
    # - "10-20": 表示10到20级之间的所有等级都发送公告
    # - "50": 表示只有50级发送公告
    # - ["0"]: 表示所有等级都发送公告
    # - []: 空列表表示不发送任何公告
    #
    # 示例: ["10-15", "20-25", "30-35", "40-50"] 表示这些范围内的等级会发送公告
    # 混合格式示例: ["5-15", "20", "25-35", "40", "45-50"] 表示5-15级范围、20级单独、25-35级范围、40级单独、45-50级范围
    broadcast_levels: ["5-15", "20", "25-35", "40", "45-50"]