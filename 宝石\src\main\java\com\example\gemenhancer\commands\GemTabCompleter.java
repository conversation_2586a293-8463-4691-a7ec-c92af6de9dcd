package com.example.gemenhancer.commands;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

public class GemTabCompleter implements TabCompleter {

    private final List<String> COMMANDS = Arrays.asList("reload", "give", "info", "help");

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            // 第一个参数：子命令
            String partialCommand = args[0].toLowerCase();
            for (String cmd : COMMANDS) {
                if (cmd.startsWith(partialCommand)) {
                    if (cmd.equals("reload") && sender.hasPermission("gemenhancer.reload")) {
                        completions.add(cmd);
                    } else if (cmd.equals("give") && sender.hasPermission("gemenhancer.give")) {
                        completions.add(cmd);
                    }
                }
            }
        } else if (args.length == 2 && args[0].equalsIgnoreCase("give")) {
            // 第二个参数：玩家名
            String partialPlayerName = args[1].toLowerCase();
            completions = Bukkit.getOnlinePlayers().stream()
                    .map(Player::getName)
                    .filter(name -> name.toLowerCase().startsWith(partialPlayerName))
                    .collect(Collectors.toList());
        } else if (args.length == 3 && args[0].equalsIgnoreCase("give")) {
            // 第三个参数：数量建议
            completions.add("1");
            completions.add("5");
            completions.add("10");
            completions.add("64");
        }

        return completions;
    }
}
