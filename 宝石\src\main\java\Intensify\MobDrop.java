/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.entity.EntityType
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.entity.EntityDeathEvent
 *  org.bukkit.inventory.ItemStack
 */
package Intensify;

import Intensify.Intensify;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.inventory.ItemStack;

public class MobDrop
        implements Listener {
    private Intensify p;

    public MobDrop() {
    }

    public MobDrop(Intensify p) {
        this.p = p;
    }

    @EventHandler
    public void onEntityDeath(EntityDeathEvent e) {
        if (e.getEntity() instanceof Player) {
            return;
        }
        if (!this.p.config.getBoolean("drop.block")) {
            return;
        }
        EntityType et = e.getEntityType();
        int id = -1;
        int i = 0;
        while (i < Intensify.getMobId().size()) {
            int a = Integer.parseInt(Intensify.getMobId().get(i).split(" ")[0]);
            if (a == et.getTypeId()) {
                id = i;
                break;
            }
            ++i;
        }
        if (id != -1) {
            ItemStack item = null;
            int key = this.p.rm.nextInt(100);
            String type = Intensify.getMobId().get(id).split(" ")[1].toLowerCase();
            int chance = Integer.parseInt(Intensify.getMobId().get(id).split(" ")[2]);
            switch (type.hashCode()) {
                case -1039745817: {
                    item = this.p.normalItem;
                    break;
                }
                case 3333041: {
                    item = this.p.luckItem;
                    break;
                }
                case 3522445: {
                    item = this.p.safeItem;
                    break;
                }
                case 116765: {
                    item = this.p.vipItem;
                }
            }
            if (key < chance) {
                item.setAmount(1);
                e.getDrops().add(item);
            }
        }
    }
}
