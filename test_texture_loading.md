# 装备贴图加载测试

## 功能说明
游戏风格装备预览界面现在支持从 `lib` 文件夹加载真实的装备贴图。

## 当前支持的贴图
根据 `lib` 文件夹中的文件，目前支持以下装备的真实贴图：

1. **钻石头盔** - `diamond_helmet.png`
2. **钻石胸甲** - `diamond_chestplate.png`
3. **钻石护腿** - `diamond_leggings.png`
4. **钻石靴子** - `diamond_boots.png`

## 贴图加载逻辑

### 1. 优先级
- **第一优先级**：从 `lib` 文件夹加载真实贴图
- **第二优先级**：如果没有找到贴图文件，使用默认的颜色块图标

### 2. 文件路径
贴图文件应放置在项目根目录的 `lib` 文件夹中：
```
cuilian2.6(4)/
├── lib/
│   ├── diamond_helmet.png
│   ├── diamond_chestplate.png
│   ├── diamond_leggings.png
│   └── diamond_boots.png
└── src/
```

### 3. 图片处理
- 自动缩放到 64x64 像素
- 支持 PNG 格式
- 使用平滑缩放算法保证图片质量

## 测试步骤

### 1. 启动游戏风格预览
1. 打开主GUI界面
2. 点击"游戏风格预览"按钮
3. 选择一个玩家

### 2. 验证贴图显示
1. 检查钻石装备是否显示真实贴图
2. 检查其他装备是否显示颜色块图标
3. 验证图标大小是否合适（64x64）

### 3. 控制台日志
成功加载贴图时会在控制台输出：
```
成功加载贴图: diamond_helmet.png
```

找不到贴图文件时会输出：
```
贴图文件不存在: C:\Users\<USER>\Desktop\cuilian2.6(4)\lib\iron_helmet.png
```

## 扩展支持

### 添加更多贴图
要支持更多装备的贴图，需要：

1. **添加贴图文件**到 `lib` 文件夹
2. **更新映射**在 `GameStyleEquipmentPreview.java` 的 `getTextureFileName` 方法中

例如，要添加铁装备支持：
```java
case IRON_HELMET:
    return "iron_helmet.png";
case IRON_CHESTPLATE:
    return "iron_chestplate.png";
// ... 其他铁装备
```

### 支持的文件格式
- PNG（推荐）
- JPG/JPEG
- GIF

### 建议的贴图尺寸
- 原始尺寸：16x16 到 128x128 像素
- 显示尺寸：自动缩放到 64x64 像素

## 错误处理
- 文件不存在：使用默认颜色块图标
- 文件损坏：使用默认颜色块图标
- 读取失败：在控制台输出错误信息并使用默认图标

## 性能优化
- **图标缓存**：已加载的图标会被缓存，避免重复加载
- **延迟加载**：只在需要时加载图标
- **内存管理**：使用静态缓存减少内存占用
