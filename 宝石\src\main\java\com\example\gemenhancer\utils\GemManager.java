package com.example.gemenhancer.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import com.example.gemenhancer.GemEnhancer;

public class GemManager {

    private final GemEnhancer plugin;
    private final Random random;
    private int successRate;
    private ItemStack gemItem;
    private Map<String, Map<Enchantment, Integer>> equipmentEnchantments;

    public GemManager(GemEnhancer plugin) {
        this.plugin = plugin;
        this.random = new Random();
        this.equipmentEnchantments = new HashMap<>();
        reload();
    }

    /**
     * 重新加载配置
     */
    public void reload() {
        // 加载成功几率
        successRate = plugin.getConfig().getInt("success-rate", 50);

        // 加载宝石物品
        loadGemItem();

        // 加载装备附魔配置
        loadEquipmentEnchantments();
    }

    /**
     * 加载宝石物品
     */
    private void loadGemItem() {
        ConfigurationSection gemSection = plugin.getConfig().getConfigurationSection("gem");
        if (gemSection == null)
            return;

        Material material;
        try {
            material = Material.valueOf(gemSection.getString("material", "EMERALD").toUpperCase());
        } catch (IllegalArgumentException e) {
            material = Material.EMERALD;
            plugin.getLogger().warning("无效的宝石材质，使用默认的绿宝石");
        }

        gemItem = new ItemStack(material);
        ItemMeta meta = gemItem.getItemMeta();

        if (meta != null) {
            // 设置名称
            String name = gemSection.getString("name", "&b强化宝石");
            meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', name));

            // 设置描述
            List<String> lore = new ArrayList<>();
            for (String line : gemSection.getStringList("lore")) {
                lore.add(ChatColor.translateAlternateColorCodes('&',
                        line.replace("%chance%", String.valueOf(successRate))));
            }
            meta.setLore(lore);

            // 添加发光效果
            meta.addEnchant(Enchantment.DURABILITY, 1, true);
            meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);

            gemItem.setItemMeta(meta);
        }
    }

    /**
     * 加载装备附魔配置
     */
    private void loadEquipmentEnchantments() {
        equipmentEnchantments.clear();

        ConfigurationSection equipmentSection = plugin.getConfig().getConfigurationSection("equipment");
        if (equipmentSection == null)
            return;

        for (String equipmentType : equipmentSection.getKeys(false)) {
            ConfigurationSection enchantmentsSection = equipmentSection
                    .getConfigurationSection(equipmentType + ".enchantments");
            if (enchantmentsSection == null)
                continue;

            Map<Enchantment, Integer> enchantments = new HashMap<>();

            for (String enchantmentName : enchantmentsSection.getKeys(false)) {
                try {
                    Enchantment enchantment = Enchantment.getByName(enchantmentName);
                    if (enchantment != null) {
                        int level = enchantmentsSection.getInt(enchantmentName);
                        enchantments.put(enchantment, level);
                    } else {
                        plugin.getLogger().warning("无效的附魔名称: " + enchantmentName);
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("加载附魔时出错: " + e.getMessage());
                }
            }

            equipmentEnchantments.put(equipmentType, enchantments);
        }
    }

    /**
     * 获取宝石物品
     *
     * @return 宝石物品
     */
    public ItemStack getGemItem() {
        return gemItem.clone();
    }

    /**
     * 检查物品是否为宝石
     *
     * @param item 要检查的物品
     * @return 是否为宝石
     */
    public boolean isGem(ItemStack item) {
        // 检查物品是否为空或类型是否匹配
        if (item == null || item.getType() != gemItem.getType())
            return false;

        ItemMeta itemMeta = item.getItemMeta();
        ItemMeta gemMeta = gemItem.getItemMeta();

        if (itemMeta == null || gemMeta == null)
            return false;

        // 检查名称
        if (!itemMeta.hasDisplayName() || !gemMeta.hasDisplayName())
            return false;
        if (!itemMeta.getDisplayName().equals(gemMeta.getDisplayName()))
            return false;

        // 检查lore
        if (!itemMeta.hasLore() || !gemMeta.hasLore())
            return false;

        List<String> itemLore = itemMeta.getLore();
        List<String> gemLore = gemMeta.getLore();

        if (itemLore == null || gemLore == null || itemLore.size() != gemLore.size())
            return false;

        // 检查第一行lore是否匹配（用于识别是否为强化宝石）
        if (itemLore.size() > 0 && gemLore.size() > 0) {
            return itemLore.get(0).equals(gemLore.get(0));
        }

        return false;
    }

    /**
     * 获取宝石燃烧时间
     *
     * @return 燃烧时间（tick）
     */
    public int getGemBurnTime() {
        return plugin.getConfig().getInt("gem.burn-time", 200);
    }

    /**
     * 获取装备类型
     *
     * @param item 装备物品
     * @return 装备类型，如果不是可强化装备则返回null
     */
    public String getEquipmentType(ItemStack item) {
        if (item == null)
            return null;

        Material type = item.getType();
        String name = type.name();

        if (name.endsWith("_HELMET"))
            return "HELMET";
        if (name.endsWith("_CHESTPLATE"))
            return "CHESTPLATE";
        if (name.endsWith("_LEGGINGS"))
            return "LEGGINGS";
        if (name.endsWith("_BOOTS"))
            return "BOOTS";

        if (name.endsWith("_SWORD") || name.equals("TRIDENT"))
            return "SWORD";
        if (name.equals("BOW"))
            return "BOW";
        if (name.equals("CROSSBOW"))
            return "CROSSBOW";
        if (name.equals("TRIDENT"))
            return "TRIDENT";

        if (name.endsWith("_PICKAXE") || name.endsWith("_AXE") ||
                name.endsWith("_SHOVEL") || name.endsWith("_HOE"))
            return "TOOL";

        return null;
    }

    /**
     * 检查装备是否满耐久
     *
     * @param item 装备物品
     * @return 是否满耐久
     */
    public boolean isFullDurability(ItemStack item) {
        if (item == null)
            return false;

        // 检查物品是否有耐久度
        // 在1.8.8中没有Damageable接口，使用物品的耐久值来判断
        short durability = item.getDurability();

        // 检查物品是否有损坏
        return durability == 0;
    }

    /**
     * 获取强化等级
     *
     * @param item 装备物品
     * @return 强化等级，如果没有强化则返回0
     */
    public int getEnhancementLevel(ItemStack item) {
        if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasDisplayName())
            return 0;

        String displayName = item.getItemMeta().getDisplayName();
        String prefix = ChatColor.translateAlternateColorCodes('&', plugin.getConfig().getString("style.prefix", "强化"));

        // 检查名称是否包含强化前缀
        if (!displayName.contains(prefix))
            return 0;

        // 尝试从名称中提取强化等级
        try {
            // 查找强化等级的位置
            int levelIndex = displayName.indexOf("+");
            if (levelIndex == -1)
                return 0;

            // 提取等级数字
            String levelStr = "";
            for (int i = levelIndex + 1; i < displayName.length(); i++) {
                char c = displayName.charAt(i);
                if (Character.isDigit(c)) {
                    levelStr += c;
                } else {
                    break;
                }
            }

            if (levelStr.isEmpty())
                return 0;

            return Integer.parseInt(levelStr);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 获取强化符号
     *
     * @param level    强化等级
     * @param maxLevel 最大等级
     * @return 强化符号字符串
     */
    public String getEnhancementSymbols(int level, int maxLevel) {
        StringBuilder symbols = new StringBuilder();
        String withLevel = plugin.getConfig().getString("style.with_level", "§c§l◆");
        String withoutLevel = plugin.getConfig().getString("style.without_level", "§7§l◇");

        // 添加已有等级的符号
        for (int i = 0; i < level; i++) {
            symbols.append(withLevel);
        }

        // 添加未有等级的符号
        for (int i = level; i < maxLevel; i++) {
            symbols.append(withoutLevel);
        }

        return symbols.toString();
    }

    /**
     * 更新装备的强化显示
     *
     * @param item  装备物品
     * @param level 强化等级
     * @return 更新后的装备
     */
    public ItemStack updateEnhancementDisplay(ItemStack item, int level) {
        if (item == null)
            return item;

        ItemStack result = item.clone();
        ItemMeta meta = result.getItemMeta();

        if (meta == null)
            return result;

        // 获取配置
        String color = ChatColor.translateAlternateColorCodes('&', plugin.getConfig().getString("style.color", "&c"));
        String prefix = ChatColor.translateAlternateColorCodes('&', plugin.getConfig().getString("style.prefix", "强化"));
        int maxLevel = plugin.getConfig().getInt("style.max_level", 10);

        // 获取原始名称（移除之前的强化信息）
        String originalName = meta.hasDisplayName() ? meta.getDisplayName() : item.getType().name();
        String cleanName = originalName.replaceAll(prefix + " \\+\\d+ ", "").trim();

        // 设置新的显示名称
        if (level > 0) {
            String newName = color + prefix + " +" + level + " " + cleanName;
            meta.setDisplayName(newName);

            // 更新lore
            List<String> lore = meta.hasLore() ? meta.getLore() : new ArrayList<>();

            // 移除之前的强化符号
            if (lore != null && !lore.isEmpty()) {
                lore.removeIf(line -> line.contains("§c§l◆") || line.contains("§7§l◇"));
            } else {
                lore = new ArrayList<>();
            }

            // 添加强化符号
            lore.add(0, getEnhancementSymbols(level, maxLevel));
            meta.setLore(lore);
        }

        result.setItemMeta(meta);
        return result;
    }

    /**
     * 尝试强化装备
     *
     * @param equipment 要强化的装备
     * @return 强化后的装备，如果强化失败则返回原装备
     */
    public ItemStack enhanceEquipment(ItemStack equipment) {
        // 检查装备类型
        String equipmentType = getEquipmentType(equipment);
        if (equipmentType == null)
            return equipment;

        // 检查是否满耐久
        if (!isFullDurability(equipment)) {
            // 不是满耐久，无法强化
            return equipment;
        }

        // 获取该装备类型的附魔配置
        Map<Enchantment, Integer> enchantments = equipmentEnchantments.get(equipmentType);
        if (enchantments == null || enchantments.isEmpty())
            return equipment;

        // 获取当前强化等级
        int currentLevel = getEnhancementLevel(equipment);
        int maxLevel = plugin.getConfig().getInt("style.max_level", 10);

        // 检查是否已达到最大等级
        if (currentLevel >= maxLevel)
            return equipment;

        // 随机决定是否强化成功
        boolean success = random.nextInt(100) < successRate;
        if (!success)
            return equipment;

        // 强化成功，提升附魔等级
        ItemStack enhancedItem = equipment.clone();
        ItemMeta meta = enhancedItem.getItemMeta();

        if (meta != null) {
            // 检查装备是否有附魔
            boolean hasEnchantments = !meta.getEnchants().isEmpty();

            // 如果装备没有附魔，添加配置文件中的附魔（只添加第一级）
            if (!hasEnchantments) {
                for (Map.Entry<Enchantment, Integer> entry : enchantments.entrySet()) {
                    Enchantment enchantment = entry.getKey();
                    // 只添加第一级附魔
                    meta.addEnchant(enchantment, 1, true);
                }
            } else {
                // 如果已有附魔，提升一级
                for (Map.Entry<Enchantment, Integer> entry : meta.getEnchants().entrySet()) {
                    Enchantment enchantment = entry.getKey();
                    int currentEnchantLevel = entry.getValue();

                    // 检查是否在配置的附魔列表中
                    if (enchantments.containsKey(enchantment)) {
                        int newEnchantLevel = currentEnchantLevel + 1;

                        // 确保不超过附魔的最大等级
                        int maxEnchantLevel = enchantment.getMaxLevel();
                        if (newEnchantLevel > maxEnchantLevel)
                            newEnchantLevel = maxEnchantLevel;

                        // 只有当新等级大于当前等级时才更新
                        if (newEnchantLevel > currentEnchantLevel) {
                            meta.addEnchant(enchantment, newEnchantLevel, true);
                        }
                    }
                }
            }

            enhancedItem.setItemMeta(meta);
        }

        // 更新强化显示
        return updateEnhancementDisplay(enhancedItem, currentLevel + 1);
    }

    /**
     * 获取成功几率
     *
     * @return 成功几率
     */
    public int getSuccessRate() {
        return successRate;
    }

    /**
     * 给玩家宝石
     *
     * @param player 玩家
     * @param amount 数量
     */
    public void giveGem(Player player, int amount) {
        ItemStack gems = getGemItem();
        gems.setAmount(amount);

        HashMap<Integer, ItemStack> leftover = player.getInventory().addItem(gems);
        if (!leftover.isEmpty()) {
            for (ItemStack drop : leftover.values()) {
                player.getWorld().dropItemNaturally(player.getLocation(), drop);
            }
        }
    }
}
