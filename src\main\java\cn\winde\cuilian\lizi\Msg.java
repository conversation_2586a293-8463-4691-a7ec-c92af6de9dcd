/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.entity.Player
 *  org.bukkit.inventory.ItemStack
 */
package cn.winde.cuilian.lizi;

import cn.winde.cuilian.Cuilian;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

public class Msg {
    public static String getItemFormattedExplanation(ItemStack item) {
        String s = String.valueOf(item.getItemMeta().getDisplayName()) + "\n";
        for (String line : item.getItemMeta().getLore()) {
            s = String.valueOf(s) + line + "\n";
        }
        String[] parts = s.split("\n");
        String info = "display:{Name:";
        if (parts.length >= 1) {
            info = String.valueOf(info) + "\"" + parts[0] + "\"";
            if (parts.length >= 2) {
                info = String.valueOf(info) + ", Lore:[";
                int i = 1;
                while (i < parts.length) {
                    info = String.valueOf(info) + "\"" + parts[i] + "\", ";
                    ++i;
                }
                info = info.substring(0, info.length() - 2);
                info = String.valueOf(info) + "]";
            }
            info = String.valueOf(info) + "}";
        }
        return "{id:" + item.getTypeId() + ",tag:{" + info + "}}";
    }

    public static void msg(Player p, ItemStack item, int cldj) {
        Bukkit.broadcastMessage((String)("\u00a76\u3016\u00a74\u516c\u544a\u00a76\u3017 \u00a77\u606d\u559c\u73a9\u5bb6 \u00a7b" + p.getDisplayName() + " \u00a77\u6210\u529f\u5c06\u88c5\u5907\u6dec\u70bc\u81f3 \u00a7r" + Cuilian.getCuilianStr(cldj + 1)));
    }
}

