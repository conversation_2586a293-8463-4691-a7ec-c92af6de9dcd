package cn.winde.cuilian.clbh;

import org.bukkit.entity.Player;
import org.bukkit.OfflinePlayer;
import org.bukkit.inventory.ItemStack;
import org.bukkit.Material;
import cn.winde.cuilian.Cuilian;
import cn.winde.cuilian.suit.SuitManager;
import cn.winde.cuilian.texture.TextureManager;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.HashMap;
import java.util.Map;

/**
 * 游戏风格的装备预览对话框
 * 模拟游戏内的装备界面，显示装备图标和详细信息
 */
public class GameStyleEquipmentPreview extends JDialog {
    private final Player player;
    private final OfflinePlayer offlinePlayer;
    private final boolean isOnline;
    private final String playerName;

    // 装备槽位组件
    private JLabel helmetSlot;
    private JLabel chestplateSlot;
    private JLabel leggingsSlot;
    private JLabel bootsSlot;
    private JLabel weaponSlot;

    // 信息显示区域
    private JTextArea detailsArea;
    private JLabel playerNameLabel;
    private JLabel suitLevelLabel;
    private JLabel effectStatusLabel;

    // 装备图标缓存
    private static final Map<Material, ImageIcon> iconCache = new HashMap<>();

    // 装备中文名称映射
    private static final Map<Material, String> chineseNames = new HashMap<>();

    static {
        // 初始化中文名称映射
        initChineseNames();
    }

    /**
     * 初始化装备中文名称映射
     */
    private static void initChineseNames() {
        // 头盔类
        chineseNames.put(Material.LEATHER_HELMET, "皮革头盔");
        chineseNames.put(Material.CHAINMAIL_HELMET, "锁链头盔");
        chineseNames.put(Material.IRON_HELMET, "铁头盔");
        chineseNames.put(Material.DIAMOND_HELMET, "钻石头盔");
        chineseNames.put(Material.GOLD_HELMET, "金头盔");

        // 胸甲类
        chineseNames.put(Material.LEATHER_CHESTPLATE, "皮革胸甲");
        chineseNames.put(Material.CHAINMAIL_CHESTPLATE, "锁链胸甲");
        chineseNames.put(Material.IRON_CHESTPLATE, "铁胸甲");
        chineseNames.put(Material.DIAMOND_CHESTPLATE, "钻石胸甲");
        chineseNames.put(Material.GOLD_CHESTPLATE, "金胸甲");

        // 护腿类
        chineseNames.put(Material.LEATHER_LEGGINGS, "皮革护腿");
        chineseNames.put(Material.CHAINMAIL_LEGGINGS, "锁链护腿");
        chineseNames.put(Material.IRON_LEGGINGS, "铁护腿");
        chineseNames.put(Material.DIAMOND_LEGGINGS, "钻石护腿");
        chineseNames.put(Material.GOLD_LEGGINGS, "金护腿");

        // 靴子类
        chineseNames.put(Material.LEATHER_BOOTS, "皮革靴子");
        chineseNames.put(Material.CHAINMAIL_BOOTS, "锁链靴子");
        chineseNames.put(Material.IRON_BOOTS, "铁靴子");
        chineseNames.put(Material.DIAMOND_BOOTS, "钻石靴子");
        chineseNames.put(Material.GOLD_BOOTS, "金靴子");

        // 武器类
        chineseNames.put(Material.WOOD_SWORD, "木剑");
        chineseNames.put(Material.STONE_SWORD, "石剑");
        chineseNames.put(Material.IRON_SWORD, "铁剑");
        chineseNames.put(Material.DIAMOND_SWORD, "钻石剑");
        chineseNames.put(Material.GOLD_SWORD, "金剑");
        chineseNames.put(Material.BOW, "弓");

        // 工具类（有时也被当作武器）
        chineseNames.put(Material.WOOD_AXE, "木斧");
        chineseNames.put(Material.STONE_AXE, "石斧");
        chineseNames.put(Material.IRON_AXE, "铁斧");
        chineseNames.put(Material.DIAMOND_AXE, "钻石斧");
        chineseNames.put(Material.GOLD_AXE, "金斧");

        chineseNames.put(Material.WOOD_PICKAXE, "木镐");
        chineseNames.put(Material.STONE_PICKAXE, "石镐");
        chineseNames.put(Material.IRON_PICKAXE, "铁镐");
        chineseNames.put(Material.DIAMOND_PICKAXE, "钻石镐");
        chineseNames.put(Material.GOLD_PICKAXE, "金镐");

        chineseNames.put(Material.WOOD_SPADE, "木锹");
        chineseNames.put(Material.STONE_SPADE, "石锹");
        chineseNames.put(Material.IRON_SPADE, "铁锹");
        chineseNames.put(Material.DIAMOND_SPADE, "钻石锹");
        chineseNames.put(Material.GOLD_SPADE, "金锹");

        chineseNames.put(Material.WOOD_HOE, "木锄");
        chineseNames.put(Material.STONE_HOE, "石锄");
        chineseNames.put(Material.IRON_HOE, "铁锄");
        chineseNames.put(Material.DIAMOND_HOE, "钻石锄");
        chineseNames.put(Material.GOLD_HOE, "金锄");

        // 其他常见物品
        chineseNames.put(Material.FISHING_ROD, "钓鱼竿");
        chineseNames.put(Material.FLINT_AND_STEEL, "打火石");
        chineseNames.put(Material.SHEARS, "剪刀");

        // 1.9+ 新增武器（如果服务器版本支持）
        try {
            chineseNames.put(Material.valueOf("SHIELD"), "盾牌");
        } catch (IllegalArgumentException ignored) {}

        try {
            chineseNames.put(Material.valueOf("ELYTRA"), "鞘翅");
        } catch (IllegalArgumentException ignored) {}
    }

    /**
     * 获取物品的中文名称
     */
    private static String getChineseName(Material material) {
        return chineseNames.getOrDefault(material, material.name());
    }

    /**
     * 获取物品的简短中文名称（用于图标显示）
     */
    private static String getShortChineseName(Material material) {
        String chineseName = getChineseName(material);
        // 如果是中文名称，取前2-3个字符
        if (chineseNames.containsKey(material)) {
            if (chineseName.length() > 3) {
                return chineseName.substring(0, 3);
            }
            return chineseName;
        }
        // 如果没有中文名称，使用英文简称
        String name = material.name();
        name = name.replace("DIAMOND_", "").replace("IRON_", "").replace("GOLD_", "")
                   .replace("LEATHER_", "").replace("CHAINMAIL_", "").replace("NETHERITE_", "");
        if (name.length() > 6) {
            return name.substring(0, 6);
        }
        return name;
    }

    // 在线玩家构造函数
    public GameStyleEquipmentPreview(JFrame parent, Player player) {
        super(parent, "游戏风格装备预览 - " + player.getName(), true);
        this.player = player;
        this.offlinePlayer = null;
        this.isOnline = true;
        this.playerName = player.getName();
        initializeComponents();
        loadPlayerEquipment();
        setLocationRelativeTo(parent);
    }

    // 离线玩家构造函数
    public GameStyleEquipmentPreview(JFrame parent, OfflinePlayer offlinePlayer) {
        super(parent, "游戏风格装备预览 - " + offlinePlayer.getName() + " [离线]", true);
        this.player = null;
        this.offlinePlayer = offlinePlayer;
        this.isOnline = false;
        this.playerName = offlinePlayer.getName();
        initializeComponents();
        loadPlayerEquipment();
        setLocationRelativeTo(parent);
    }

    private void initializeComponents() {
        setLayout(new BorderLayout());
        setSize(900, 700);
        setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
        setResizable(false);

        // 创建主面板
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBackground(new Color(45, 45, 45)); // 深色背景

        // 顶部玩家信息面板
        JPanel playerInfoPanel = createPlayerInfoPanel();
        mainPanel.add(playerInfoPanel, BorderLayout.NORTH);

        // 中央装备预览面板
        JPanel equipmentPanel = createEquipmentPanel();
        mainPanel.add(equipmentPanel, BorderLayout.CENTER);

        // 右侧详细信息面板
        JPanel detailsPanel = createDetailsPanel();
        mainPanel.add(detailsPanel, BorderLayout.EAST);

        // 底部按钮面板
        JPanel buttonPanel = createButtonPanel();
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        add(mainPanel);
    }

    private JPanel createPlayerInfoPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        panel.setBackground(new Color(35, 35, 35));
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // 玩家名称
        playerNameLabel = new JLabel("玩家: " + playerName);
        playerNameLabel.setFont(new Font("微软雅黑", Font.BOLD, 16));
        playerNameLabel.setForeground(Color.WHITE);

        // 套装等级
        suitLevelLabel = new JLabel("套装等级: 计算中...");
        suitLevelLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        suitLevelLabel.setForeground(Color.CYAN);

        // 特效状态
        effectStatusLabel = new JLabel("特效状态: 检查中...");
        effectStatusLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        effectStatusLabel.setForeground(Color.YELLOW);

        panel.add(playerNameLabel);
        panel.add(Box.createHorizontalStrut(30));
        panel.add(suitLevelLabel);
        panel.add(Box.createHorizontalStrut(30));
        panel.add(effectStatusLabel);

        return panel;
    }

    private JPanel createEquipmentPanel() {
        JPanel panel = new JPanel();
        panel.setLayout(null); // 使用绝对定位来模拟游戏界面
        panel.setBackground(new Color(50, 50, 50));
        panel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createLineBorder(Color.GRAY, 2),
                "装备预览",
                TitledBorder.CENTER,
                TitledBorder.TOP,
                new Font("微软雅黑", Font.BOLD, 14),
                Color.WHITE));

        // 创建装备槽位 (模拟游戏内的装备界面布局)
        int slotSize = 80;
        int centerX = 250;
        int centerY = 200;

        // 头盔槽位 (上方)
        helmetSlot = createEquipmentSlot("头盔");
        helmetSlot.setBounds(centerX - slotSize/2, centerY - 120, slotSize, slotSize);
        panel.add(helmetSlot);

        // 胸甲槽位 (中央)
        chestplateSlot = createEquipmentSlot("胸甲");
        chestplateSlot.setBounds(centerX - slotSize/2, centerY - 40, slotSize, slotSize);
        panel.add(chestplateSlot);

        // 护腿槽位 (中下)
        leggingsSlot = createEquipmentSlot("护腿");
        leggingsSlot.setBounds(centerX - slotSize/2, centerY + 40, slotSize, slotSize);
        panel.add(leggingsSlot);

        // 靴子槽位 (下方)
        bootsSlot = createEquipmentSlot("靴子");
        bootsSlot.setBounds(centerX - slotSize/2, centerY + 120, slotSize, slotSize);
        panel.add(bootsSlot);

        // 武器槽位 (左侧)
        weaponSlot = createEquipmentSlot("武器");
        weaponSlot.setBounds(centerX - 160, centerY - 40, slotSize, slotSize);
        panel.add(weaponSlot);

        // 添加装备槽位标签
        addSlotLabels(panel, centerX, centerY, slotSize);

        return panel;
    }

    private void addSlotLabels(JPanel panel, int centerX, int centerY, int slotSize) {
        Font labelFont = new Font("微软雅黑", Font.PLAIN, 12);
        Color labelColor = Color.LIGHT_GRAY;

        // 头盔标签
        JLabel helmetLabel = new JLabel("头盔", SwingConstants.CENTER);
        helmetLabel.setFont(labelFont);
        helmetLabel.setForeground(labelColor);
        helmetLabel.setBounds(centerX - slotSize/2, centerY - 140, slotSize, 15);
        panel.add(helmetLabel);

        // 胸甲标签
        JLabel chestLabel = new JLabel("胸甲", SwingConstants.CENTER);
        chestLabel.setFont(labelFont);
        chestLabel.setForeground(labelColor);
        chestLabel.setBounds(centerX + slotSize/2 + 10, centerY - 25, 40, 15);
        panel.add(chestLabel);

        // 护腿标签
        JLabel leggingsLabel = new JLabel("护腿", SwingConstants.CENTER);
        leggingsLabel.setFont(labelFont);
        leggingsLabel.setForeground(labelColor);
        leggingsLabel.setBounds(centerX + slotSize/2 + 10, centerY + 55, 40, 15);
        panel.add(leggingsLabel);

        // 靴子标签
        JLabel bootsLabel = new JLabel("靴子", SwingConstants.CENTER);
        bootsLabel.setFont(labelFont);
        bootsLabel.setForeground(labelColor);
        bootsLabel.setBounds(centerX - slotSize/2, centerY + 205, slotSize, 15);
        panel.add(bootsLabel);

        // 武器标签
        JLabel weaponLabel = new JLabel("武器", SwingConstants.CENTER);
        weaponLabel.setFont(labelFont);
        weaponLabel.setForeground(labelColor);
        weaponLabel.setBounds(centerX - 160, centerY - 60, slotSize, 15);
        panel.add(weaponLabel);
    }

    private JLabel createEquipmentSlot(String slotName) {
        JLabel slot = new JLabel();
        slot.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(Color.GRAY, 2),
                BorderFactory.createEmptyBorder(5, 5, 5, 5)));
        slot.setBackground(new Color(60, 60, 60));
        slot.setOpaque(true);
        slot.setHorizontalAlignment(SwingConstants.CENTER);
        slot.setVerticalAlignment(SwingConstants.CENTER);

        // 设置空槽位的默认显示
        slot.setText("空");
        slot.setForeground(Color.GRAY);
        slot.setFont(new Font("微软雅黑", Font.PLAIN, 12));

        // 添加鼠标悬停效果
        slot.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent e) {
                slot.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createLineBorder(Color.YELLOW, 2),
                        BorderFactory.createEmptyBorder(5, 5, 5, 5)));
            }

            @Override
            public void mouseExited(java.awt.event.MouseEvent e) {
                slot.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createLineBorder(Color.GRAY, 2),
                        BorderFactory.createEmptyBorder(5, 5, 5, 5)));
            }
        });

        return slot;
    }

    private JPanel createDetailsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setPreferredSize(new Dimension(300, 0));
        panel.setBackground(new Color(40, 40, 40));
        panel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createLineBorder(Color.GRAY, 1),
                "详细信息",
                TitledBorder.LEFT,
                TitledBorder.TOP,
                new Font("微软雅黑", Font.BOLD, 12),
                Color.WHITE));

        detailsArea = new JTextArea();
        detailsArea.setEditable(false);
        detailsArea.setFont(new Font("微软雅黑", Font.PLAIN, 11));
        detailsArea.setBackground(new Color(35, 35, 35));
        detailsArea.setForeground(Color.WHITE);
        detailsArea.setMargin(new Insets(10, 10, 10, 10));

        JScrollPane scrollPane = new JScrollPane(detailsArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        scrollPane.getVerticalScrollBar().setBackground(new Color(50, 50, 50));
        scrollPane.getHorizontalScrollBar().setBackground(new Color(50, 50, 50));

        panel.add(scrollPane, BorderLayout.CENTER);
        return panel;
    }

    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout());
        panel.setBackground(new Color(35, 35, 35));

        JButton refreshButton = new JButton("刷新");
        refreshButton.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        refreshButton.addActionListener(e -> loadPlayerEquipment());

        JButton closeButton = new JButton("关闭");
        closeButton.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        closeButton.addActionListener(e -> dispose());

        panel.add(refreshButton);
        panel.add(closeButton);

        return panel;
    }

    /**
     * 加载玩家装备信息
     */
    private void loadPlayerEquipment() {
        if (isOnline && (player == null || !player.isOnline())) {
            JOptionPane.showMessageDialog(this, "玩家已离线！", "错误", JOptionPane.ERROR_MESSAGE);
            dispose();
            return;
        }

        if (isOnline && player != null) {
            // 在线玩家 - 获取实时装备
            loadEquipmentSlot(helmetSlot, player.getInventory().getHelmet(), "头盔");
            loadEquipmentSlot(chestplateSlot, player.getInventory().getChestplate(), "胸甲");
            loadEquipmentSlot(leggingsSlot, player.getInventory().getLeggings(), "护腿");
            loadEquipmentSlot(bootsSlot, player.getInventory().getBoots(), "靴子");
            loadEquipmentSlot(weaponSlot, player.getInventory().getItemInHand(), "武器");

            // 更新状态信息
            updatePlayerStatus();
            updateDetailsArea();
        } else {
            // 离线玩家 - 显示离线状态
            loadOfflineEquipment();
        }
    }

    /**
     * 加载装备槽位
     */
    private void loadEquipmentSlot(JLabel slot, ItemStack item, String slotName) {
        if (item == null || item.getType() == Material.AIR) {
            // 空槽位
            slot.setIcon(null);
            slot.setText("空");
            slot.setForeground(Color.GRAY);
            slot.setToolTipText(slotName + ": 无装备");
        } else {
            // 有装备 - 检查是否附魔
            boolean isEnchanted = item.hasItemMeta() && item.getItemMeta().hasEnchants();
            short data = item.getDurability(); // 在1.8中，getDurability()返回数据值
            ImageIcon icon = getItemIcon(item.getType(), data, isEnchanted);

            if (icon != null) {
                slot.setIcon(icon);
                slot.setText("");
            } else {
                slot.setIcon(null);
                slot.setText(getShortChineseName(item.getType()));
                slot.setForeground(Color.WHITE);
            }

            // 设置工具提示
            String tooltip = createItemTooltip(item, slotName);
            slot.setToolTipText(tooltip);

            // 根据淬炼等级和附魔状态设置边框颜色
            int level = Cuilian.getCuilianlevel(item);
            Color borderColor = getLevelColor(level);

            if (isEnchanted) {
                // 附魔装备使用特殊的紫色光效边框
                Color enchantedBorder = new Color(138, 43, 226); // 蓝紫色
                slot.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createLineBorder(enchantedBorder, 3),
                        BorderFactory.createLineBorder(borderColor, 1)));
            } else {
                slot.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createLineBorder(borderColor, 3),
                        BorderFactory.createEmptyBorder(2, 2, 2, 2)));
            }
        }
    }

    /**
     * 获取物品图标
     */
    private ImageIcon getItemIcon(Material material) {
        return getItemIcon(material, false);
    }

    /**
     * 获取物品图标（支持附魔）
     */
    private ImageIcon getItemIcon(Material material, boolean isEnchanted) {
        return getItemIcon(material, (short) 0, isEnchanted);
    }

    /**
     * 获取物品图标（支持数据值和附魔）
     */
    private ImageIcon getItemIcon(Material material, short data, boolean isEnchanted) {
        String cacheKey = material.name() + "_" + data + (isEnchanted ? "_ENCHANTED" : "");
        if (iconCache.containsKey(material) && !isEnchanted && data == 0) {
            return iconCache.get(material);
        }

        try {
            // 首先尝试从贴图管理器获取真实贴图
            ImageIcon realIcon;
            if (isEnchanted) {
                realIcon = TextureManager.getEnchantedTexture(material, true);
            } else if (data != 0) {
                realIcon = TextureManager.getTexture(material, data);
            } else {
                realIcon = TextureManager.getTexture(material);
            }

            if (realIcon != null) {
                if (!isEnchanted && data == 0) {
                    iconCache.put(material, realIcon);
                }
                return realIcon;
            }

            // 如果没有找到真实贴图，创建简单的物品图标 (使用颜色块代替)
            BufferedImage image = new BufferedImage(64, 64, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g2d = image.createGraphics();
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 根据物品类型设置颜色
            Color itemColor = getItemColor(material);
            g2d.setColor(itemColor);
            g2d.fillRoundRect(4, 4, 56, 56, 8, 8);

            // 添加边框
            g2d.setColor(Color.DARK_GRAY);
            g2d.setStroke(new BasicStroke(2));
            g2d.drawRoundRect(4, 4, 56, 56, 8, 8);

            // 添加物品类型标识
            g2d.setColor(Color.WHITE);
            g2d.setFont(new Font("微软雅黑", Font.BOLD, 10));
            String shortName = getShortChineseName(material);
            FontMetrics fm = g2d.getFontMetrics();
            int textWidth = fm.stringWidth(shortName);
            int textHeight = fm.getHeight();
            g2d.drawString(shortName, (64 - textWidth) / 2, (64 + textHeight) / 2 - 2);

            g2d.dispose();

            ImageIcon icon = new ImageIcon(image);
            iconCache.put(material, icon);
            return icon;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据物品类型获取颜色
     */
    private Color getItemColor(Material material) {
        String name = material.name().toLowerCase();

        // 头盔类
        if (name.contains("helmet")) {
            if (name.contains("diamond")) return new Color(0, 255, 255);
            if (name.contains("iron")) return new Color(192, 192, 192);
            if (name.contains("gold")) return new Color(255, 215, 0);
            if (name.contains("leather")) return new Color(139, 69, 19);
            return new Color(128, 128, 128);
        }

        // 胸甲类
        if (name.contains("chestplate")) {
            if (name.contains("diamond")) return new Color(0, 255, 255);
            if (name.contains("iron")) return new Color(192, 192, 192);
            if (name.contains("gold")) return new Color(255, 215, 0);
            if (name.contains("leather")) return new Color(139, 69, 19);
            return new Color(128, 128, 128);
        }

        // 护腿类
        if (name.contains("leggings")) {
            if (name.contains("diamond")) return new Color(0, 255, 255);
            if (name.contains("iron")) return new Color(192, 192, 192);
            if (name.contains("gold")) return new Color(255, 215, 0);
            if (name.contains("leather")) return new Color(139, 69, 19);
            return new Color(128, 128, 128);
        }

        // 靴子类
        if (name.contains("boots")) {
            if (name.contains("diamond")) return new Color(0, 255, 255);
            if (name.contains("iron")) return new Color(192, 192, 192);
            if (name.contains("gold")) return new Color(255, 215, 0);
            if (name.contains("leather")) return new Color(139, 69, 19);
            return new Color(128, 128, 128);
        }

        // 武器类
        if (name.contains("sword")) {
            if (name.contains("diamond")) return new Color(0, 255, 255);
            if (name.contains("iron")) return new Color(192, 192, 192);
            if (name.contains("gold")) return new Color(255, 215, 0);
            if (name.contains("wood")) return new Color(139, 69, 19);
            return new Color(128, 128, 128);
        }

        if (name.contains("bow")) return new Color(139, 69, 19);
        if (name.contains("crossbow")) return new Color(105, 105, 105);
        if (name.contains("trident")) return new Color(0, 191, 255);

        // 默认颜色
        return new Color(128, 128, 128);
    }

    /**
     * 获取物品简短名称
     */
    private String getShortItemName(Material material) {
        String name = material.name();

        // 移除常见前缀和后缀
        name = name.replace("DIAMOND_", "").replace("IRON_", "").replace("GOLD_", "")
                   .replace("LEATHER_", "").replace("CHAINMAIL_", "").replace("NETHERITE_", "");

        // 获取前几个字符
        if (name.length() > 6) {
            return name.substring(0, 6);
        }
        return name;
    }

    /**
     * 根据淬炼等级获取边框颜色
     */
    private Color getLevelColor(int level) {
        if (level == 0) return Color.GRAY;
        if (level <= 3) return Color.GREEN;
        if (level <= 6) return Color.BLUE;
        if (level <= 9) return new Color(128, 0, 128); // 紫色
        if (level <= 12) return Color.ORANGE;
        if (level <= 15) return Color.RED;
        if (level <= 18) return Color.MAGENTA;
        return Color.YELLOW; // 19星及以上
    }

    /**
     * 创建物品工具提示
     */
    private String createItemTooltip(ItemStack item, String slotName) {
        if (item == null || item.getType() == Material.AIR) {
            return slotName + ": 无装备";
        }

        StringBuilder tooltip = new StringBuilder();
        tooltip.append("<html>");
        tooltip.append("<b>").append(slotName).append("</b><br>");

        // 物品名称
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            tooltip.append("名称: ").append(item.getItemMeta().getDisplayName()).append("<br>");
        }

        // 物品类型
        tooltip.append("类型: ").append(getChineseName(item.getType())).append("<br>");

        // 淬炼等级
        int level = Cuilian.getCuilianlevel(item);
        if (level > 0) {
            tooltip.append("淬炼等级: <font color='gold'>").append(level).append("星</font><br>");
        } else {
            tooltip.append("淬炼等级: 无<br>");
        }

        // 附魔信息
        if (item.hasItemMeta() && item.getItemMeta().hasEnchants()) {
            tooltip.append("<font color='#8A2BE2'>附魔效果:</font><br>");
            item.getItemMeta().getEnchants().forEach((enchant, enchantLevel) -> {
                String enchantName = getEnchantmentName(enchant);
                tooltip.append("  <font color='#DDA0DD'>").append(enchantName)
                       .append(" ").append(enchantLevel).append("</font><br>");
            });
        }

        // 数量
        tooltip.append("数量: ").append(item.getAmount()).append("<br>");

        // 耐久度
        if (item.getType().getMaxDurability() > 0) {
            short durability = item.getDurability();
            short maxDurability = item.getType().getMaxDurability();
            int remaining = maxDurability - durability;
            tooltip.append("耐久: ").append(remaining).append("/").append(maxDurability);
        }

        tooltip.append("</html>");
        return tooltip.toString();
    }

    /**
     * 获取附魔的中文名称
     */
    private String getEnchantmentName(org.bukkit.enchantments.Enchantment enchant) {
        switch (enchant.getName()) {
            case "PROTECTION_ENVIRONMENTAL": return "保护";
            case "PROTECTION_FIRE": return "火焰保护";
            case "PROTECTION_FALL": return "摔落保护";
            case "PROTECTION_EXPLOSIONS": return "爆炸保护";
            case "PROTECTION_PROJECTILE": return "弹射物保护";
            case "OXYGEN": return "水下呼吸";
            case "WATER_WORKER": return "水下速掘";
            case "THORNS": return "荆棘";
            case "DEPTH_STRIDER": return "深海探索者";
            case "DAMAGE_ALL": return "锋利";
            case "DAMAGE_UNDEAD": return "亡灵杀手";
            case "DAMAGE_ARTHROPODS": return "节肢杀手";
            case "KNOCKBACK": return "击退";
            case "FIRE_ASPECT": return "火焰附加";
            case "LOOT_BONUS_MOBS": return "抢夺";
            case "DIG_SPEED": return "效率";
            case "SILK_TOUCH": return "精准采集";
            case "DURABILITY": return "耐久";
            case "LOOT_BONUS_BLOCKS": return "时运";
            case "ARROW_DAMAGE": return "力量";
            case "ARROW_KNOCKBACK": return "冲击";
            case "ARROW_FIRE": return "火矢";
            case "ARROW_INFINITE": return "无限";
            case "LUCK": return "海之眷顾";
            case "LURE": return "饵钓";
            default: return enchant.getName();
        }
    }

    /**
     * 更新玩家状态信息
     */
    private void updatePlayerStatus() {
        if (!isOnline || player == null) {
            suitLevelLabel.setText("套装等级: 离线");
            effectStatusLabel.setText("特效状态: 离线");
            return;
        }

        // 计算套装等级
        int equipmentLevel = Cuilian.checkPlayerZBCL(player);
        int weaponLevel = Cuilian.checkPlayerWQCL(player);
        int suitLevel = Cuilian.getCLTZ(equipmentLevel, weaponLevel);

        suitLevelLabel.setText("套装等级: " + suitLevel + "星 (装备:" + equipmentLevel + " 武器:" + weaponLevel + ")");

        // 检查特效状态
        Integer currentEffect = Cuilian.lizi.get(playerName);
        if (currentEffect != null && currentEffect != 0) {
            if (currentEffect == -1) {
                String suitName = SuitManager.getPlayerSuit(playerName);
                effectStatusLabel.setText("特效状态: 激活中 (命名套装: " + (suitName != null ? suitName : "未知") + ")");
                effectStatusLabel.setForeground(Color.GREEN);
            } else if (currentEffect > 0) {
                effectStatusLabel.setText("特效状态: 激活中 (" + currentEffect + "星特效)");
                effectStatusLabel.setForeground(Color.GREEN);
            }
        } else {
            effectStatusLabel.setText("特效状态: 未激活");
            effectStatusLabel.setForeground(Color.RED);
        }
    }

    /**
     * 更新详细信息区域
     */
    private void updateDetailsArea() {
        if (!isOnline || player == null) {
            detailsArea.setText("玩家离线，无法获取详细信息。");
            return;
        }

        StringBuilder sb = new StringBuilder();
        sb.append("=== 装备详细信息 ===\n\n");

        // 装备信息
        sb.append("头盔: ").append(getItemDetailInfo(player.getInventory().getHelmet())).append("\n\n");
        sb.append("胸甲: ").append(getItemDetailInfo(player.getInventory().getChestplate())).append("\n\n");
        sb.append("护腿: ").append(getItemDetailInfo(player.getInventory().getLeggings())).append("\n\n");
        sb.append("靴子: ").append(getItemDetailInfo(player.getInventory().getBoots())).append("\n\n");
        sb.append("武器: ").append(getItemDetailInfo(player.getInventory().getItemInHand())).append("\n\n");

        // 套装统计
        sb.append("=== 套装统计 ===\n");
        int equipmentLevel = Cuilian.checkPlayerZBCL(player);
        int weaponLevel = Cuilian.checkPlayerWQCL(player);
        int suitLevel = Cuilian.getCLTZ(equipmentLevel, weaponLevel);

        sb.append("装备等级: ").append(equipmentLevel).append("星\n");
        sb.append("武器等级: ").append(weaponLevel).append("星\n");
        sb.append("套装等级: ").append(suitLevel).append("星\n");
        sb.append("激活条件: ").append(suitLevel >= 6 ? "已满足" : "未满足").append(" (需要6星)\n\n");

        // 特效开关状态
        boolean globalEffects = Cuilian.config.getBoolean("effects", true);
        boolean playerEffects = Cuilian.config.getBoolean("playereffects." + playerName, true);

        sb.append("=== 特效开关状态 ===\n");
        sb.append("全局特效: ").append(globalEffects ? "开启" : "关闭").append("\n");
        sb.append("玩家特效: ").append(playerEffects ? "开启" : "关闭").append("\n\n");

        // 当前激活的特效
        Integer currentLevel = Cuilian.lizi.get(playerName);
        sb.append("=== 当前特效 ===\n");
        if (isOnline && currentLevel != null && currentLevel != 0) {
            if (currentLevel == -1) {
                // 命名套装特效
                String suitName = SuitManager.getPlayerSuit(playerName);
                if (suitName != null) {
                    sb.append("激活特效类型: 命名套装 (").append(suitName).append(")\n");
                } else {
                    sb.append("激活特效类型: 命名套装\n");
                }
                sb.append("特效状态: 激活中\n");

                // 显示命名套装的特效信息
                sb.append("\n=== 特效详情 ===\n");
                loadNamedSuitEffectDetails(sb, suitName);
            } else if (currentLevel > 0) {
                // 淬炼套装特效
                sb.append("激活特效等级: ").append(currentLevel).append("星\n");
                sb.append("特效状态: 激活中\n");

                // 显示具体的特效信息
                sb.append("\n=== 特效详情 ===\n");
                loadEffectDetails(sb, currentLevel);
            }
        } else {
            if (!isOnline) {
                sb.append("特效状态: 离线 (无法获取实时特效状态)\n");
                sb.append("说明: 特效只在玩家在线时激活\n\n");
                sb.append("=== 特效配置预览 ===\n");
                sb.append("以下是6星特效的配置信息:\n");
                loadEffectDetails(sb, 6);
            } else {
                sb.append("特效状态: 未激活\n");
                sb.append("原因: ");

                // 分析未激活的原因
                if (!globalEffects) {
                    sb.append("全局特效已关闭");
                } else if (!playerEffects) {
                    sb.append("玩家特效已关闭");
                } else {
                    if (suitLevel < 6) {
                        sb.append("套装等级不足6星 (当前: ").append(suitLevel).append("星)");
                    } else {
                        sb.append("未知原因");
                    }
                }
                sb.append("\n\n");

                // 显示可用特效预览
                sb.append("=== 可用特效预览 ===\n");
                if (suitLevel >= 6) {
                    sb.append("以下是").append(suitLevel).append("星特效的配置信息:\n");
                    loadEffectDetails(sb, suitLevel);
                } else {
                    sb.append("达到6星后可查看特效配置\n");
                }
            }
        }

        detailsArea.setText(sb.toString());
        detailsArea.setCaretPosition(0);
    }

    /**
     * 获取物品详细信息
     */
    private String getItemDetailInfo(ItemStack item) {
        if (item == null || item.getType() == Material.AIR) {
            return "无";
        }

        StringBuilder info = new StringBuilder();
        info.append(getChineseName(item.getType()));

        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            info.append(" (").append(item.getItemMeta().getDisplayName()).append(")");
        }

        int level = Cuilian.getCuilianlevel(item);
        if (level > 0) {
            info.append(" [").append(level).append("星]");
        }

        return info.toString();
    }

    /**
     * 加载离线装备信息
     */
    private void loadOfflineEquipment() {
        // 清空所有装备槽位
        loadEquipmentSlot(helmetSlot, null, "头盔");
        loadEquipmentSlot(chestplateSlot, null, "胸甲");
        loadEquipmentSlot(leggingsSlot, null, "护腿");
        loadEquipmentSlot(bootsSlot, null, "靴子");
        loadEquipmentSlot(weaponSlot, null, "武器");

        // 更新状态
        suitLevelLabel.setText("套装等级: 离线");
        effectStatusLabel.setText("特效状态: 离线");

        // 更新详细信息
        StringBuilder sb = new StringBuilder();
        sb.append("=== 离线玩家信息 ===\n\n");
        sb.append("玩家名称: ").append(playerName).append("\n");
        sb.append("状态: 离线\n\n");

        if (offlinePlayer != null && offlinePlayer.getLastPlayed() > 0) {
            java.util.Date lastPlayed = new java.util.Date(offlinePlayer.getLastPlayed());
            sb.append("最后在线时间: ").append(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(lastPlayed)).append("\n\n");
        }

        sb.append("提示: 装备信息需要玩家在线时才能查看。\n");
        sb.append("请等待玩家上线后再次查看。");

        detailsArea.setText(sb.toString());
        detailsArea.setCaretPosition(0);
    }

    /**
     * 加载淬炼等级特效详细信息
     */
    private void loadEffectDetails(StringBuilder sb, int level) {
        try {
            // 获取特效配置
            String configPath = "eff.leve" + level + ".effects";
            if (Cuilian.config.contains(configPath)) {
                java.util.List<?> effectsList = Cuilian.config.getList(configPath);
                if (effectsList != null && !effectsList.isEmpty()) {
                    sb.append("特效列表:\n");
                    for (int i = 0; i < effectsList.size(); i++) {
                        if (effectsList.get(i) instanceof java.util.Map) {
                            @SuppressWarnings("unchecked")
                            java.util.Map<String, Object> effectMap = (java.util.Map<String, Object>) effectsList.get(i);

                            String type = effectMap.getOrDefault("type", "").toString();
                            String color1 = effectMap.getOrDefault("color1", "").toString();
                            String color2 = effectMap.getOrDefault("color2", "").toString();
                            String color3 = effectMap.getOrDefault("color3", "").toString();
                            boolean enabled = Boolean.parseBoolean(effectMap.getOrDefault("enabled", "true").toString());

                            if (!type.isEmpty()) {
                                String displayName = SuitManager.getEffectDisplayName(type);
                                String statusColor = enabled ? "启用" : "禁用";
                                sb.append("  ").append(i + 1).append(". ").append(displayName)
                                  .append(" [").append(statusColor).append("]\n");

                                if (!color1.isEmpty() || !color2.isEmpty() || !color3.isEmpty()) {
                                    sb.append("     颜色配置: ").append(color1.isEmpty() ? "默认" : color1)
                                      .append(", ").append(color2.isEmpty() ? "默认" : color2)
                                      .append(", ").append(color3.isEmpty() ? "默认" : color3).append("\n");
                                }
                            }
                        }
                    }
                } else {
                    sb.append("该等级暂无配置的特效\n");
                }
            } else {
                // 检查旧格式配置
                String oldConfigPath = "eff.leve" + level;
                if (Cuilian.config.contains(oldConfigPath + ".type")) {
                    String type = Cuilian.config.getString(oldConfigPath + ".type");
                    String color1 = Cuilian.config.getString(oldConfigPath + ".color1", "");
                    String color2 = Cuilian.config.getString(oldConfigPath + ".color2", "");
                    String color3 = Cuilian.config.getString(oldConfigPath + ".color3", "");

                    if (!type.isEmpty()) {
                        String displayName = SuitManager.getEffectDisplayName(type);
                        sb.append("特效类型: ").append(displayName).append(" [启用]\n");

                        if (!color1.isEmpty() || !color2.isEmpty() || !color3.isEmpty()) {
                            sb.append("颜色配置: ").append(color1.isEmpty() ? "默认" : color1)
                              .append(", ").append(color2.isEmpty() ? "默认" : color2)
                              .append(", ").append(color3.isEmpty() ? "默认" : color3).append("\n");
                        }
                    }
                } else {
                    sb.append("该等级暂无配置的特效\n");
                }
            }
        } catch (Exception e) {
            sb.append("获取特效信息时出错: ").append(e.getMessage()).append("\n");
        }
    }

    /**
     * 加载命名套装特效详细信息
     */
    private void loadNamedSuitEffectDetails(StringBuilder sb, String suitName) {
        try {
            if (suitName == null) {
                sb.append("命名套装信息不可用\n");
                return;
            }

            // 获取命名套装的特效配置
            String basePath = "suit." + suitName + ".effect";
            if (!Cuilian.Suit.contains(basePath)) {
                sb.append("该套装暂无配置的特效\n");
                return;
            }

            // 检查新格式的effects数组
            if (Cuilian.Suit.contains(basePath + ".effects")) {
                java.util.List<?> effectsList = Cuilian.Suit.getList(basePath + ".effects");
                boolean enableStacking = Cuilian.Suit.getBoolean(basePath + ".enable_stacking", false);

                if (effectsList != null && !effectsList.isEmpty()) {
                    if (enableStacking && effectsList.size() > 1) {
                        sb.append("叠加特效 (共 ").append(effectsList.size()).append(" 种):\n");
                    } else {
                        sb.append("特效列表:\n");
                    }

                    for (int i = 0; i < effectsList.size(); i++) {
                        if (effectsList.get(i) instanceof java.util.Map) {
                            @SuppressWarnings("unchecked")
                            java.util.Map<String, Object> effectMap = (java.util.Map<String, Object>) effectsList.get(i);

                            String type = effectMap.getOrDefault("type", "").toString();
                            String color1 = effectMap.getOrDefault("colore1", "").toString();
                            String color2 = effectMap.getOrDefault("colore2", "").toString();
                            String color3 = effectMap.getOrDefault("colore3", "").toString();
                            boolean enabled = Boolean.parseBoolean(effectMap.getOrDefault("enabled", "true").toString());

                            if (!type.isEmpty()) {
                                String displayName = SuitManager.getEffectDisplayName(type);
                                String statusColor = enabled ? "启用" : "禁用";
                                sb.append("  ").append(i + 1).append(". ").append(displayName)
                                  .append(" [").append(statusColor).append("]\n");

                                if (!color1.isEmpty() || !color2.isEmpty() || !color3.isEmpty()) {
                                    sb.append("     颜色配置: ").append(color1.isEmpty() ? "默认" : color1)
                                      .append(", ").append(color2.isEmpty() ? "默认" : color2)
                                      .append(", ").append(color3.isEmpty() ? "默认" : color3).append("\n");
                                }
                            }
                        }
                    }
                } else {
                    sb.append("该套装暂无配置的特效\n");
                }
            } else {
                // 检查旧格式的特效配置
                if (Cuilian.Suit.contains(basePath + ".type")) {
                    String type = Cuilian.Suit.getString(basePath + ".type");
                    String color1 = Cuilian.Suit.getString(basePath + ".color1", "");
                    String color2 = Cuilian.Suit.getString(basePath + ".color2", "");
                    String color3 = Cuilian.Suit.getString(basePath + ".color3", "");

                    if (!type.isEmpty()) {
                        String displayName = SuitManager.getEffectDisplayName(type);
                        sb.append("特效类型: ").append(displayName).append(" [启用]\n");

                        if (!color1.isEmpty() || !color2.isEmpty() || !color3.isEmpty()) {
                            sb.append("颜色配置: ").append(color1.isEmpty() ? "默认" : color1)
                              .append(", ").append(color2.isEmpty() ? "默认" : color2)
                              .append(", ").append(color3.isEmpty() ? "默认" : color3).append("\n");
                        }
                    }
                } else {
                    sb.append("该套装暂无配置的特效\n");
                }
            }
        } catch (Exception e) {
            sb.append("获取套装特效信息时出错: ").append(e.getMessage()).append("\n");
        }
    }


}
