/*
 * Decompiled with CFR 0.152.
 *
 * Could not load the following classes:
 *  org.bukkit.Material
 *  org.bukkit.Sound
 *  org.bukkit.block.Furnace
 *  org.bukkit.command.Command
 *  org.bukkit.command.CommandSender
 *  org.bukkit.configuration.file.FileConfiguration
 *  org.bukkit.enchantments.Enchantment
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.EventPriority
 *  org.bukkit.event.Listener
 *  org.bukkit.event.block.Action
 *  org.bukkit.event.inventory.FurnaceBurnEvent
 *  org.bukkit.event.inventory.FurnaceSmeltEvent
 *  org.bukkit.event.player.PlayerInteractEvent
 *  org.bukkit.inventory.FurnaceRecipe
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.Recipe
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 *  org.bukkit.plugin.java.JavaPlugin
 */
package Intensify;

import Intensify.BlockDrop;
import Intensify.Enchan;
import Intensify.MobDrop;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.Sound;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.block.Furnace;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.inventory.FurnaceBurnEvent;
import org.bukkit.event.inventory.FurnaceSmeltEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.FurnaceInventory;
import org.bukkit.inventory.FurnaceRecipe;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.Recipe;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.java.JavaPlugin;

public class Intensify
    extends JavaPlugin
    implements Listener, TabCompleter {
  public FileConfiguration config;
  public Random rm = new Random();
  private String cg;
  private String sb;
  private int normalchance;
  private int luckchance;
  private int safechance;
  private int vipchance;
  private int directUpgradeChance; // 直升符咒成功几率
  public ItemStack normalItem;
  public ItemStack luckItem;
  public ItemStack safeItem;
  public ItemStack vipItem;
  private ItemStack adminItem;
  private static List<String> BlockId = new ArrayList<String>();
  private static List<String> MobId = new ArrayList<String>();
  public HashMap<Integer, Integer> random = new HashMap<>();
  public HashMap<Integer, ItemStack> fuelItem = new HashMap<>();
  public HashMap<Integer, Player> player = new HashMap<>();
  // 添加消息冷却机制，防止刷屏
  private HashMap<String, Long> messageCooldowns = new HashMap<String, Long>();
  // 添加战力查询冷却机制
  private HashMap<String, Long> powerCooldowns = new HashMap<String, Long>();
  // 添加战力排行榜相关字段
  private HashMap<String, PowerData> playerPowers = new HashMap<String, PowerData>();
  private List<PowerData> powerRanking = new ArrayList<>();
  private long lastRankingUpdate = 0;
  private long rankingUpdateInterval = 300000; // 默认5分钟更新一次排行榜
  private PlaceholderAPI placeholderAPI;
  public Enchan enchan;

  // 装备最高强化等级限制
  private Map<Integer, Integer> maxEnhancementLevels = new HashMap<Integer, Integer>();

  public void onEnable() {
    this.getServer().getPluginManager().registerEvents((Listener) this, (Plugin) this);
    this.getServer().getPluginManager().registerEvents((Listener) new BlockDrop(this), (Plugin) this);
    this.getServer().getPluginManager().registerEvents((Listener) new MobDrop(this), (Plugin) this);

    // 注册Tab补全器
    getCommand("qh").setTabCompleter(this);
    getCommand("zl").setTabCompleter(this);

    // 从配置文件加载排行榜更新间隔
    int updateIntervalMinutes = this.config.getInt("power.ranking_update_interval", 5);
    this.rankingUpdateInterval = updateIntervalMinutes * 60 * 1000; // 转换为毫秒

    // 加载战力排行榜数据
    loadPowerRankingData();

    // 初始化PlaceholderAPI
    this.placeholderAPI = new PlaceholderAPI(this);
    this.placeholderAPI.register();

    for (String i : this.config.getConfigurationSection("id.items").getKeys(false)) {
      FurnaceRecipe recipe = new FurnaceRecipe(new ItemStack(Material.getMaterial((int) Integer.parseInt(i))),
          Material.EMERALD);
      recipe.setInput(Material.getMaterial((int) Integer.parseInt(i)));
      this.getServer().addRecipe((Recipe) recipe);
    }
    this.enchan = new Enchan();
  }

  /**
   * Tab补全功能
   */
  @Override
  public List<String> onTabComplete(CommandSender sender, Command cmd, String alias, String[] args) {
    List<String> completions = new ArrayList<>();

    // 处理战力系统命令补全
    if (cmd.getName().equalsIgnoreCase("zl")) {
      if (args.length == 1) {
        // 第一个参数的补全
        completions.add("power");
        completions.add("top");
        // 只有有权限的玩家才能看到cx命令补全
        if (sender.hasPermission("intensify.cx")) {
          completions.add("cx");
        }
        // 只有管理员才能看到update命令补全
        if (sender.hasPermission(this.config.getString("Admin"))) {
          completions.add("update");
        }
      } else if (args.length == 2 && args[0].equalsIgnoreCase("cx")) {
        // 第二个参数的补全 - 玩家名称
        for (Player player : Bukkit.getOnlinePlayers()) {
          completions.add(player.getName());
        }
      }
      return completions;
    }

    // 处理强化系统命令补全
    if (cmd.getName().equalsIgnoreCase("qh")) {
      if (args.length == 1) {
        // 第一个参数的补全
        if (sender.hasPermission(this.config.getString("Admin"))) {
          completions.add("get");
          completions.add("reload");
          completions.add("set"); // 添加"set"功能
          completions.add("直升"); // 添加"直升"功能
          completions.add("突破"); // 添加"突破"功能
          completions.add("强化棒"); // 添加"强化棒"功能
        }
      } else if (args.length == 2 && args[0].equalsIgnoreCase("get")) {
        // 第二个参数的补全（get命令后）- 按照指定顺序排列
        // 清空默认补全列表，确保按照我们指定的顺序显示
        completions.clear();

        // 按照指定顺序添加物品类型
        completions.add("normal");
        completions.add("luck");
        completions.add("safe");
        completions.add("vip");
        completions.add("admin");
        completions.add("直升");
        completions.add("突破");
        completions.add("强化棒");
      } else if (args.length == 2 && args[0].equalsIgnoreCase("set")) {
        // 设置强化等级的补全 - 提供常用等级选择
        List<Integer> levels = new ArrayList<>();

        // 添加常用等级
        levels.add(1);
        levels.add(5);
        levels.add(10);
        levels.add(20);
        levels.add(29);
        levels.add(30);
        levels.add(49);
        levels.add(50);
        levels.add(69);
        levels.add(70);
        levels.add(99);
        levels.add(100);

        // 排序确保从小到大
        Collections.sort(levels);

        // 添加到补全列表
        for (int level : levels) {
          completions.add(String.valueOf(level));
        }
      } else if (args.length == 3 && args[0].equalsIgnoreCase("get")) {
        // 第三个参数的补全（数量）
        completions.add("1");
        completions.add("10");
        completions.add("64");
      } else if (args.length == 2 && args[0].equalsIgnoreCase("直升")) {
        // 直升后面的数字补全 - 提供特定等级选择，按从小到大排序
        List<Integer> levels = new ArrayList<>();

        // 添加特定高级等级选项
        levels.add(10);
        levels.add(20);
        levels.add(29);
        levels.add(49);
        levels.add(69);
        levels.add(99);

        // 排序确保从小到大
        Collections.sort(levels);

        // 添加到补全列表
        for (int level : levels) {
          completions.add(String.valueOf(level));
        }
      } else if (args.length == 2 && args[0].equalsIgnoreCase("突破")) {
        // 突破后面的数字补全 - 使用与直升符咒相同的等级
        List<Integer> breakthroughLevels = new ArrayList<>();

        // 添加特定高级等级选项
        breakthroughLevels.add(30);
        breakthroughLevels.add(50);
        breakthroughLevels.add(70);
        breakthroughLevels.add(100);

        // 对突破等级进行排序，确保从小到大
        Collections.sort(breakthroughLevels);

        // 将等级添加到补全列表中
        for (int level : breakthroughLevels) {
          completions.add(String.valueOf(level));
        }
      } else if (args.length == 2 && args[0].equalsIgnoreCase("强化棒")) {
        // 强化棒后面的数字补全 - 提供常用的提升等级选项
        List<Integer> rodLevels = new ArrayList<>();

        // 添加常用提升等级选项
        rodLevels.add(1);
        rodLevels.add(2);
        rodLevels.add(3);
        rodLevels.add(5);
        rodLevels.add(10);

        // 排序确保从小到大
        Collections.sort(rodLevels);

        // 添加到补全列表
        for (int level : rodLevels) {
          completions.add(String.valueOf(level));
        }
      } else if (args.length == 3 && (args[0].equalsIgnoreCase("突破") || args[0].equalsIgnoreCase("强化棒"))) {
        // 突破石或强化棒几率补全，从小到大排序
        List<Integer> chances = new ArrayList<>();
        chances.add(10);
        chances.add(30);
        chances.add(50);
        chances.add(80);
        chances.add(100);

        // 添加到补全列表
        for (int chance : chances) {
          completions.add(String.valueOf(chance));
        }
      } else if (args.length == 4 && (args[0].equalsIgnoreCase("突破") || args[0].equalsIgnoreCase("强化棒"))) {
        // 突破石或强化棒数量补全，从小到大排序
        List<Integer> amounts = new ArrayList<>();
        amounts.add(1);
        amounts.add(5);
        amounts.add(10);
        amounts.add(64);

        // 添加到补全列表
        for (int amount : amounts) {
          completions.add(String.valueOf(amount));
        }
      } else if (args.length == 5 && (args[0].equalsIgnoreCase("突破") || args[0].equalsIgnoreCase("强化棒"))) {
        // 玩家名补全
        for (Player player : this.getServer().getOnlinePlayers()) {
          completions.add(player.getName());
        }
      }
    }

    return completions;
  }

  public void onLoad() {
    this.saveDefaultConfig();
    this.config = this.getConfig();
    this.Initialize();
  }

  /**
   * 检查是否应该输出控制台日志
   *
   * @return 是否应该输出控制台日志
   */
  private boolean shouldLogToConsole() {
    return this.config.getBoolean("logging.console_logs", false);
  }

  /**
   * 输出控制台日志（如果启用）
   *
   * @param message 日志消息
   */
  private void logToConsole(String message) {
    if (shouldLogToConsole()) {
      System.out.println(message);
    }
  }

  /**
   * 加载战力排行榜数据
   */
  private void loadPowerRankingData() {
    // 清空现有数据
    playerPowers.clear();
    powerRanking.clear();

    // 从配置文件加载数据
    if (this.config.contains("power.players")) {
      for (String uuid : this.config.getConfigurationSection("power.players").getKeys(false)) {
        int power = this.config.getInt("power.players." + uuid + ".power");
        String playerName = this.config.getString("power.players." + uuid + ".name", "未知玩家");

        // 创建PowerData对象并添加到playerPowers
        PowerData powerData = new PowerData(playerName, uuid, power);
        playerPowers.put(uuid, powerData);
      }
    }

    // 更新排行榜
    updatePowerRanking();
  }

  /**
   * 获取最后一次排行榜更新时间
   *
   * @return 最后一次排行榜更新时间
   */
  public long getLastRankingUpdate() {
    return lastRankingUpdate;
  }

  /**
   * 获取排行榜更新间隔
   *
   * @return 排行榜更新间隔
   */
  public long getRankingUpdateInterval() {
    return rankingUpdateInterval;
  }

  /**
   * 获取战力排行榜
   *
   * @return 战力排行榜
   */
  public List<PowerData> getPowerRanking() {
    return powerRanking;
  }

  /**
   * 更新战力排行榜
   */
  public void updatePowerRanking() {
    // 清空现有排行榜
    powerRanking.clear();

    // 将玩家战力数据转换为列表
    for (PowerData powerData : playerPowers.values()) {
      powerRanking.add(powerData);
    }

    // 按战力值降序排序
    Collections.sort(powerRanking, new Comparator<PowerData>() {
      @Override
      public int compare(PowerData o1, PowerData o2) {
        return Integer.compare(o2.getPower(), o1.getPower());
      }
    });

    // 更新最后更新时间
    lastRankingUpdate = System.currentTimeMillis();
  }

  /**
   * 保存战力排行榜数据
   */
  private void savePowerRankingData() {
    // 保存到配置文件
    for (PowerData powerData : playerPowers.values()) {
      String uuid = powerData.getUuid();
      int power = powerData.getPower();
      String playerName = powerData.getPlayerName();

      // 保存战力值
      this.config.set("power.players." + uuid + ".power", power);

      // 保存玩家名称
      if (playerName != null && !playerName.isEmpty()) {
        this.config.set("power.players." + uuid + ".name", playerName);
      }
    }

    // 保存配置文件
    this.saveConfig();
  }

  /**
   * 更新玩家战力值
   *
   * @param player 玩家
   * @param power  战力值
   */
  private void updatePlayerPower(Player player, int power) {
    String uuid = player.getUniqueId().toString();
    String playerName = player.getName();

    // 创建或更新PowerData对象
    PowerData powerData = playerPowers.get(uuid);
    if (powerData == null) {
      powerData = new PowerData(playerName, uuid, power);
    } else {
      powerData.setPlayerName(playerName);
      powerData.setPower(power);
    }

    // 保存到playerPowers
    playerPowers.put(uuid, powerData);

    // 检查是否需要更新排行榜
    long currentTime = System.currentTimeMillis();
    if (currentTime - lastRankingUpdate > rankingUpdateInterval) {
      updatePowerRanking();
      savePowerRankingData();
    }
  }

  /**
   * 打开玩家装备信息菜单
   *
   * @param player       查看的玩家
   * @param targetPlayer 被查看的目标玩家
   */
  private void openPlayerEquipmentMenu(Player player, Player targetPlayer) {
    // 创建菜单
    Inventory menu = Bukkit.createInventory(null, 54, "§e§l✦ " + targetPlayer.getName() + " 的战力信息 ✦");

    // 填充菜单边框 - 使用黑色玻璃板作为背景
    ItemStack borderItem = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 15); // 黑色玻璃板
    ItemMeta borderMeta = borderItem.getItemMeta();
    borderMeta.setDisplayName("§8✦");
    borderItem.setItemMeta(borderMeta);

    // 填充边框
    for (int i = 0; i < 9; i++) {
      menu.setItem(i, borderItem); // 第一行
      menu.setItem(45 + i, borderItem); // 最后一行
    }

    for (int i = 0; i < 6; i++) {
      menu.setItem(i * 9, borderItem); // 左边框
      menu.setItem(i * 9 + 8, borderItem); // 右边框
    }

    // 添加装饰物品 - 使用不同颜色的玻璃板
    ItemStack decorItem1 = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 4); // 黄色玻璃板
    ItemMeta decorMeta1 = decorItem1.getItemMeta();
    decorMeta1.setDisplayName("§e✦");
    decorItem1.setItemMeta(decorMeta1);

    ItemStack decorItem2 = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 1); // 橙色玻璃板
    ItemMeta decorMeta2 = decorItem2.getItemMeta();
    decorMeta2.setDisplayName("§6✦");
    decorItem2.setItemMeta(decorMeta2);

    // 添加装饰
    menu.setItem(1, decorItem1);
    menu.setItem(7, decorItem1);
    menu.setItem(9, decorItem2);
    menu.setItem(17, decorItem2);
    menu.setItem(27, decorItem2);
    menu.setItem(35, decorItem2);
    menu.setItem(46, decorItem1);
    menu.setItem(52, decorItem1);

    // 添加玩家头颅
    ItemStack playerHead = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
    SkullMeta headMeta = (SkullMeta) playerHead.getItemMeta();
    headMeta.setOwner(targetPlayer.getName());
    headMeta.setDisplayName("§e§l" + targetPlayer.getName());
    List<String> headLore = new ArrayList<>();

    // 计算玩家总战力
    int totalPower = calculatePlayerPower(targetPlayer);

    headLore.add("§7");
    headLore.add("§f总战力值: §e" + totalPower);
    headLore.add("§7");

    headMeta.setLore(headLore);
    playerHead.setItemMeta(headMeta);
    menu.setItem(4, playerHead);

    // 获取玩家装备
    ItemStack helmet = targetPlayer.getInventory().getHelmet();
    ItemStack chestplate = targetPlayer.getInventory().getChestplate();
    ItemStack leggings = targetPlayer.getInventory().getLeggings();
    ItemStack boots = targetPlayer.getInventory().getBoots();
    ItemStack weapon = targetPlayer.getItemInHand();

    // 添加装备到菜单
    if (helmet != null && helmet.getType() != Material.AIR) {
      addEquipmentToMenu(menu, helmet, 20, "头盔");
    }

    if (chestplate != null && chestplate.getType() != Material.AIR) {
      addEquipmentToMenu(menu, chestplate, 21, "胸甲");
    }

    if (leggings != null && leggings.getType() != Material.AIR) {
      addEquipmentToMenu(menu, leggings, 22, "护腿");
    }

    if (boots != null && boots.getType() != Material.AIR) {
      addEquipmentToMenu(menu, boots, 23, "靴子");
    }

    if (weapon != null && weapon.getType() != Material.AIR) {
      addEquipmentToMenu(menu, weapon, 24, "武器");
    }

    // 添加总战力信息
    ItemStack powerItem = new ItemStack(Material.NETHER_STAR, 1);
    ItemMeta powerMeta = powerItem.getItemMeta();
    powerMeta.setDisplayName("§e§l✦ 总战力值: " + totalPower + " ✦");

    List<String> powerLore = new ArrayList<>();
    powerLore.add("§7");
    powerLore.add("§f装备强化等级战力: §a" + calculateEquipmentLevelPower(targetPlayer));
    powerLore.add("§f装备淬炼属性战力: §c" + calculateEquipmentAttributePower(targetPlayer));
    powerLore.add("§7");
    powerLore.add("§e提示: 强化装备和获得淬炼属性可以提升战力值");

    powerMeta.setLore(powerLore);
    powerItem.setItemMeta(powerMeta);
    menu.setItem(31, powerItem);

    // 添加关闭按钮 - 使用屏障方块
    ItemStack closeItem = new ItemStack(Material.BARRIER, 1);
    ItemMeta closeMeta = closeItem.getItemMeta();
    closeMeta.setDisplayName("§c§l关闭菜单");
    List<String> closeLore = new ArrayList<>();
    closeLore.add("§7点击关闭菜单");
    closeMeta.setLore(closeLore);
    closeItem.setItemMeta(closeMeta);

    // 放在最后一行中间位置
    menu.setItem(49, closeItem);

    // 打开菜单
    player.openInventory(menu);
  }

  /**
   * 添加装备到菜单
   *
   * @param menu      菜单
   * @param equipment 装备
   * @param slot      槽位
   * @param type      装备类型
   */
  private void addEquipmentToMenu(Inventory menu, ItemStack equipment, int slot, String type) {
    // 创建装备的副本
    ItemStack equipmentCopy = equipment.clone();
    ItemMeta meta = equipmentCopy.getItemMeta();

    // 获取原始lore
    List<String> lore = new ArrayList<>();
    if (meta.hasLore()) {
      lore.addAll(meta.getLore());
    }

    // 添加战力信息
    lore.add("§7");
    lore.add("§e§l✦ 装备战力信息 ✦");

    // 计算装备强化等级
    int level = getItemLevel(equipmentCopy);
    if (level > 0) {
      // 从配置文件中获取每级强化对应的战力值
      int powerPerLevel = this.config.getInt("power.level." + level, level); // 默认为等级值
      lore.add("§f强化等级战力: §a+" + powerPerLevel);
    } else {
      lore.add("§f强化等级战力: §70");
    }

    // 计算装备萃炼属性战力
    int attributePower = calculateItemAttributePower(equipmentCopy);
    if (attributePower > 0) {
      lore.add("§f淬炼属性战力: §c+" + attributePower);
    } else {
      lore.add("§f淬炼属性战力: §70");
    }

    // 计算总战力
    int totalPower = (level > 0 ? this.config.getInt("power.level." + level, level) : 0) + attributePower;
    lore.add("§f总战力值: §e" + totalPower);

    // 设置新的lore
    meta.setLore(lore);
    equipmentCopy.setItemMeta(meta);

    // 添加到菜单
    menu.setItem(slot, equipmentCopy);

    // 添加装备类型标签 - 使用玩家头颅显示
    ItemStack typeItem;

    // 如果是头盔，使用玩家头颅
    if (type.equals("头盔") && equipment.getType() == Material.SKULL_ITEM) {
      typeItem = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
      SkullMeta skullMeta = (SkullMeta) typeItem.getItemMeta();

      // 如果头盔是玩家头颅，获取头颅主人
      if (equipment.getDurability() == 3 && equipment.hasItemMeta() && equipment.getItemMeta() instanceof SkullMeta) {
        SkullMeta equipmentMeta = (SkullMeta) equipment.getItemMeta();
        if (equipmentMeta.hasOwner()) {
          skullMeta.setOwner(equipmentMeta.getOwner());
        }
      }

      skullMeta.setDisplayName("§e§l" + type);
      List<String> typeLore = new ArrayList<>();
      typeLore.add("§7点击上方查看详细信息");
      skullMeta.setLore(typeLore);
      typeItem.setItemMeta(skullMeta);
    } else {
      // 其他装备使用告示牌
      typeItem = new ItemStack(Material.SIGN, 1);
      ItemMeta typeMeta = typeItem.getItemMeta();
      typeMeta.setDisplayName("§e§l" + type);
      List<String> typeLore = new ArrayList<>();
      typeLore.add("§7点击上方查看详细信息");
      typeMeta.setLore(typeLore);
      typeItem.setItemMeta(typeMeta);
    }

    // 放在装备下方
    menu.setItem(slot + 9, typeItem);
  }

  /**
   * 计算装备的萃炼属性战力
   *
   * @param item 装备
   * @return 萃炼属性战力
   */
  private int calculateItemAttributePower(ItemStack item) {
    if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasLore()) {
      return 0;
    }

    int attributePower = 0;
    List<String> lore = item.getItemMeta().getLore();

    for (String line : lore) {
      // 去除颜色代码
      String cleanLine = stripColorCodes(line);

      // 检查萃炼属性
      if (cleanLine.contains("淬炼属性:") && cleanLine.contains("防御")) {
        try {
          String numStr = extractNumber(line);
          if (numStr != null && !numStr.isEmpty()) {
            if (numStr.contains(".")) {
              double value = Double.parseDouble(numStr);
              attributePower += (int) Math.round(value);
            } else {
              attributePower += Integer.parseInt(numStr);
            }
          }
        } catch (NumberFormatException e) {
          // 忽略解析错误
        }
      }

      if (cleanLine.contains("淬炼属性:") && cleanLine.contains("伤害")) {
        try {
          String numStr = extractNumber(line);
          if (numStr != null && !numStr.isEmpty()) {
            if (numStr.contains(".")) {
              double value = Double.parseDouble(numStr);
              attributePower += (int) Math.round(value);
            } else {
              attributePower += Integer.parseInt(numStr);
            }
          }
        } catch (NumberFormatException e) {
          // 忽略解析错误
        }
      }
    }

    return attributePower;
  }

  /**
   * 计算玩家装备强化等级带来的战力
   *
   * @param player 玩家
   * @return 装备强化等级战力
   */
  private int calculateEquipmentLevelPower(Player player) {
    int power = 0;

    // 检查头盔
    ItemStack helmet = player.getInventory().getHelmet();
    if (helmet != null && helmet.getType() != Material.AIR) {
      int level = getItemLevel(helmet);
      if (level > 0) {
        power += this.config.getInt("power.level." + level, level);
      }
    }

    // 检查胸甲
    ItemStack chestplate = player.getInventory().getChestplate();
    if (chestplate != null && chestplate.getType() != Material.AIR) {
      int level = getItemLevel(chestplate);
      if (level > 0) {
        power += this.config.getInt("power.level." + level, level);
      }
    }

    // 检查护腿
    ItemStack leggings = player.getInventory().getLeggings();
    if (leggings != null && leggings.getType() != Material.AIR) {
      int level = getItemLevel(leggings);
      if (level > 0) {
        power += this.config.getInt("power.level." + level, level);
      }
    }

    // 检查靴子
    ItemStack boots = player.getInventory().getBoots();
    if (boots != null && boots.getType() != Material.AIR) {
      int level = getItemLevel(boots);
      if (level > 0) {
        power += this.config.getInt("power.level." + level, level);
      }
    }

    // 检查武器
    ItemStack weapon = player.getItemInHand();
    if (weapon != null && weapon.getType() != Material.AIR) {
      int level = getItemLevel(weapon);
      if (level > 0) {
        power += this.config.getInt("power.level." + level, level);
      }
    }

    return power;
  }

  /**
   * 计算玩家装备萃炼属性带来的战力
   *
   * @param player 玩家
   * @return 装备萃炼属性战力
   */
  private int calculateEquipmentAttributePower(Player player) {
    int power = 0;

    // 检查头盔
    ItemStack helmet = player.getInventory().getHelmet();
    if (helmet != null && helmet.getType() != Material.AIR) {
      power += calculateItemAttributePower(helmet);
    }

    // 检查胸甲
    ItemStack chestplate = player.getInventory().getChestplate();
    if (chestplate != null && chestplate.getType() != Material.AIR) {
      power += calculateItemAttributePower(chestplate);
    }

    // 检查护腿
    ItemStack leggings = player.getInventory().getLeggings();
    if (leggings != null && leggings.getType() != Material.AIR) {
      power += calculateItemAttributePower(leggings);
    }

    // 检查靴子
    ItemStack boots = player.getInventory().getBoots();
    if (boots != null && boots.getType() != Material.AIR) {
      power += calculateItemAttributePower(boots);
    }

    // 检查武器
    ItemStack weapon = player.getItemInHand();
    if (weapon != null && weapon.getType() != Material.AIR) {
      power += calculateItemAttributePower(weapon);
    }

    return power;
  }

  /**
   * 打开战力排行榜菜单
   *
   * @param player 玩家
   */
  private void openPowerRankingMenu(Player player) {
    // 确保排行榜是最新的
    if (System.currentTimeMillis() - lastRankingUpdate > rankingUpdateInterval) {
      updatePowerRanking();
    }

    // 创建排行榜菜单 - 6行9列的大小
    Inventory menu = Bukkit.createInventory(null, 54, "§e§l✦ 战力排行榜 ✦");

    // 填充菜单边框 - 使用黑色玻璃板作为背景
    ItemStack borderItem = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 15); // 黑色玻璃板
    ItemMeta borderMeta = borderItem.getItemMeta();
    borderMeta.setDisplayName("§8✦");
    borderItem.setItemMeta(borderMeta);

    // 填充边框
    for (int i = 0; i < 9; i++) {
      menu.setItem(i, borderItem); // 第一行
      menu.setItem(45 + i, borderItem); // 最后一行
    }

    for (int i = 0; i < 6; i++) {
      menu.setItem(i * 9, borderItem); // 左边框
      menu.setItem(i * 9 + 8, borderItem); // 右边框
    }

    // 添加装饰物品 - 使用不同颜色的玻璃板
    ItemStack decorItem1 = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 4); // 黄色玻璃板
    ItemMeta decorMeta1 = decorItem1.getItemMeta();
    decorMeta1.setDisplayName("§e✦");
    decorItem1.setItemMeta(decorMeta1);

    ItemStack decorItem2 = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 1); // 橙色玻璃板
    ItemMeta decorMeta2 = decorItem2.getItemMeta();
    decorMeta2.setDisplayName("§6✦");
    decorItem2.setItemMeta(decorMeta2);

    // 添加装饰
    menu.setItem(1, decorItem1);
    menu.setItem(7, decorItem1);
    menu.setItem(9, decorItem2);
    menu.setItem(17, decorItem2);
    menu.setItem(27, decorItem2);
    menu.setItem(35, decorItem2);
    menu.setItem(46, decorItem1);
    menu.setItem(52, decorItem1);

    // 添加排行榜标题 - 使用金块
    ItemStack titleItem = new ItemStack(Material.GOLD_BLOCK, 1);
    ItemMeta titleMeta = titleItem.getItemMeta();
    titleMeta.setDisplayName("§e§l✦ 战力排行榜 ✦");
    List<String> titleLore = new ArrayList<>();
    titleLore.add("§7显示服务器战力值最高的10名玩家");
    titleLore.add("§7战力值由装备强化等级和淬炼属性决定");
    titleLore.add("§7");

    // 获取配置文件中的更新间隔
    int updateIntervalMinutes = this.config.getInt("power.ranking_update_interval", 5);
    titleLore.add("§7排行榜每§e" + updateIntervalMinutes + "分钟§7更新一次");

    // 计算上次更新时间
    long timeSinceLastUpdate = System.currentTimeMillis() - lastRankingUpdate;
    long minutesSinceLastUpdate = timeSinceLastUpdate / 60000;
    long secondsSinceLastUpdate = (timeSinceLastUpdate % 60000) / 1000;

    if (lastRankingUpdate > 0) {
      titleLore.add("§7上次更新: §e" + minutesSinceLastUpdate + "§7分§e" + secondsSinceLastUpdate + "§7秒前");
    } else {
      titleLore.add("§7上次更新: §e刚刚");
    }

    titleMeta.setLore(titleLore);
    titleItem.setItemMeta(titleMeta);
    menu.setItem(4, titleItem);

    // 添加排行榜项目
    int maxRank = Math.min(10, powerRanking.size());
    int[] slots = { 21, 22, 23, 30, 31, 32, 39, 40, 41, 49 }; // 排行榜显示位置

    for (int i = 0; i < maxRank; i++) {
      PowerData powerData = powerRanking.get(i);
      String uuid = powerData.getUuid();
      int power = powerData.getPower();

      // 获取玩家名称
      String playerName = powerData.getPlayerName();

      // 创建头颅物品
      ItemStack skull = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
      SkullMeta meta = (SkullMeta) skull.getItemMeta();

      // 设置头颅拥有者 - 确保显示玩家的皮肤
      try {
        meta.setOwner(playerName);
      } catch (Exception e) {
        // 忽略错误
      }

      // 设置显示名称和颜色
      String rankColor = "";
      String prefix = "";
      switch (i) {
        case 0:
          rankColor = "§6"; // 金色
          prefix = "§6§l✫ 冠军 ✫ ";
          break;
        case 1:
          rankColor = "§f"; // 银色
          prefix = "§f§l✧ 亚军 ✧ ";
          break;
        case 2:
          rankColor = "§c"; // 铜色
          prefix = "§c§l✦ 季军 ✦ ";
          break;
        default:
          rankColor = "§7"; // 灰色
          prefix = "§7§l第" + (i + 1) + "名 ";
      }

      meta.setDisplayName(prefix + rankColor + playerName);

      // 设置lore - 添加更多信息
      List<String> lore = new ArrayList<>();
      lore.add("§7");
      lore.add(rankColor + "✦ §f战力值: " + rankColor + power);
      lore.add("§7");

      // 根据排名添加特殊描述
      if (i == 0) {
        lore.add("§6✫ §e服务器最强战力 §6✫");
      } else if (i < 3) {
        lore.add("§e✧ §f服务器顶尖战力 §e✧");
      } else if (i < 5) {
        lore.add("§a✦ §f服务器精英战力 §a✦");
      } else {
        lore.add("§7✦ §f实力不俗 §7✦");
      }

      meta.setLore(lore);
      skull.setItemMeta(meta);

      // 添加到菜单
      menu.setItem(slots[i], skull);

      // 为前三名添加特殊装饰
      if (i == 0) {
        // 冠军装饰 - 金块
        ItemStack crown = new ItemStack(Material.GOLD_BLOCK, 1);
        ItemMeta crownMeta = crown.getItemMeta();
        crownMeta.setDisplayName("§6§l✫ 冠军宝座 ✫");
        crown.setItemMeta(crownMeta);
        menu.setItem(12, crown);
      } else if (i == 1) {
        // 亚军装饰 - 铁块
        ItemStack silver = new ItemStack(Material.IRON_BLOCK, 1);
        ItemMeta silverMeta = silver.getItemMeta();
        silverMeta.setDisplayName("§f§l✧ 亚军宝座 ✧");
        silver.setItemMeta(silverMeta);
        menu.setItem(13, silver);
      } else if (i == 2) {
        // 季军装饰 - 红石块
        ItemStack bronze = new ItemStack(Material.REDSTONE_BLOCK, 1);
        ItemMeta bronzeMeta = bronze.getItemMeta();
        bronzeMeta.setDisplayName("§c§l✦ 季军宝座 ✦");
        bronze.setItemMeta(bronzeMeta);
        menu.setItem(14, bronze);
      }
    }

    // 添加玩家自己的排名信息
    String playerUUID = player.getUniqueId().toString();
    int playerPower = 0;
    int playerRank = -1;

    // 如果玩家有战力数据，获取战力值和排名
    if (playerPowers.containsKey(playerUUID)) {
      PowerData playerData = playerPowers.get(playerUUID);
      playerPower = playerData.getPower();

      // 查找玩家排名
      for (int i = 0; i < powerRanking.size(); i++) {
        if (powerRanking.get(i).getUuid().equals(playerUUID)) {
          playerRank = i + 1;
          break;
        }
      }
    }

    // 创建玩家排名物品 - 无论玩家是否有战力数据都显示
    ItemStack playerItem = new ItemStack(Material.NETHER_STAR, 1);
    ItemMeta playerMeta = playerItem.getItemMeta();
    playerMeta.setDisplayName("§b§l✧ 您的战力排名 ✧");

    List<String> playerLore = new ArrayList<>();
    playerLore.add("§7");
    playerLore.add("§f战力值: §b" + playerPower);

    if (playerRank > 0) {
      playerLore.add("§f当前排名: §b第" + playerRank + "名");
    } else {
      playerLore.add("§f当前排名: §c暂无排名");
    }

    playerLore.add("§7");
    playerLore.add("§e提示: 强化装备和获得淬炼属性可以提升战力值");

    playerMeta.setLore(playerLore);
    playerItem.setItemMeta(playerMeta);

    menu.setItem(50, playerItem);

    // 添加关闭按钮 - 使用屏障方块
    ItemStack closeItem = new ItemStack(Material.BARRIER, 1);
    ItemMeta closeMeta = closeItem.getItemMeta();
    closeMeta.setDisplayName("§c§l关闭菜单");
    List<String> closeLore = new ArrayList<>();
    closeLore.add("§7点击关闭菜单");
    closeMeta.setLore(closeLore);
    closeItem.setItemMeta(closeMeta);

    // 放在下界之星左边
    menu.setItem(48, closeItem);

    // 打开菜单
    player.openInventory(menu);
  }

  public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
    if (!(sender instanceof Player)) {
      sender.sendMessage(this.get(1));
      return true;
    }
    String cmd = command.getName();
    Player p = (Player) sender;

    // 处理战力系统命令
    if (cmd.equalsIgnoreCase("zl")) {
      // 无参数时显示命令列表
      if (args.length == 0) {
        this.show(p, "§7==------->> §e【§c战力系统§e】§7 <<-------==");

        // 显示普通玩家可见的命令
        this.show(p, "§b-->§6/zl power             §7- §a§l查看自己的战力值");
        this.show(p, "§b-->§6/zl top               §7- §a§l查看战力排行榜");

        // 显示有权限玩家可见的命令
        if (p.hasPermission("intensify.cx")) {
          this.show(p, "§b-->§6/zl cx <玩家名>       §7- §a§l查看指定玩家的装备信息");
        }

        // 显示管理员可见的命令
        if (p.hasPermission(this.config.getString("Admin"))) {
          this.show(p, "§b-->§6/zl update           §7- §a§l手动更新战力排行榜");
        }

        return true;
      }

      // 处理子命令
      if (args[0].equalsIgnoreCase("power")) {
        // 检查冷却时间
        String playerUUID = p.getUniqueId().toString();
        long currentTime = System.currentTimeMillis();
        int cooldownSeconds = this.config.getInt("power.cooldown", 10);

        // 检查是否在冷却时间内
        if (powerCooldowns.containsKey(playerUUID) &&
            currentTime - powerCooldowns.get(playerUUID) < cooldownSeconds * 1000) {
          // 计算剩余冷却时间
          long remainingTime = (powerCooldowns.get(playerUUID) + cooldownSeconds * 1000 - currentTime) / 1000;
          this.show(p, this.get(18).replace("{0}", String.valueOf(remainingTime)));
          return true;
        }

        // 更新冷却时间
        powerCooldowns.put(playerUUID, currentTime);

        // 计算战力值
        int totalPower = calculatePlayerPower(p);

        // 更新玩家战力
        updatePlayerPower(p, totalPower);

        // 显示战力值
        this.show(p, this.get(17).replace("{0}", String.valueOf(totalPower)));
        return true;
      }

      // 处理排行榜命令
      if (args[0].equalsIgnoreCase("top")) {
        openPowerRankingMenu(p);
        return true;
      }

      // 处理查询命令
      if (args[0].equalsIgnoreCase("cx")) {
        // 检查权限
        if (!p.hasPermission("intensify.cx")) {
          this.show(p, "§e【§c战力系统§e】 §c你没有权限使用此命令");
          return true;
        }

        if (args.length < 2) {
          this.show(p, "§e【§c战力系统§e】 §c请指定要查询的玩家名称");
          return true;
        }

        // 获取目标玩家
        String targetName = args[1];
        Player targetPlayer = Bukkit.getPlayer(targetName);

        if (targetPlayer == null || !targetPlayer.isOnline()) {
          this.show(p, "§e【§c战力系统§e】 §c玩家 " + targetName + " 不在线");
          return true;
        }

        // 打开查询菜单
        openPlayerEquipmentMenu(p, targetPlayer);
        return true;
      }

      // 处理更新排行榜命令
      if (args[0].equalsIgnoreCase("update")) {
        // 检查权限
        if (!p.hasPermission(this.config.getString("Admin"))) {
          this.show(p, "§e【§c战力系统§e】 §c你没有权限使用此命令");
          return true;
        }

        // 更新排行榜
        updatePowerRanking();
        savePowerRankingData();

        // 获取配置文件中的更新间隔
        int updateIntervalMinutes = this.config.getInt("power.ranking_update_interval", 5);

        // 发送成功消息
        this.show(p, "§e【§c战力系统§e】 §a排行榜已手动更新成功！下次自动更新将在 " + updateIntervalMinutes + " 分钟后");
        return true;
      }

      // 未知子命令
      this.show(p, "§e【§c战力系统§e】 §c未知命令，请使用 /zl power、/zl top、/zl update 或 /zl cx <玩家名>");
      return true;
    }

    // 处理强化系统命令
    if (cmd.equalsIgnoreCase("qh")) {
      if (args.length == 0) {
        this.show(p, "§7==------->> §e【§c强化系统§e】§7 <<-------==");

        // 显示普通玩家可见的强化系统命令
        this.show(p, "§b-->§6/qh power             §7- §a§l查看自己的战力值");

        // 显示管理员可见的强化系统命令
        if (p.hasPermission(this.config.getString("Admin"))) {
          this.show(p, "§b-->§6/qh get <类型> <数量> §7- §a§l获得一个强化素材");
          this.show(p, "§b-->§6/qh reload            §7- §a§l重载插件");
          this.show(p, "§b-->§6/qh set <等级>         §7- §a§l设置手持物品的强化等级");
          this.show(p, "§b-->§6/qh 直升 <等级> [玩家]  §7- §a§l创建强化直升符咒");
          this.show(p, "§b-->§6/qh 突破 <等级> [玩家]  §7- §a§l创建突破石");
          this.show(p, "§b-->§6/qh 强化棒 <等级> [几率] [数量] [玩家] §7- §a§l创建强化棒");
        }

        return true;
      }
      if (!p.hasPermission(this.config.getString("Admin"))) {
        return true;
      }
      if (args[0].equalsIgnoreCase("get")) {
        if (args.length == 1) {
          return true;
        }
        if (args.length == 2) {
          return true;
        }
        if (args[1].equalsIgnoreCase("normal")) {
          ItemStack is = this.normalItem;
          if (args.length == 3) {
            int amount = Integer.parseInt(args[2]);
            is.setAmount(amount);
          }
          p.getInventory().addItem(new ItemStack[] { is });
          this.show(p, this.get(6));
          return true;
        }
        if (args[1].equalsIgnoreCase("luck")) {
          ItemStack is = this.luckItem;
          if (args.length == 3) {
            int amount = Integer.parseInt(args[2]);
            is.setAmount(amount);
          }
          p.getInventory().addItem(new ItemStack[] { is });
          this.show(p, this.get(6));
          return true;
        }
        if (args[1].equalsIgnoreCase("safe")) {
          ItemStack is = this.safeItem;
          if (args.length == 3) {
            int amount = Integer.parseInt(args[2]);
            is.setAmount(amount);
          }
          p.getInventory().addItem(new ItemStack[] { is });
          this.show(p, this.get(6));
          return true;
        }
        if (args[1].equalsIgnoreCase("vip")) {
          ItemStack is = this.vipItem;
          if (args.length == 3) {
            int amount = Integer.parseInt(args[2]);
            is.setAmount(amount);
          }
          p.getInventory().addItem(new ItemStack[] { is });
          this.show(p, this.get(6));
          return true;
        }
        if (args[1].equalsIgnoreCase("admin")) {
          ItemStack is = this.adminItem;
          if (args.length == 3) {
            int amount = Integer.parseInt(args[2]);
            is.setAmount(amount);
          }
          p.getInventory().addItem(new ItemStack[] { is });
          this.show(p, this.get(6));
          return true;
        }
      }
      if (args[0].equalsIgnoreCase("reload")) {
        this.reload();
        this.show(p, this.get(7));
        return true;
      }

      // 处理查看战力命令
      if (args[0].equalsIgnoreCase("power")) {
        // 检查冷却时间
        String playerUUID = p.getUniqueId().toString();
        long currentTime = System.currentTimeMillis();
        int cooldownSeconds = this.config.getInt("power.cooldown", 10);

        // 检查是否在冷却时间内
        if (powerCooldowns.containsKey(playerUUID) &&
            currentTime - powerCooldowns.get(playerUUID) < cooldownSeconds * 1000) {
          // 计算剩余冷却时间
          long remainingTime = (powerCooldowns.get(playerUUID) + cooldownSeconds * 1000 - currentTime) / 1000;
          this.show(p, this.get(18).replace("{0}", String.valueOf(remainingTime)));
          return true;
        }

        // 更新冷却时间
        powerCooldowns.put(playerUUID, currentTime);

        // 计算战力值
        int totalPower = calculatePlayerPower(p);

        // 更新玩家战力
        updatePlayerPower(p, totalPower);

        // 显示战力值
        this.show(p, this.get(17).replace("{0}", String.valueOf(totalPower)));
        return true;
      }

      // 直升功能 - 创建直升符咒
      if (args[0].equalsIgnoreCase("直升") && args.length == 2) {
        try {
          int level = Integer.parseInt(args[1]);
          if (level < 1) {
            this.show(p, "§e【§c强化系统§e】 §c等级必须大于0");
            return true;
          }

          // 使用配置文件创建直升符咒
          ItemStack directItem = createDirectUpgradeItem(level, 1);

          // 获取等级要求文本用于消息显示
          int requiredLevel = 0;
          if (this.config.contains("items.direct_upgrade.level_requirements." + level)) {
            requiredLevel = this.config.getInt("items.direct_upgrade.level_requirements." + level);
          } else {
            // 如果没有特定等级的配置，使用默认规则
            if (level == 29)
              requiredLevel = 20;
            else if (level == 49)
              requiredLevel = 29;
            else if (level == 69)
              requiredLevel = 49;
            else if (level == 99)
              requiredLevel = 69;
            else
              requiredLevel = Math.max(1, level - 10); // 默认需要前10级
          }

          String requirementText = "";
          if (requiredLevel > 0) {
            requirementText = " §c(需要装备已达到" + requiredLevel + "级)";
          }

          p.getInventory().addItem(directItem);
          this.show(p, "§e【§c强化系统§e】 §a已获得强化直升符咒(等级" + level + ")" + requirementText);
          return true;
        } catch (NumberFormatException e) {
          this.show(p, "§e【§c强化系统§e】 §c请输入有效的数字");
          return true;
        }
      }

      // 强化棒功能 - 创建强化棒
      if (args[0].equalsIgnoreCase("强化棒")) {
        try {
          // 基本格式: /qh 强化棒 <提升等级> [几率] [数量] [玩家名]
          if (args.length < 2) {
            this.show(p, "§e【§c强化系统§e】 §c用法: /qh 强化棒 <提升等级> [几率] [数量] [玩家名]");
            return true;
          }

          int level = Integer.parseInt(args[1]);
          int chance = this.config.getInt("items.enhancement_rod.default_chance", 80); // 默认使用配置文件中的值
          int amount = 1; // 默认数量1
          Player targetPlayer = p; // 默认给自己

          // 解析几率参数
          if (args.length >= 3) {
            try {
              chance = Integer.parseInt(args[2]);
              if (chance < 0)
                chance = 0;
              if (chance > 100)
                chance = 100;
            } catch (NumberFormatException e) {
              this.show(p, "§e【§c强化系统§e】 §c几率必须是0-100之间的数字");
              return true;
            }
          }

          // 解析数量参数
          if (args.length >= 4) {
            try {
              amount = Integer.parseInt(args[3]);
              if (amount < 1)
                amount = 1;
              if (amount > 64)
                amount = 64;
            } catch (NumberFormatException e) {
              this.show(p, "§e【§c强化系统§e】 §c数量必须是1-64之间的数字");
              return true;
            }
          }

          // 解析玩家名参数
          if (args.length >= 5) {
            String playerName = args[4];
            targetPlayer = this.getServer().getPlayer(playerName);
            if (targetPlayer == null) {
              this.show(p, "§e【§c强化系统§e】 §c找不到玩家: " + playerName);
              return true;
            }
          }

          // 检查等级是否有效
          if (level <= 0) {
            this.show(p, "§e【§c强化系统§e】 §c提升等级必须大于0");
            return true;
          }

          // 创建强化棒
          ItemStack rodItem = createEnhancementRod(level, chance, amount);

          // 给目标玩家物品
          targetPlayer.getInventory().addItem(rodItem);

          // 发送消息
          String chanceText = "";
          if (chance != 100) {
            chanceText = " §e[" + chance + "%几率]";
          }

          if (targetPlayer == p) {
            this.show(p, "§e【§c强化系统§e】 §a已获得" + amount + "个强化棒(+" + level + ")" + chanceText);
          } else {
            this.show(p, "§e【§c强化系统§e】 §a已给予玩家 §e" + targetPlayer.getName() + " §a" + amount + "个强化棒(+" + level + ")"
                + chanceText);
            this.show(targetPlayer,
                "§e【§c强化系统§e】 §a您收到了来自 §e" + p.getName() + " §a的" + amount + "个强化棒(+" + level + ")" + chanceText);
          }

          return true;
        } catch (NumberFormatException e) {
          this.show(p, "§e【§c强化系统§e】 §c请输入有效的数字");
          return true;
        }
      }

      // 强化棒功能 - 创建强化棒
      if (args[0].equalsIgnoreCase("强化棒")) {
        try {
          // 基本格式: /qh 强化棒 <提升等级> [几率] [数量] [玩家名]
          if (args.length < 2) {
            this.show(p, "§e【§c强化系统§e】 §c用法: /qh 强化棒 <提升等级> [几率] [数量] [玩家名]");
            return true;
          }

          int level = Integer.parseInt(args[1]);
          int chance = this.config.getInt("items.enhancement_rod.default_chance", 80); // 默认使用配置文件中的值
          int amount = 1; // 默认数量1
          Player targetPlayer = p; // 默认给自己

          // 解析几率参数
          if (args.length >= 3) {
            try {
              chance = Integer.parseInt(args[2]);
              if (chance < 0)
                chance = 0;
              if (chance > 100)
                chance = 100;
            } catch (NumberFormatException e) {
              this.show(p, "§e【§c强化系统§e】 §c几率必须是0-100之间的数字");
              return true;
            }
          }

          // 解析数量参数
          if (args.length >= 4) {
            try {
              amount = Integer.parseInt(args[3]);
              if (amount < 1)
                amount = 1;
              if (amount > 64)
                amount = 64;
            } catch (NumberFormatException e) {
              this.show(p, "§e【§c强化系统§e】 §c数量必须是1-64之间的数字");
              return true;
            }
          }

          // 解析玩家名参数
          if (args.length >= 5) {
            String playerName = args[4];
            targetPlayer = this.getServer().getPlayer(playerName);
            if (targetPlayer == null) {
              this.show(p, "§e【§c强化系统§e】 §c找不到玩家: " + playerName);
              return true;
            }
          }

          // 检查等级是否有效
          if (level <= 0) {
            this.show(p, "§e【§c强化系统§e】 §c提升等级必须大于0");
            return true;
          }

          // 创建强化棒
          ItemStack rodItem = createEnhancementRod(level, chance, amount);

          // 给目标玩家物品
          targetPlayer.getInventory().addItem(rodItem);

          // 发送消息
          String chanceText = "";
          if (chance != 100) {
            chanceText = " §e[" + chance + "%几率]";
          }

          if (targetPlayer == p) {
            this.show(p, "§e【§c强化系统§e】 §a已获得" + amount + "个强化棒(+" + level + ")" + chanceText);
          } else {
            this.show(p, "§e【§c强化系统§e】 §a已给予玩家 §e" + targetPlayer.getName() + " §a" + amount + "个强化棒(+" + level + ")"
                + chanceText);
            this.show(targetPlayer,
                "§e【§c强化系统§e】 §a您收到了来自 §e" + p.getName() + " §a的" + amount + "个强化棒(+" + level + ")" + chanceText);
          }

          return true;
        } catch (NumberFormatException e) {
          this.show(p, "§e【§c强化系统§e】 §c请输入有效的数字");
          return true;
        }
      }

      // 突破功能 - 创建突破石
      if (args[0].equalsIgnoreCase("突破")) {
        try {
          // 基本格式: /qh 突破 <等级> [玩家名]
          if (args.length < 2) {
            this.show(p, "§e【§c强化系统§e】 §c用法: /qh 突破 <等级> [玩家名]");
            return true;
          }

          int level = Integer.parseInt(args[1]);
          int amount = 1; // 默认数量1
          Player targetPlayer = p; // 默认给自己

          // 检查是否是 /qh 突破 <等级> [玩家名] 格式
          if (args.length == 3) {
            // 这是 /qh 突破 <等级> [玩家名] 格式
            String playerName = args[2];
            targetPlayer = this.getServer().getPlayer(playerName);
            if (targetPlayer == null) {
              this.show(p, "§e【§c强化系统§e】 §c找不到玩家: " + playerName);
              return true;
            }

            // 记录日志
            if (this.config.getBoolean("style.debug_logs", false)) {
              logToConsole("指令创建突破石 - 等级: " + level + ", 玩家: " + targetPlayer.getName() + ", 使用配置文件中的几率");
            }
          } else {
            // 记录日志
            if (this.config.getBoolean("style.debug_logs", false)) {
              logToConsole("指令创建突破石 - 等级: " + level + ", 未指定玩家，将给予自己，使用配置文件中的几率");
            }
          }

          // 解析数量参数
          if (args.length >= 4) {
            try {
              amount = Integer.parseInt(args[3]);
              if (amount < 1)
                amount = 1;
              if (amount > 64)
                amount = 64;
            } catch (NumberFormatException e) {
              this.show(p, "§e【§c强化系统§e】 §c数量必须是1-64之间的数字");
              return true;
            }
          }

          // 解析玩家名参数
          if (args.length >= 5) {
            String playerName = args[4];
            targetPlayer = this.getServer().getPlayer(playerName);
            if (targetPlayer == null) {
              this.show(p, "§e【§c强化系统§e】 §c找不到玩家: " + playerName);
              return true;
            }
          }

          // 检查等级是否有效
          if (level <= 0) {
            this.show(p, "§e【§c强化系统§e】 §c等级必须大于0");
            return true;
          }

          // 使用配置文件创建突破石
          // 从配置文件获取对应等级的成功几率
          int configChance = 100; // 默认100%

          if (this.config.contains("breakthrough.success_chance." + level)) {
            configChance = this.config.getInt("breakthrough.success_chance." + level);
            // 记录日志
            if (this.config.getBoolean("style.debug_logs", false)) {
              logToConsole("从配置文件获取几率: " + level + "级突破石 - " + configChance + "%");
            }
          } else {
            // 如果没有特定等级的配置，使用默认值
            configChance = this.config.getInt("breakthrough.success_chance.default", 50);
            // 记录日志
            if (this.config.getBoolean("style.debug_logs", false)) {
              logToConsole("使用默认成功几率: " + configChance + "%");
            }
          }

          // 创建突破石
          ItemStack breakthroughItem = createBreakthroughStone(level, configChance, amount);

          // 获取等级要求文本用于消息显示
          int requiredLevel = 0;
          if (this.config.contains("breakthrough.levels." + level)) {
            requiredLevel = this.config.getInt("breakthrough.levels." + level);
          } else {
            // 如果配置文件中没有设置，则使用默认规则：需要前一级
            requiredLevel = level - 1;
          }

          String requirementText = "§c(需要装备已达到" + requiredLevel + "级)";
          String chanceText = "";
          if (configChance != 100) {
            chanceText = " §e[" + configChance + "%几率]";
          }

          // 给目标玩家物品
          targetPlayer.getInventory().addItem(breakthroughItem);

          // 发送消息
          if (targetPlayer == p) {
            this.show(p, "§e【§c强化系统§e】 §a已获得" + amount + "个强化突破石(等级" + level + chanceText + ") " + requirementText);
          } else {
            this.show(p, "§e【§c强化系统§e】 §a已给予玩家 §e" + targetPlayer.getName() + " §a" + amount + "个强化突破石(等级" + level
                + chanceText + ") " + requirementText);
            this.show(targetPlayer, "§e【§c强化系统§e】 §a您收到了来自 §e" + p.getName() + " §a的" + amount + "个强化突破石(等级" + level
                + chanceText + ") " + requirementText);
          }

          return true;
        } catch (NumberFormatException e) {
          this.show(p, "§e【§c强化系统§e】 §c请输入有效的数字");
          return true;
        }
      }

      // 处理设置强化等级命令
      if (args[0].equalsIgnoreCase("set")) {
        // 检查权限
        if (!p.hasPermission(this.config.getString("Admin"))) {
          this.show(p, "§e【§c强化系统§e】 §c你没有权限使用此命令");
          return true;
        }

        if (args.length < 2) {
          this.show(p, "§e【§c强化系统§e】 §c请指定强化等级");
          return true;
        }

        // 获取强化等级
        int level;
        try {
          level = Integer.parseInt(args[1]);
          if (level < 0) {
            this.show(p, "§e【§c强化系统§e】 §c强化等级不能为负数");
            return true;
          }
        } catch (NumberFormatException e) {
          this.show(p, "§e【§c强化系统§e】 §c强化等级必须是数字");
          return true;
        }

        // 获取玩家手中的物品
        ItemStack item = p.getItemInHand();
        if (item == null || item.getType() == Material.AIR) {
          this.show(p, "§e【§c强化系统§e】 §c请手持要设置强化等级的物品");
          return true;
        }

        // 检查物品是否可以强化
        boolean isEnhanceable = false;
        for (String i : this.config.getConfigurationSection("id.items").getKeys(false)) {
          int itemId = Integer.parseInt(i);
          if (itemId == item.getTypeId()) {
            isEnhanceable = true;
            break;
          }
        }

        if (!isEnhanceable) {
          this.show(p, "§e【§c强化系统§e】 §c此物品不能被强化");
          return true;
        }

        // 设置物品强化等级
        setItemEnchantLevel(p, item, level);

        // 发送成功消息
        this.show(p, "§e【§c强化系统§e】 §a已将物品强化等级设置为 " + level);
        return true;
      }

      return true;
    }
    return false;

  }

  public String get(int id) {
    return this.config.getString("lang_" + id).replace("&", "\u00a7");
  }

  public void show(Player p, String msg) {
    p.sendMessage(msg);
  }

  public boolean Rdom(int level, int chance) {
    // 获取基础几率
    int baseChance = getBaseEnhancementChance(level);

    // 计算最终几率：基础几率 + 强化石加成
    int finalChance = baseChance + chance;

    // 确保几率不超过100%
    if (finalChance > 100) {
      finalChance = 100;
    }

    // 记录日志
    if (this.config.getBoolean("style.debug_logs", false)) {
      logToConsole(
          "强化几率计算 - 等级: " + level + ", 基础几率: " + baseChance + "%, 加成: " + chance + "%, 最终几率: " + finalChance + "%");
    }

    // 计算最终几率并返回结果
    return this.rm.nextInt(100) < finalChance;
  }

  public void reload() {
    this.reloadConfig();
    BlockId.clear();
    MobId.clear();
    this.random.clear();
    this.Initialize();
    this.config = this.getConfig();
  }

  public void Initialize() {
    this.normalItem = this.config.getItemStack("items.main_normal");
    this.luckItem = this.config.getItemStack("items.main_luck");
    this.safeItem = this.config.getItemStack("items.main_safe");
    this.vipItem = this.config.getItemStack("items.main_vip");
    this.adminItem = this.config.getItemStack("items.main_admin");
    // 初始化基础几率哈希表（向后兼容）
    // 注意：这个哈希表现在主要用于向后兼容，实际的基础几率计算使用getBaseEnhancementChance方法

    // 检查配置格式
    boolean isOldFormat = false;
    try {
      // 检查是否是旧的逗号分隔格式
      String defaultChances = this.config.getString("chance.default");
      if (defaultChances != null && defaultChances.contains(",") && !defaultChances.contains("MemorySection")) {
        // 这是旧的逗号分隔格式
        isOldFormat = true;
        String[] chances = defaultChances.split(",");
        for (int i = 0; i < Math.min(chances.length, 11); i++) {
          int a = Integer.valueOf(chances[i]);
          this.random.put(i, a);
        }
        if (this.config.getBoolean("style.debug_logs", false)) {
          System.out.println("检测到旧的逗号分隔格式基础几率配置");
        }
      }
    } catch (Exception e) {
      // 解析旧格式失败
      if (this.config.getBoolean("style.debug_logs", false)) {
        System.out.println("解析旧格式基础几率配置失败: " + e.getMessage());
      }
    }

    // 如果不是旧格式，检查是否是新的对象格式
    if (!isOldFormat) {
      ConfigurationSection defaultSection = this.config.getConfigurationSection("chance.default");
      if (defaultSection != null) {
        // 这是新的对象格式，使用默认值填充random哈希表（仅用于向后兼容）
        int[] defaultValues = { 100, 100, 85, 70, 55, 40, 25, 15, 10, 5, 1 };
        for (int i = 0; i < defaultValues.length; i++) {
          this.random.put(i, defaultValues[i]);
        }
        if (this.config.getBoolean("style.debug_logs", false)) {
          System.out.println("检测到新的混合格式基础几率配置，random哈希表使用默认值（实际几率由getBaseEnhancementChance方法计算）");
        }
      } else {
        // 没有找到任何配置，使用默认值
        int[] defaultValues = { 100, 100, 85, 70, 55, 40, 25, 15, 10, 5, 1 };
        for (int i = 0; i < defaultValues.length; i++) {
          this.random.put(i, defaultValues[i]);
        }
        if (this.config.getBoolean("style.debug_logs", false)) {
          System.out.println("未找到基础几率配置，使用默认值");
        }
      }
    }
    for (String s : this.config.getStringList("drop.blocks")) {
      BlockId.add(s);
    }
    for (String s : this.config.getStringList("drop.mobs")) {
      MobId.add(s);
    }
    this.cg = this.config.getString("style.a", "§c§l◆");
    this.sb = this.config.getString("style.b", "§7§l◇");

    // 确保符号不为null
    if (this.cg == null) {
      this.cg = "§c§l◆";
    }
    if (this.sb == null) {
      this.sb = "§7§l◇";
    }

    if (this.config.getBoolean("style.debug_logs", false)) {
      System.out.println("加载符号 - 有等级符号: " + this.cg + ", 无等级符号: " + this.sb);
    }
    // 初始化强化石加成值（兼容旧版配置）
    this.normalchance = this.config.getInt("chance.normal");
    this.luckchance = this.config.getInt("chance.luck");
    this.safechance = this.config.getInt("chance.safe");
    this.vipchance = this.config.getInt("chance.vip");
    this.directUpgradeChance = this.config.getInt("chance.direct_upgrade", 80); // 直升符咒成功几率，默认80%

    // 输出调试信息
    if (this.config.getBoolean("style.debug_logs", false)) {
      logToConsole("初始化强化石加成值 - 普通: " + this.normalchance + ", 幸运: " + this.luckchance +
          ", 安全: " + this.safechance + ", VIP: " + this.vipchance);

      // 检查是否有范围配置
      if (this.config.contains("chance.normal_ranges")) {
        logToConsole("检测到普通强化石范围配置:");
        for (String range : this.config.getConfigurationSection("chance.normal_ranges").getKeys(false)) {
          int bonus = this.config.getInt("chance.normal_ranges." + range);
          logToConsole("  范围 " + range + ": 加成 " + bonus);
        }
      }

      if (this.config.contains("chance.luck_ranges")) {
        logToConsole("检测到幸运强化石范围配置:");
        for (String range : this.config.getConfigurationSection("chance.luck_ranges").getKeys(false)) {
          int bonus = this.config.getInt("chance.luck_ranges." + range);
          logToConsole("  范围 " + range + ": 加成 " + bonus);
        }
      }

      if (this.config.contains("chance.safe_ranges")) {
        logToConsole("检测到安全强化石范围配置:");
        for (String range : this.config.getConfigurationSection("chance.safe_ranges").getKeys(false)) {
          int bonus = this.config.getInt("chance.safe_ranges." + range);
          logToConsole("  范围 " + range + ": 加成 " + bonus);
        }
      }

      if (this.config.contains("chance.vip_ranges")) {
        logToConsole("检测到VIP强化石范围配置:");
        for (String range : this.config.getConfigurationSection("chance.vip_ranges").getKeys(false)) {
          int bonus = this.config.getInt("chance.vip_ranges." + range);
          logToConsole("  范围 " + range + ": 加成 " + bonus);
        }
      }
    }

    // 检查是否有消息设置
    if (!this.config.contains("messages.show_congratulation_on_failure")) {
      // 如果没有，添加默认设置
      this.config.set("messages.show_congratulation_on_failure", false);
      this.saveConfig();
      System.out.println("已添加新配置项: messages.show_congratulation_on_failure = false");
    }

    // 加载装备最高强化等级限制
    loadMaxEnhancementLevels();
  }

  /**
   * 加载装备最高强化等级限制
   */
  private void loadMaxEnhancementLevels() {
    // 清空现有数据
    maxEnhancementLevels.clear();

    // 从配置文件加载数据
    if (this.config.contains("max_enhancement_levels")) {
      for (String itemIdStr : this.config.getConfigurationSection("max_enhancement_levels").getKeys(false)) {
        try {
          int itemId = Integer.parseInt(itemIdStr);
          int maxLevel = this.config.getInt("max_enhancement_levels." + itemIdStr);
          maxEnhancementLevels.put(itemId, maxLevel);

          if (this.config.getBoolean("style.debug_logs", false)) {
            System.out.println("加载装备最高强化等级限制 - 物品ID: " + itemId + ", 最高等级: " + maxLevel);
          }
        } catch (NumberFormatException e) {
          System.out.println("加载装备最高强化等级限制出错 - 无效的物品ID: " + itemIdStr);
        }
      }
    }
  }

  public List<String> getLore(ItemStack item) {
    List<String> lore = item.getItemMeta().getLore();
    return lore;
  }

  public List<String> setLore(ItemStack item, int level) {
    List<String> lore = this.getLore(item);
    if (lore == null) {
      lore = new ArrayList<String>();
      lore.add(this.get(13));
      lore.add(this.get(14).replace("{0}", String.valueOf(level)));
      lore.add(this.style(level));
      return lore;
    }
    boolean use = true;
    int size = 0;
    while (size < lore.size()) {
      if (lore.get(size).indexOf("\u5f3a\u5316\u4fe1\u606f") > -1) {
        lore.set(size + 1, this.get(14).replace("{0}", String.valueOf(level)));
        lore.set(size + 2, this.style(level));
        use = false;
        break;
      }
      ++size;
    }
    if (use) {
      lore.add(this.get(13));
      lore.add(this.get(14).replace("{0}", String.valueOf(level)));
      lore.add(this.style(level));
    }
    return lore;
  }

  public static List<String> getBlockId() {
    return BlockId;
  }

  public static List<String> getMobId() {
    return MobId;
  }

  /**
   * 检查字符串是否是数字
   *
   * @param str 要检查的字符串
   * @return 如果字符串是数字，返回true；否则返回false
   */
  private boolean isNumeric(String str) {
    if (str == null || str.isEmpty()) {
      return false;
    }
    for (char c : str.toCharArray()) {
      if (!Character.isDigit(c)) {
        return false;
      }
    }
    return true;
  }

  public String style(int level) {
    String str = "";

    // 获取符号颜色配置
    String[] symbolColors = new String[10]; // 存储每10级的颜色代码

    // 从配置文件中读取颜色设置，如果没有则使用默认值
    for (int i = 0; i < 10; i++) {
      String colorKey = "style.colors.level_" + ((i + 1) * 10);
      if (this.config.contains(colorKey)) {
        String colorCode = this.config.getString(colorKey);
        symbolColors[i] = colorCode;
        if (this.config.getBoolean("style.debug_logs", false)) {
          System.out.println("从配置文件加载颜色 - 等级 " + ((i + 1) * 10) + ": " + colorCode);
        }
      } else {
        // 默认颜色设置
        switch (i) {
          case 0:
            symbolColors[i] = "§a";
            break; // 1-10级：绿色
          case 1:
            symbolColors[i] = "§b";
            break; // 11-20级：青色
          case 2:
            symbolColors[i] = "§e";
            break; // 21-30级：黄色
          case 3:
            symbolColors[i] = "§6";
            break; // 31-40级：金色
          case 4:
            symbolColors[i] = "§c";
            break; // 41-50级：红色
          case 5:
            symbolColors[i] = "§d";
            break; // 51-60级：粉色
          case 6:
            symbolColors[i] = "§5";
            break; // 61-70级：紫色
          case 7:
            symbolColors[i] = "§9";
            break; // 71-80级：蓝色
          case 8:
            symbolColors[i] = "§1";
            break; // 81-90级：深蓝色
          case 9:
            symbolColors[i] = "§4";
            break; // 91-100级：深红色
          default:
            symbolColors[i] = "§f";
            break; // 默认白色
        }
        if (this.config.getBoolean("style.debug_logs", false)) {
          System.out.println("使用默认颜色 - 等级 " + ((i + 1) * 10) + ": " + symbolColors[i]);
        }
      }
    }

    // 确定当前等级使用哪个颜色
    int colorIndex = (level - 1) / 10;
    if (colorIndex < 0)
      colorIndex = 0;
    if (colorIndex >= symbolColors.length)
      colorIndex = symbolColors.length - 1;

    // 调试信息
    if (this.config.getBoolean("style.debug_logs", false)) {
      System.out.println("等级: " + level + ", 颜色索引: " + colorIndex + ", 颜色: " + symbolColors[colorIndex]);
    }

    String currentColor = symbolColors[colorIndex];

    // 获取符号，移除可能存在的颜色代码
    String filledSymbol = this.cg;
    String emptySymbol = this.sb;

    // 确保符号不为null
    if (filledSymbol == null) {
      filledSymbol = "§c§l◆";
    }
    if (emptySymbol == null) {
      emptySymbol = "§7§l◇";
    }

    // 移除颜色代码，只保留符号本身
    filledSymbol = filledSymbol.replaceAll("§[0-9a-fk-or]", "");
    emptySymbol = emptySymbol.replaceAll("§[0-9a-fk-or]", "");

    // 添加粗体效果
    String formattedSymbol = "§l" + filledSymbol;

    // 计算当前10级内的等级（1-10）
    int levelWithin10 = ((level - 1) % 10) + 1;

    // 调试信息
    if (this.config.getBoolean("style.debug_logs", false)) {
      System.out.println("等级: " + level + ", 10级内的等级: " + levelWithin10);
    }

    // 处理所有等级，每10级重新开始计数
    for (int i = 0; i < levelWithin10; i++) {
      str = String.valueOf(str) + formattedSymbol;
    }

    // 添加空符号，使总数为10
    for (int i = 0; i < 10 - levelWithin10; i++) {
      str = String.valueOf(str) + emptySymbol;
    }

    String result = currentColor + str;
    if (this.config.getBoolean("style.debug_logs", false)) {
      System.out.println("等级: " + level + ", 返回的符号字符串: " + result);
    }
    return result;
  }

  /**
   * 强化装备的核心方法
   *
   * @param hash           熔炉的哈希值
   * @param itemStack      要强化的装备
   * @param rand           成功几率
   * @param Leve           目标等级（直升符咒或突破石的等级）
   * @param isBreakthrough 是否是突破石（true为突破石，false为直升符咒或普通强化）
   * @return 强化后的装备，如果返回null表示强化失败（达到最高等级限制）
   */
  public ItemStack getqh(int hash, ItemStack itemStack, int rand, int Leve, boolean isBreakthrough) {
    Player p = this.player.get(hash);
    ItemStack ItemD = new ItemStack(itemStack);
    ItemD.setAmount(1);
    ItemMeta ItemM = ItemD.getItemMeta();
    int itemId = ItemD.getTypeId();
    Enchantment enchan = this.enchan.getEnchan(this.config.getInt("id.items." + itemId));
    int WqLeve = ItemD.getEnchantmentLevel(enchan);

    // 检查装备是否有最高强化等级限制
    if (maxEnhancementLevels.containsKey(itemId)) {
      int maxLevel = maxEnhancementLevels.get(itemId);

      // 检查当前等级是否已经达到或超过最高限制
      if (WqLeve >= maxLevel) {
        this.show(p, "§e【§c强化系统§e】 §c此装备已达到最高强化等级 " + maxLevel + " 级，无法继续强化");
        return null; // 返回null表示强化失败
      }

      // 如果是直升符咒或突破石，检查目标等级是否超过最高限制
      if (Leve > 0 && Leve > maxLevel) {
        this.show(p, "§e【§c强化系统§e】 §c此装备最高只能强化到 " + maxLevel + " 级");
        return null; // 返回null表示强化失败
      }

      // 如果是普通强化，检查当前等级+1是否超过最高限制
      if (Leve <= 0 && WqLeve + 1 > maxLevel) {
        this.show(p, "§e【§c强化系统§e】 §c此装备最高只能强化到 " + maxLevel + " 级");
        return null; // 返回null表示强化失败
      }
    }

    // 如果几率是100%，直接成功
    if (rand == 100) {
      // 如果是直升符咒或突破石，直接设置为指定等级
      if (Leve > 0) {
        WqLeve = Leve;
        // 不在这里发送广播消息，由调用方处理
      } else {
        // 普通强化，等级+1
        WqLeve++;
        // 显示给玩家的消息，包含当前装备等级
        this.show(p, this.get(9).replace("{0}", String.valueOf(WqLeve)));
        // 广播消息
        String broadcastMessage = getBroadcastMessage("enhancement_success", p.getName(), WqLeve);
        this.getServer().broadcastMessage(broadcastMessage);
      }
    } else {
      // 如果几率不是100%，进行随机判断
      Random r = new Random();
      if (r.nextInt(100) < rand) {
        // 如果是直升符咒或突破石，直接设置为指定等级
        if (Leve > 0) {
          WqLeve = Leve;
          // 不在这里发送广播消息，由调用方处理
        } else {
          // 普通强化，等级+1
          WqLeve++;
          // 显示给玩家的消息，包含当前装备等级
          this.show(p, this.get(9).replace("{0}", String.valueOf(WqLeve)));

          // 检查是否应该广播成功消息
          boolean shouldBroadcast = this.shouldBroadcast("broadcast.enhancement_levels", WqLeve);

          // 只有在配置允许的情况下才广播消息
          if (shouldBroadcast) {
            String broadcastMessage = getBroadcastMessage("enhancement_success", p.getName(), WqLeve);
            this.getServer().broadcastMessage(broadcastMessage);
          }
        }
      } else if (r.nextInt(100) < 70) {
        if (Leve < 1) {
          this.show(p, this.get(10));
          --WqLeve;
        } else {
          this.show(p, this.get(16));
        }
      }
    }
    ItemM.setLore(this.setLore(ItemD, WqLeve));
    ItemD.setItemMeta(ItemM);
    if (WqLeve > 0) {
      ItemD.addUnsafeEnchantment(enchan, WqLeve);
    } else {
      ItemD.removeEnchantment(enchan);
    }
    return ItemD;
  }

  public ItemStack qh(int hash, ItemStack itemStack, boolean safe, boolean admin, int id, int chance) {
    Player p = this.player.get(hash);
    ItemStack item = itemStack;
    Enchantment enchan = this.enchan.getEnchan(this.config.getInt("id.items." + id));
    int currentLevel = item.getEnchantmentLevel(enchan);
    int level = currentLevel + 1;

    // 检查装备是否有最高强化等级限制
    if (maxEnhancementLevels.containsKey(id)) {
      int maxLevel = maxEnhancementLevels.get(id);

      // 检查当前等级是否已经达到或超过最高限制
      if (!admin && currentLevel >= maxLevel) {
        this.show(p, "§e【§c强化系统§e】 §c此装备已达到最高强化等级 " + maxLevel + " 级，无法继续强化");
        return item; // 返回原物品，不进行强化
      }

      // 如果是管理员强化，不受限制
      if (!admin && level > maxLevel) {
        this.show(p, "§e【§c强化系统§e】 §c此装备最高只能强化到 " + maxLevel + " 级");
        return item; // 返回原物品，不进行强化
      }
    }

    // 移除特殊的11级处理，使用统一的强化逻辑
    if (admin) {
      // 显示成功消息，包含当前装备等级
      this.show(p, this.get(9).replace("{0}", "10"));
      item.addUnsafeEnchantment(enchan, 10);
      ItemMeta im = item.getItemMeta();
      im.setLore(this.setLore(item, 10));
      item.setItemMeta(im);
      return item;
    }
    if (this.Rdom(level, chance)) {
      item.addUnsafeEnchantment(enchan, level);
      // 显示成功消息，包含当前装备等级
      this.show(p, this.get(9).replace("{0}", String.valueOf(level)));
    } else {
      if (level == 0) {
        item.addUnsafeEnchantment(enchan, level);
        // 显示成功消息，包含当前装备等级
        this.show(p, this.get(9).replace("{0}", String.valueOf(level)));
        ItemMeta im = item.getItemMeta();
        im.setLore(this.setLore(item, level));
        item.setItemMeta(im);
        return item;
      }
      this.show(p, this.get(10));
      if ((level -= 2) > 6 && !safe) {
        // 装备自毁时显示明确的提示信息
        this.show(p, this.get(22));
        // 广播装备自毁消息
        this.getServer().broadcastMessage(this.get(23).replace("{0}", p.getName()));
        return new ItemStack(0);
      }
      if (level < 1) {
        item.removeEnchantment(enchan);
      } else {
        item.addUnsafeEnchantment(enchan, level);
      }
    }
    // 检查是否应该广播成功消息
    boolean shouldBroadcast = this.shouldBroadcast("broadcast.enhancement_levels", level);

    // 只有在配置允许的情况下才广播消息
    if (shouldBroadcast) {
      String broadcastMessage = getBroadcastMessage("enhancement_success", p.getName(), level);
      this.getServer().broadcastMessage(broadcastMessage);
    }
    ItemMeta im = item.getItemMeta();
    im.setLore(this.setLore(item, level));
    item.setItemMeta(im);
    return item;
  }

  public boolean furnCheck(Furnace furn) {
    boolean flag = false;
    Player p = this.player.get(furn.getBlock().hashCode());
    ItemStack smelt = furn.getInventory().getSmelting();
    ItemStack fuel = furn.getInventory().getFuel();
    boolean use = false;

    // 调试信息
    logToConsole("furnCheck方法被调用");
    if (p != null) {
      logToConsole("关联玩家: " + p.getName());
    } else {
      logToConsole("没有关联玩家");
    }

    if (smelt != null) {
      logToConsole(
          "装备: " + smelt.getType() + " 耐久度: " + smelt.getDurability() + "/" + smelt.getType().getMaxDurability());
      if (smelt.hasItemMeta()) {
        logToConsole("装备有元数据");
        if (smelt.getItemMeta().hasDisplayName()) {
          logToConsole("装备名称: " + smelt.getItemMeta().getDisplayName());
        }
        if (smelt.getItemMeta().hasLore()) {
          logToConsole("装备Lore: " + smelt.getItemMeta().getLore());
        }
        if (smelt.getItemMeta().hasEnchants()) {
          logToConsole("装备附魔: " + smelt.getItemMeta().getEnchants());
        }
      }
    } else {
      logToConsole("装备为空");
    }

    // 如果燃料不是强化宝石，让原版熔炉正常工作
    if (fuel == null || !fuel.hasItemMeta() || !fuel.getItemMeta().hasDisplayName()) {
      logToConsole("燃料不是强化宝石，让原版熔炉正常工作");
      return false; // 不是强化宝石，让Minecraft正常处理
    }

    String displayName = fuel.getItemMeta().getDisplayName();
    logToConsole("燃料名称: " + displayName);

    // 检查是否是强化系统的物品
    boolean isEnhancementItem = false;

    // 检查是否是强化石
    if (fuel.getItemMeta().hasDisplayName() &&
        (fuel.getItemMeta().getDisplayName().equals(this.normalItem.getItemMeta().getDisplayName()) ||
            fuel.getItemMeta().getDisplayName().equals(this.luckItem.getItemMeta().getDisplayName()) ||
            fuel.getItemMeta().getDisplayName().equals(this.safeItem.getItemMeta().getDisplayName()) ||
            fuel.getItemMeta().getDisplayName().equals(this.vipItem.getItemMeta().getDisplayName()))) {
      isEnhancementItem = true;
    }

    // 检查是否是强化突破石（精确匹配，避免与淬炼系统冲突）
    if (displayName.contains("强化突破石") && !displayName.contains("淬炼")) {
      isEnhancementItem = true;
      use = true;
    }

    // 检查是否是强化直升符咒（精确匹配，避免与淬炼系统冲突）
    if (displayName.contains("直升符咒") && !displayName.contains("淬炼")) {
      isEnhancementItem = true;
    }

    // 检查是否是强化棒（精确匹配，避免与淬炼系统冲突）
    if (displayName.contains("强化棒") && !displayName.contains("淬炼")) {
      isEnhancementItem = true;
      use = true;
    }

    // 检查是否是管理员强化石
    if (fuel.getItemMeta().equals(this.adminItem.getItemMeta())) {
      isEnhancementItem = true;
    }

    // 如果不是强化系统的物品，让原版熔炉正常工作
    if (!isEnhancementItem) {
      System.out.println("燃料不是强化系统的物品，让原版熔炉正常工作");
      return false; // 不是强化系统的物品，让Minecraft正常处理
    }

    // 检查装备是否满耐久
    if (smelt != null && smelt.getType() != Material.AIR && smelt.getType().getMaxDurability() > 0) {
      if (smelt.getDurability() > 0) {
        if (p != null) {
          p.sendMessage("§e【§c强化系统§e】 §c只能强化满耐久的装备");
          System.out.println("装备耐久度不满，无法强化");
        }
        return false;
      }
    }

    // 检查是否是直升符咒
    if (displayName.contains("强化直升符咒")) {
      // 提取直升符咒等级
      String levelStr = "";
      if (displayName.contains("§l")) {
        String[] parts = displayName.split("§l");
        if (parts.length > 1) {
          levelStr = parts[1];
        }
      }

      try {
        int directLevel = Integer.parseInt(levelStr);

        // 检查装备是否满足直升符咒的等级要求
        ItemStack smeltItem = furn.getInventory().getSmelting();
        if (smeltItem != null && smeltItem.getType() != Material.AIR) {
          int currentLevel = smeltItem.getEnchantmentLevel(
              this.enchan.getEnchan(this.config.getInt("id.items." + smeltItem.getTypeId())));

          // 获取物品ID，用于检查最高强化等级限制
          int itemId = smeltItem.getTypeId();

          // 检查装备是否有最高强化等级限制
          if (maxEnhancementLevels.containsKey(itemId)) {
            int maxLevel = maxEnhancementLevels.get(itemId);

            // 检查两种情况：
            // 1. 直升符咒等级超过了装备的最高强化等级限制
            // 2. 当前装备等级已经达到或超过了最高强化等级限制
            if (directLevel > maxLevel || currentLevel >= maxLevel) {
              // 显示不同的错误消息，取决于是哪种情况
              if (currentLevel >= maxLevel) {
                p.sendMessage("§e【§c强化系统§e】 §c此装备已达到最高强化等级 " + maxLevel + " 级，无法继续强化");
              } else {
                p.sendMessage("§e【§c强化系统§e】 §c此装备最高只能强化到 " + maxLevel + " 级");
              }

              // 如果熔炉正在燃烧，重置熔炉状态
              if (furn.getBurnTime() > 0) {
                furn.setBurnTime((short) 0);
                furn.setCookTime((short) 0);
                furn.update();
              }

              // 返还直升符咒给玩家 - 确保正确克隆
              ItemStack fuelCopy = furn.getInventory().getFuel().clone();
              furn.getInventory().setFuel(null);
              p.getInventory().addItem(fuelCopy);
              p.sendMessage("§e【§c强化系统§e】 §a直升符咒已返还");

              return false;
            }
          }

          // 从配置文件获取等级要求
          int requiredLevel = 0;
          if (this.config.contains("items.direct_upgrade.level_requirements." + directLevel)) {
            requiredLevel = this.config.getInt("items.direct_upgrade.level_requirements." + directLevel);
          } else {
            // 如果没有特定等级的配置，使用默认规则
            if (directLevel == 29)
              requiredLevel = 20;
            else if (directLevel == 49)
              requiredLevel = 29;
            else if (directLevel == 69)
              requiredLevel = 49;
            else if (directLevel == 99)
              requiredLevel = 69;
            else
              requiredLevel = Math.max(1, directLevel - 10); // 默认需要前10级
          }

          // 检查是否满足等级要求
          if (currentLevel < requiredLevel) {
            // 不满足要求，显示错误消息
            String errorMessage = "§c装备等级必须达到" + requiredLevel + "级才能使用" + directLevel + "级直升符咒";

            p.sendMessage("§e【§c强化系统§e】 " + errorMessage);

            // 如果熔炉正在燃烧，重置熔炉状态
            if (furn.getBurnTime() > 0) {
              furn.setBurnTime((short) 0);
              furn.setCookTime((short) 0);
              furn.update();
            }

            // 返还直升符咒给玩家
            ItemStack fuelCopy = furn.getInventory().getFuel().clone();
            furn.getInventory().setFuel(null);
            p.getInventory().addItem(fuelCopy);
            p.sendMessage("§e【§c强化系统§e】 §a直升符咒已返还");

            return false;
          }
        }
      } catch (Exception e) {
        // 解析失败，忽略
      }

      use = true;
    }

    // 检查是否是强化系统的物品或普通强化石
    if (use || (fuel.hasItemMeta() && (fuel.getItemMeta().equals(this.normalItem.getItemMeta()) ||
        fuel.getItemMeta().equals(this.luckItem.getItemMeta()) ||
        fuel.getItemMeta().equals(this.safeItem.getItemMeta()) ||
        fuel.getItemMeta().equals(this.vipItem.getItemMeta()) ||
        fuel.getItemMeta().equals(this.adminItem.getItemMeta())))) {
      int leve = smelt.getEnchantmentLevel(this.enchan.getEnchan(this.config.getInt("id.items." + smelt.getTypeId())));
      int Pleve = fuel.getEnchantmentLevel(this.enchan.getEnchan(34));

      // 获取配置中的普通强化石最高等级
      int maxNormalLevel = this.config.getInt("breakthrough.max_normal_level", 29);

      // 检查是否已达到最高等级 (只对普通宝石进行检查，直升符咒和突破石不受限制)
      if (!use &&
          (fuel.getItemMeta().equals(this.normalItem.getItemMeta()) ||
              fuel.getItemMeta().equals(this.luckItem.getItemMeta()) ||
              fuel.getItemMeta().equals(this.safeItem.getItemMeta()) ||
              fuel.getItemMeta().equals(this.vipItem.getItemMeta()))) {

        // 获取所有突破等级点
        List<Integer> breakthroughLevels = new ArrayList<>();

        // 从配置文件中读取所有突破等级
        if (this.config.contains("breakthrough.levels")) {
          for (String key : this.config.getConfigurationSection("breakthrough.levels").getKeys(false)) {
            try {
              int level = Integer.parseInt(key);
              breakthroughLevels.add(level);
            } catch (NumberFormatException e) {
              // 忽略非数字的键
            }
          }
        }

        // 如果配置文件中没有设置，则使用默认值
        if (breakthroughLevels.isEmpty()) {
          breakthroughLevels.add(30);
          breakthroughLevels.add(50);
          breakthroughLevels.add(70);
          breakthroughLevels.add(100);
        }

        // 对突破等级点进行排序
        Collections.sort(breakthroughLevels);

        // 找到下一个需要突破的等级点
        int nextBreakthroughLevel = -1;
        for (int level : breakthroughLevels) {
          if (level > leve) {
            nextBreakthroughLevel = level;
            break;
          }
        }

        // 检查是否达到下一个突破点的前一级
        if (nextBreakthroughLevel > 0 && leve >= nextBreakthroughLevel - 1) {
          this.show(p, "§e【§c强化系统§e】 §c装备已达到" + leve + "级，需要使用" + nextBreakthroughLevel + "级突破石才能继续强化");

          // 如果熔炉正在燃烧，重置熔炉状态
          if (furn.getBurnTime() > 0) {
            furn.setBurnTime((short) 0);
            furn.setCookTime((short) 0);
            furn.update();
          }

          // 返还宝石给玩家
          ItemStack fuelCopy = furn.getInventory().getFuel().clone();
          furn.getInventory().setFuel(null);
          p.getInventory().addItem(fuelCopy);
          p.sendMessage("§e【§c强化系统§e】 §a强化石已返还");

          return false;
        }

        // 检查是否达到10级（原有的检查），但只有当maxNormalLevel为10时才检查
        // 这样当maxNormalLevel为29时，10-28级的装备都可以继续强化
        if (maxNormalLevel == 10 && leve >= 10) {
          this.show(p, this.get(11));

          // 如果熔炉正在燃烧，重置熔炉状态
          if (furn.getBurnTime() > 0) {
            furn.setBurnTime((short) 0);
            furn.setCookTime((short) 0);
            furn.update();
          }

          // 返还宝石给玩家
          ItemStack fuelCopy = furn.getInventory().getFuel().clone();
          furn.getInventory().setFuel(null);
          p.getInventory().addItem(fuelCopy);
          p.sendMessage("§e【§c强化系统§e】 §a装备已达到最高等级，强化石已返还");

          return false;
        }
      }
      // 检查突破石是否满足等级要求
      if (use && fuel.getItemMeta().hasDisplayName() && fuel.getItemMeta().getDisplayName().contains("强化突破石")) {
        // 获取突破石等级 - 使用锋利附魔(Enchantment.DAMAGE_ALL)
        int breakthroughLevel = fuel.getItemMeta().hasEnchant(Enchantment.DAMAGE_ALL)
            ? fuel.getItemMeta().getEnchantLevel(Enchantment.DAMAGE_ALL)
            : 0;

        // 从配置文件获取突破石需要的装备等级
        int requiredLevel = 0;

        // 检查配置文件中是否有该等级的设置
        if (this.config.contains("breakthrough.levels." + breakthroughLevel)) {
          requiredLevel = this.config.getInt("breakthrough.levels." + breakthroughLevel);
        } else {
          // 如果配置文件中没有设置，则使用默认规则：需要前一级
          requiredLevel = breakthroughLevel - 1;
        }

        // 检查装备等级是否满足要求
        if (leve < requiredLevel) {
          // 不满足要求，显示错误消息
          String errorMessage = "§c装备等级必须达到" + requiredLevel + "级才能使用" + breakthroughLevel + "级突破石";
          p.sendMessage("§e【§c强化系统§e】 " + errorMessage);

          // 如果熔炉正在燃烧，重置熔炉状态
          if (furn.getBurnTime() > 0) {
            furn.setBurnTime((short) 0);
            furn.setCookTime((short) 0);
            furn.update();
          }

          // 返还突破石给玩家
          ItemStack fuelCopy = furn.getInventory().getFuel().clone();
          furn.getInventory().setFuel(null);
          p.getInventory().addItem(fuelCopy);
          p.sendMessage("§e【§c强化系统§e】 §a突破石已返还");

          return false;
        }
      }
      for (String i : this.config.getConfigurationSection("id.items").getKeys(false)) {
        int a = Integer.parseInt(i);
        if (a != smelt.getTypeId() || smelt.getAmount() != 1)
          continue;
        flag = true;
        break;
      }
      if (flag) {
        int hash = furn.getBlock().hashCode();
        this.fuelItem.put(hash, fuel);
        return true;
      }
      return false;
    }
    return false;
  }

  @EventHandler
  public synchronized void onPlayerInteract(PlayerInteractEvent e) {
    // 右键点击熔炉，记录玩家
    if (e.getAction().equals((Object) Action.RIGHT_CLICK_BLOCK) && e.hasBlock()
        && e.getClickedBlock().getType().equals((Object) Material.FURNACE)) {
      Player p = e.getPlayer();
      int hash = e.getClickedBlock().hashCode();
      if (this.player.get(hash) != null) {
        this.player.remove(hash);
        this.player.put(hash, p);
        return;
      }
      this.player.put(hash, p);
      return;
    }

    // Shift+左键点击装备，自动添加到熔炉
    if (e.getAction().equals((Object) Action.LEFT_CLICK_BLOCK) && e.hasBlock()
        && e.getClickedBlock().getType().equals((Object) Material.FURNACE) && e.getPlayer().isSneaking()) {
      Player p = e.getPlayer();
      ItemStack handItem = p.getItemInHand();

      // 检查手中物品是否为可强化装备
      if (handItem != null && handItem.getType() != Material.AIR) {
        boolean isEnhanceable = false;
        for (String i : this.config.getConfigurationSection("id.items").getKeys(false)) {
          int a = Integer.parseInt(i);
          if (a == handItem.getTypeId()) {
            isEnhanceable = true;
            break;
          }
        }

        if (isEnhanceable) {
          // 获取熔炉
          Furnace furnace = (Furnace) e.getClickedBlock().getState();
          FurnaceInventory furnaceInv = furnace.getInventory();

          // 检查熔炉上方是否为空
          if (furnaceInv.getSmelting() == null || furnaceInv.getSmelting().getType() == Material.AIR) {
            // 检查装备是否满耐久
            if (handItem.getType().getMaxDurability() > 0 && handItem.getDurability() > 0) {
              p.sendMessage("§e【§c强化系统§e】 §c只能强化满耐久的装备");
              return;
            }

            // 将装备放入熔炉上方
            ItemStack itemToPlace = handItem.clone();
            itemToPlace.setAmount(1);
            furnaceInv.setSmelting(itemToPlace);

            // 如果玩家不是创造模式，减少手中物品数量
            if (p.getGameMode() != org.bukkit.GameMode.CREATIVE) {
              if (handItem.getAmount() > 1) {
                handItem.setAmount(handItem.getAmount() - 1);
                p.setItemInHand(handItem);
              } else {
                p.setItemInHand(null);
              }
            }

            // 记录玩家
            int hash = e.getClickedBlock().hashCode();
            if (this.player.get(hash) != null) {
              this.player.remove(hash);
            }
            this.player.put(hash, p);

            // 发送消息
            p.sendMessage("§e【§c强化系统§e】 §a装备已放入熔炉");

            // 阻止默认事件
            e.setCancelled(true);
          } else {
            p.sendMessage("§e【§c强化系统§e】 §c熔炉上方已有物品");
          }
        }
      }
    }
  }

  @EventHandler
  public void onFurnaceBurn(FurnaceBurnEvent e) {
    // 获取熔炉
    Furnace furnace = (Furnace) e.getBlock().getState();

    // 检查燃料和装备
    ItemStack fuel = furnace.getInventory().getFuel();
    ItemStack smelt = furnace.getInventory().getSmelting();

    // 调试信息
    logToConsole("熔炉燃烧事件触发");
    if (fuel != null) {
      logToConsole("燃料: " + fuel.getType()
          + (fuel.hasItemMeta() && fuel.getItemMeta().hasDisplayName() ? " 名称: " + fuel.getItemMeta().getDisplayName()
              : ""));
    } else {
      logToConsole("燃料为空");
    }

    if (smelt != null) {
      logToConsole(
          "装备: " + smelt.getType() + " 耐久度: " + smelt.getDurability() + "/" + smelt.getType().getMaxDurability());
      if (smelt.hasItemMeta()) {
        logToConsole("装备有元数据");
        if (smelt.getItemMeta().hasDisplayName()) {
          logToConsole("装备名称: " + smelt.getItemMeta().getDisplayName());
        }
        if (smelt.getItemMeta().hasLore()) {
          logToConsole("装备Lore: " + smelt.getItemMeta().getLore());
        }
        if (smelt.getItemMeta().hasEnchants()) {
          logToConsole("装备附魔: " + smelt.getItemMeta().getEnchants());
        }
      }
    } else {
      logToConsole("装备为空");
    }

    // 如果燃料不是强化宝石，让原版熔炉正常工作
    if (fuel == null || !fuel.hasItemMeta() || !fuel.getItemMeta().hasDisplayName()) {
      logToConsole("燃料不是强化宝石，让原版熔炉正常工作");
      return; // 不是强化宝石，让Minecraft正常处理
    }

    // 检查装备是否满耐久
    if (smelt != null && smelt.getType() != Material.AIR && smelt.getType().getMaxDurability() > 0) {
      if (smelt.getDurability() > 0) {
        // 获取熔炉哈希值，用于获取玩家
        int hash = e.getBlock().hashCode();
        Player p = this.player.get(hash);

        if (p != null) {
          p.sendMessage("§e【§c强化系统§e】 §c只能强化满耐久的装备");
          logToConsole("装备耐久度不满，无法强化");
        }

        // 取消燃烧事件
        e.setCancelled(true);
        return;
      }
    }

    // 检查是否是强化系统的物品
    boolean isEnhancementItem = false;

    // 检查是否是强化石
    if (fuel.getItemMeta().getDisplayName().equals(this.normalItem.getItemMeta().getDisplayName()) ||
        fuel.getItemMeta().getDisplayName().equals(this.luckItem.getItemMeta().getDisplayName()) ||
        fuel.getItemMeta().getDisplayName().equals(this.safeItem.getItemMeta().getDisplayName()) ||
        fuel.getItemMeta().getDisplayName().equals(this.vipItem.getItemMeta().getDisplayName())) {
      isEnhancementItem = true;
      logToConsole("燃料是强化石");
    }

    // 检查是否是强化突破石（精确匹配，避免与淬炼系统冲突）
    if (fuel.getItemMeta().getDisplayName().contains("强化突破石") && !fuel.getItemMeta().getDisplayName().contains("淬炼")) {
      isEnhancementItem = true;
      logToConsole("燃料是强化突破石");
    }

    // 检查是否是强化直升符咒（精确匹配，避免与淬炼系统冲突）
    if (fuel.getItemMeta().getDisplayName().contains("直升符咒") && !fuel.getItemMeta().getDisplayName().contains("淬炼")) {
      isEnhancementItem = true;
      logToConsole("燃料是强化直升符咒");
    }

    // 检查是否是强化棒（精确匹配，避免与淬炼系统冲突）
    if (fuel.getItemMeta().getDisplayName().contains("强化棒") && !fuel.getItemMeta().getDisplayName().contains("淬炼")) {
      isEnhancementItem = true;
      logToConsole("燃料是强化棒");
    }

    // 如果不是强化系统的物品，让原版熔炉正常工作
    if (!isEnhancementItem) {
      logToConsole("燃料不是强化系统的物品，让原版熔炉正常工作");
      return; // 不是强化系统的物品，让Minecraft正常处理
    }

    // 如果是强化系统的物品，并且上方有装备，继续处理
    if (smelt != null && smelt.getType() != Material.AIR) {
      // 获取熔炉哈希值，用于获取玩家
      int hash = e.getBlock().hashCode();
      Player p = this.player.get(hash);

      // 调试信息
      if (p != null) {
        logToConsole("关联玩家: " + p.getName());
      } else {
        logToConsole("没有关联玩家");
      }

      // 检查装备是否可以强化
      boolean isEnhanceable = false;
      int itemId = -1;
      for (String i : this.config.getConfigurationSection("id.items").getKeys(false)) {
        itemId = Integer.parseInt(i);
        if (itemId == smelt.getTypeId()) {
          isEnhanceable = true;
          break;
        }
      }

      if (isEnhanceable) {
        // 获取装备当前等级
        Enchantment enchan = this.enchan.getEnchan(this.config.getInt("id.items." + itemId));
        int currentLevel = smelt.getEnchantmentLevel(enchan);

        // 检查是否是直升符咒
        if (fuel.getItemMeta().hasDisplayName() && fuel.getItemMeta().getDisplayName().contains("强化直升符咒")) {
          // 从名称中提取等级
          String levelStr = "";
          String name = fuel.getItemMeta().getDisplayName();

          // 尝试从名称中提取数字
          if (name.contains("-")) {
            String[] parts = name.split("-");
            if (parts.length > 1) {
              levelStr = parts[1].trim();
            }
          }

          try {
            int directLevel = Integer.parseInt(levelStr);

            // 检查装备是否有最高强化等级限制
            if (maxEnhancementLevels.containsKey(itemId)) {
              int maxLevel = maxEnhancementLevels.get(itemId);

              // 检查两种情况：
              // 1. 直升符咒等级超过了装备的最高强化等级限制
              // 2. 当前装备等级已经达到或超过了最高强化等级限制
              if (directLevel > maxLevel || currentLevel >= maxLevel) {
                // 取消燃烧并返还直升符咒
                if (p != null) {
                  // 显示不同的错误消息，取决于是哪种情况
                  if (currentLevel >= maxLevel) {
                    p.sendMessage("§e【§c强化系统§e】 §c此装备已达到最高强化等级 " + maxLevel + " 级，无法继续强化");
                  } else {
                    p.sendMessage("§e【§c强化系统§e】 §c此装备最高只能强化到 " + maxLevel + " 级");
                  }

                  // 返还直升符咒给玩家 - 确保正确克隆
                  ItemStack fuelCopy = fuel.clone();
                  p.getInventory().addItem(fuelCopy);
                  p.sendMessage("§e【§c强化系统§e】 §a直升符咒已返还");

                  // 移除熔炉中的燃料
                  furnace.getInventory().setFuel(null);
                  furnace.update();
                }

                // 取消燃烧
                e.setCancelled(true);
                return;
              }
            }

            // 检查直升符咒的成功几率，如果不是100%，需要考虑失败的情况
            // 如果失败后装备等级会超过最高限制，也不允许强化
            if (this.directUpgradeChance < 100 && maxEnhancementLevels.containsKey(itemId)) {
              int maxLevel = maxEnhancementLevels.get(itemId);

              // 如果当前等级已经接近最高限制，直升失败后可能会导致装备无法继续强化
              // 例如：当前29级，最高限制30级，直升到50级失败后变成30级，就无法继续强化了
              if (currentLevel + 1 >= maxLevel) {
                if (p != null) {
                  p.sendMessage("§e【§c强化系统§e】 §c当前装备等级已接近最高限制，无法使用直升符咒");
                  p.sendMessage("§e【§c强化系统§e】 §c此装备最高只能强化到 " + maxLevel + " 级");

                  // 返还直升符咒给玩家
                  ItemStack fuelCopy = fuel.clone();
                  p.getInventory().addItem(fuelCopy);
                  p.sendMessage("§e【§c强化系统§e】 §a直升符咒已返还");

                  // 移除熔炉中的燃料
                  furnace.getInventory().setFuel(null);
                  furnace.update();
                }

                // 取消燃烧
                e.setCancelled(true);
                return;
              }
            }
          } catch (NumberFormatException ex) {
            // 忽略解析错误
          }
        }
        // 检查是否是强化棒
        else if (fuel.getItemMeta().hasDisplayName() && fuel.getItemMeta().getDisplayName().contains("强化棒")) {
          // 获取强化棒的提升等级 - 使用锋利附魔(Enchantment.DAMAGE_ALL)
          int enhancementLevel = fuel.getItemMeta().hasEnchant(Enchantment.DAMAGE_ALL)
              ? fuel.getItemMeta().getEnchantLevel(Enchantment.DAMAGE_ALL)
              : 0;

          if (enhancementLevel > 0) {
            // 计算目标等级
            int targetLevel = currentLevel + enhancementLevel;

            // 获取所有突破等级点
            List<Integer> breakthroughLevels = new ArrayList<>();
            if (this.config.contains("breakthrough.levels")) {
              for (String key : this.config.getConfigurationSection("breakthrough.levels").getKeys(false)) {
                try {
                  int level = Integer.parseInt(key);
                  breakthroughLevels.add(level);
                } catch (NumberFormatException ex) {
                  // 忽略非数字的键
                }
              }
            }

            // 如果配置文件中没有设置，则使用默认值
            if (breakthroughLevels.isEmpty()) {
              breakthroughLevels.add(30);
              breakthroughLevels.add(50);
              breakthroughLevels.add(70);
              breakthroughLevels.add(100);
            }

            // 排序确保从小到大
            Collections.sort(breakthroughLevels);

            // 检查是否会超过突破点
            boolean willExceedBreakpoint = false;
            int breakpoint = -1;

            for (int level : breakthroughLevels) {
              if (currentLevel < level && targetLevel >= level) {
                willExceedBreakpoint = true;
                breakpoint = level;
                break;
              }
            }

            // 如果会超过突破点，调整目标等级
            if (willExceedBreakpoint) {
              // 获取突破点前一级
              int adjustedTargetLevel = breakpoint - 1;

              // 如果调整后的目标等级小于等于当前等级，取消燃烧
              if (adjustedTargetLevel <= currentLevel) {
                if (p != null) {
                  p.sendMessage("§e【§c强化系统§e】 §c装备已达到" + currentLevel + "级，需要使用突破石才能继续强化");

                  // 返还强化棒给玩家
                  ItemStack fuelCopy = fuel.clone();
                  p.getInventory().addItem(fuelCopy);
                  p.sendMessage("§e【§c强化系统§e】 §a强化棒已返还");

                  // 移除熔炉中的燃料
                  furnace.getInventory().setFuel(null);
                  furnace.update();
                }

                // 取消燃烧
                e.setCancelled(true);
                return;
              }

              if (p != null) {
                p.sendMessage("§e【§c强化系统§e】 §c强化棒不能突破" + breakpoint + "级，已调整为" + adjustedTargetLevel + "级");
              }
            }

            // 检查装备是否有最高强化等级限制
            if (maxEnhancementLevels.containsKey(itemId)) {
              int maxLevel = maxEnhancementLevels.get(itemId);

              // 如果目标等级超过了装备的最高强化等级限制，取消燃烧
              if (targetLevel > maxLevel) {
                if (p != null) {
                  p.sendMessage("§e【§c强化系统§e】 §c此装备最高只能强化到 " + maxLevel + " 级");

                  // 返还强化棒给玩家
                  ItemStack fuelCopy = fuel.clone();
                  p.getInventory().addItem(fuelCopy);
                  p.sendMessage("§e【§c强化系统§e】 §a强化棒已返还");

                  // 移除熔炉中的燃料
                  furnace.getInventory().setFuel(null);
                  furnace.update();
                }

                // 取消燃烧
                e.setCancelled(true);
                return;
              }
            }
          }
        }
        // 检查是否是突破石
        else if (fuel.getItemMeta().hasDisplayName() && fuel.getItemMeta().getDisplayName().contains("强化突破石")) {
          // 获取突破石等级 - 使用锋利附魔(Enchantment.DAMAGE_ALL)
          int breakthroughLevel = fuel.getItemMeta().hasEnchant(Enchantment.DAMAGE_ALL)
              ? fuel.getItemMeta().getEnchantLevel(Enchantment.DAMAGE_ALL)
              : 0;

          // 检查装备是否有最高强化等级限制
          if (maxEnhancementLevels.containsKey(itemId) && breakthroughLevel > maxEnhancementLevels.get(itemId)) {
            // 如果突破石等级超过了装备的最高强化等级限制，取消燃烧
            if (p != null) {
              p.sendMessage("§e【§c强化系统§e】 §c此装备最高只能强化到 " + maxEnhancementLevels.get(itemId) + " 级");

              // 返还突破石给玩家
              ItemStack fuelCopy = fuel.clone();
              p.getInventory().addItem(fuelCopy);
              p.sendMessage("§e【§c强化系统§e】 §a突破石已返还");

              // 移除熔炉中的燃料
              furnace.getInventory().setFuel(null);
              furnace.update();
            }

            // 取消燃烧
            e.setCancelled(true);
            return;
          }

          // 检查突破石的成功几率，如果不是100%，需要考虑失败的情况
          // 获取突破石的成功几率
          int successChance = 100; // 默认100%

          // 如果有耐久附魔，表示有自定义几率
          if (fuel.getItemMeta().hasEnchant(Enchantment.DURABILITY)) {
            successChance = fuel.getItemMeta().getEnchantLevel(Enchantment.DURABILITY);
          }

          // 如果成功几率不是100%，且装备有最高强化等级限制
          if (successChance < 100 && maxEnhancementLevels.containsKey(itemId)) {
            int maxLevel = maxEnhancementLevels.get(itemId);

            // 如果当前等级已经接近最高限制，突破失败后可能会导致装备无法继续强化
            if (currentLevel + 1 >= maxLevel) {
              if (p != null) {
                p.sendMessage("§e【§c强化系统§e】 §c当前装备等级已接近最高限制，无法使用突破石");
                p.sendMessage("§e【§c强化系统§e】 §c此装备最高只能强化到 " + maxLevel + " 级");

                // 返还突破石给玩家
                ItemStack fuelCopy = fuel.clone();
                p.getInventory().addItem(fuelCopy);
                p.sendMessage("§e【§c强化系统§e】 §a突破石已返还");

                // 移除熔炉中的燃料
                furnace.getInventory().setFuel(null);
                furnace.update();
              }

              // 取消燃烧
              e.setCancelled(true);
              return;
            }
          }
        }
        // 检查是否是普通强化石
        else if (fuel.getItemMeta().equals(this.normalItem.getItemMeta()) ||
            fuel.getItemMeta().equals(this.luckItem.getItemMeta()) ||
            fuel.getItemMeta().equals(this.safeItem.getItemMeta()) ||
            fuel.getItemMeta().equals(this.vipItem.getItemMeta())) {

          // 检查装备是否有最高强化等级限制
          if (maxEnhancementLevels.containsKey(itemId)) {
            int maxLevel = maxEnhancementLevels.get(itemId);

            // 检查两种情况：
            // 1. 当前等级+1超过了装备的最高强化等级限制
            // 2. 当前装备等级已经达到或超过了最高强化等级限制
            if (currentLevel + 1 > maxLevel || currentLevel >= maxLevel) {
              // 取消燃烧并返还强化石
              if (p != null) {
                // 显示不同的错误消息，取决于是哪种情况
                if (currentLevel >= maxLevel) {
                  p.sendMessage("§e【§c强化系统§e】 §c此装备已达到最高强化等级 " + maxLevel + " 级，无法继续强化");
                } else {
                  p.sendMessage("§e【§c强化系统§e】 §c此装备最高只能强化到 " + maxLevel + " 级");
                }

                // 返还强化石给玩家 - 确保正确克隆
                ItemStack fuelCopy = fuel.clone();
                p.getInventory().addItem(fuelCopy);
                p.sendMessage("§e【§c强化系统§e】 §a强化石已返还");

                // 移除熔炉中的燃料
                furnace.getInventory().setFuel(null);
                furnace.update();
              }

              // 取消燃烧
              e.setCancelled(true);
              return;
            }
          }
        }
      }

      // 检查是否是突破石，且不满足等级要求或已经达到/超过突破石等级
      if (fuel.getItemMeta().getDisplayName().contains("强化突破石")) {
        // 获取突破石等级 - 使用锋利附魔(Enchantment.DAMAGE_ALL)
        int breakthroughLevel = fuel.getItemMeta().hasEnchant(Enchantment.DAMAGE_ALL)
            ? fuel.getItemMeta().getEnchantLevel(Enchantment.DAMAGE_ALL)
            : 0;
        if (breakthroughLevel > 0) {
          // 获取装备当前等级
          int currentLevel = smelt.getEnchantmentLevel(
              this.enchan.getEnchan(this.config.getInt("id.items." + smelt.getTypeId())));

          // 从配置文件获取突破石需要的装备等级
          int requiredLevel = 0;

          // 检查配置文件中是否有该等级的设置
          if (this.config.contains("breakthrough.levels." + breakthroughLevel)) {
            requiredLevel = this.config.getInt("breakthrough.levels." + breakthroughLevel);
          } else {
            // 如果配置文件中没有设置，则使用默认规则：需要前一级
            requiredLevel = breakthroughLevel - 1;
          }

          // 检查装备等级是否已经达到或超过突破石等级
          if (currentLevel >= breakthroughLevel) {
            // 已经达到或超过突破石等级，取消燃烧
            e.setCancelled(true);

            // 获取附近的玩家并发送消息
            Player player = this.player.get(furnace.getBlock().hashCode());
            if (player != null) {
              // 使用玩家UUID作为键，创建消息冷却机制
              String playerUUID = player.getUniqueId().toString();
              long currentTime = System.currentTimeMillis();

              // 准备错误消息
              String errorMessage = "§c装备等级已经达到或超过突破石等级，无法使用";

              // 检查是否在冷却时间内（3秒）
              if (!messageCooldowns.containsKey(playerUUID + "_breakthrough") ||
                  currentTime - messageCooldowns.get(playerUUID + "_breakthrough") > 3000) {

                // 更新冷却时间
                messageCooldowns.put(playerUUID + "_breakthrough", currentTime);
              }

              // 无论冷却时间如何，都显示错误消息
              p.sendMessage("§e【§c强化系统§e】 " + errorMessage);

              // 返还突破石给玩家
              ItemStack fuelCopy = fuel.clone();
              p.getInventory().addItem(fuelCopy);
              p.sendMessage("§e【§c强化系统§e】 §a突破石已返还");

              // 移除熔炉中的燃料
              furnace.getInventory().setFuel(null);
              furnace.update();

              // 重置熔炉状态，确保不会开始工作
              furnace.setBurnTime((short) 0);
              furnace.setCookTime((short) 0);
              furnace.update();
            }
            return;
          }

          // 检查装备等级是否满足要求
          if (currentLevel < requiredLevel) {
            // 不满足要求，取消燃烧
            e.setCancelled(true);

            // 获取附近的玩家并发送消息
            Player player = this.player.get(furnace.getBlock().hashCode());
            if (player != null) {
              // 使用玩家UUID作为键，创建消息冷却机制
              String playerUUID = player.getUniqueId().toString();
              long currentTime = System.currentTimeMillis();

              // 准备错误消息
              String errorMessage = "§c装备等级必须达到" + requiredLevel + "级才能使用" + breakthroughLevel + "级突破石";

              // 检查是否在冷却时间内（3秒）
              // 注意：我们仍然保留冷却时间检查，但只用于防止短时间内重复显示相同消息
              if (!messageCooldowns.containsKey(playerUUID) ||
                  currentTime - messageCooldowns.get(playerUUID) > 3000) {

                // 更新冷却时间
                messageCooldowns.put(playerUUID, currentTime);
              }

              // 无论冷却时间如何，都显示错误消息和返还消息
              p.sendMessage("§e【§c强化系统§e】 " + errorMessage);

              // 重置熔炉状态，确保不会开始工作
              furnace.setBurnTime((short) 0);
              furnace.setCookTime((short) 0);
              furnace.update();

              // 返还突破石给玩家
              ItemStack fuelCopy = furnace.getInventory().getFuel().clone();
              furnace.getInventory().setFuel(null);
              p.getInventory().addItem(fuelCopy);
              p.sendMessage("§e【§c强化系统§e】 §a突破石已返还");
            }

            return;
          }
        }
      }

      // 检查是否是直升符咒，且不满足等级要求
      if (fuel.getItemMeta().getDisplayName().contains("强化直升符咒")) {
        // 提取直升符咒等级
        String displayName = fuel.getItemMeta().getDisplayName();
        String levelStr = "";
        if (displayName.contains("§l")) {
          String[] parts = displayName.split("§l");
          if (parts.length > 1) {
            levelStr = parts[1];
          }
        }

        try {
          int directLevel = Integer.parseInt(levelStr);
          int currentLevel = smelt.getEnchantmentLevel(
              this.enchan.getEnchan(this.config.getInt("id.items." + smelt.getTypeId())));

          // 从配置文件获取等级要求
          int requiredLevel = 0;
          if (this.config.contains("items.direct_upgrade.level_requirements." + directLevel)) {
            requiredLevel = this.config.getInt("items.direct_upgrade.level_requirements." + directLevel);
          } else {
            // 如果没有特定等级的配置，使用默认规则
            if (directLevel == 29)
              requiredLevel = 20;
            else if (directLevel == 49)
              requiredLevel = 29;
            else if (directLevel == 69)
              requiredLevel = 49;
            else if (directLevel == 99)
              requiredLevel = 69;
            else
              requiredLevel = Math.max(1, directLevel - 10); // 默认需要前10级
          }

          // 检查是否满足等级要求
          if (currentLevel < requiredLevel) {

            // 不满足要求，取消燃烧
            e.setCancelled(true);

            // 获取附近的玩家并发送消息
            Player player = this.player.get(furnace.getBlock().hashCode());
            if (player != null) {
              // 使用玩家UUID作为键，创建消息冷却机制
              String playerUUID = player.getUniqueId().toString();
              long currentTime = System.currentTimeMillis();

              // 准备错误消息
              String errorMessage = "§c装备等级必须达到" + requiredLevel + "级才能使用" + directLevel + "级直升符咒";

              // 检查是否在冷却时间内（3秒）
              // 注意：我们仍然保留冷却时间检查，但只用于防止短时间内重复显示相同消息
              if (!messageCooldowns.containsKey(playerUUID) ||
                  currentTime - messageCooldowns.get(playerUUID) > 3000) {

                // 更新冷却时间
                messageCooldowns.put(playerUUID, currentTime);
              }

              // 无论冷却时间如何，都显示错误消息和返还消息
              p.sendMessage("§e【§c强化系统§e】 " + errorMessage);

              // 重置熔炉状态，确保不会开始工作
              furnace.setBurnTime((short) 0);
              furnace.setCookTime((short) 0);
              furnace.update();

              // 返还直升符咒给玩家
              ItemStack fuelCopy = furnace.getInventory().getFuel().clone();
              furnace.getInventory().setFuel(null);
              p.getInventory().addItem(fuelCopy);
              p.sendMessage("§e【§c强化系统§e】 §a直升符咒已返还");
            }

            return;
          }
        } catch (Exception ex) {
          // 解析失败，忽略
        }
      }

      // 正常情况下继续处理
      if (this.furnCheck(furnace)) {
        e.setBurning(true);
        // 设置燃烧时间为200 ticks (10秒)，刚好完成一次强化
        e.setBurnTime(200);

        // 设置熔炉的烹饪时间为200 ticks，与燃烧时间相同
        furnace.setCookTime((short) 0);
        furnace.update();
      } else {
        // 如果furnCheck返回false，表示不满足强化条件，取消燃烧
        e.setCancelled(true);

        // 重置熔炉状态，确保不会开始工作
        furnace.setBurnTime((short) 0);
        furnace.setCookTime((short) 0);
        furnace.update();
      }
    }
  }

  // 防止移动熔炉中的装备，并禁止在打开熔炉界面时使用Shift+左键点击物品
  // 同时处理战力排行榜菜单和玩家装备信息菜单的点击事件
  @EventHandler
  public void onInventoryClick(InventoryClickEvent e) {
    // 处理战力排行榜菜单的点击事件
    if (e.getInventory().getTitle().equals("§e§l✦ 战力排行榜 ✦")) {
      e.setCancelled(true); // 取消所有点击事件，防止玩家拿走物品

      // 处理关闭按钮点击
      if (e.getRawSlot() == 48 && e.getCurrentItem() != null && e.getCurrentItem().getType() == Material.BARRIER) {
        Player player = (Player) e.getWhoClicked();
        player.closeInventory();
        player.playSound(player.getLocation(), Sound.CLICK, 0.5f, 1.0f);
      }
      return;
    }

    // 处理玩家装备信息菜单的点击事件
    if (e.getInventory().getTitle().contains("§e§l✦") && e.getInventory().getTitle().contains("的装备信息 ✦")) {
      e.setCancelled(true); // 取消所有点击事件，防止玩家拿走物品

      // 处理关闭按钮点击
      if (e.getRawSlot() == 49 && e.getCurrentItem() != null && e.getCurrentItem().getType() == Material.BARRIER) {
        Player player = (Player) e.getWhoClicked();
        player.closeInventory();
        player.playSound(player.getLocation(), Sound.CLICK, 0.5f, 1.0f);
      }
      return;
    }

    // 处理熔炉的点击事件
    if (e.getInventory() instanceof FurnaceInventory) {
      FurnaceInventory furnaceInv = (FurnaceInventory) e.getInventory();
      Furnace furnace = (Furnace) furnaceInv.getHolder();
      Player p = (Player) e.getWhoClicked();

      // 检查这个熔炉是否属于强化系统
      if (!isEnhancementFurnace(furnace)) {
        return; // 不是强化系统的熔炉，不处理
      }

      // 完全禁止在熔炉界面使用Shift+左键点击物品
      if (p.isSneaking() && e.isLeftClick()) {
        e.setCancelled(true);
        p.sendMessage("§e【§c强化系统§e】 §c请关闭熔炉界面后，使用Shift+左键点击熔炉添加装备");
        return;
      }

      // 检查熔炉是否正在燃烧
      if (furnace.getBurnTime() > 0) {
        // 如果点击的是上方的装备槽或者下方的燃料槽
        if (e.getRawSlot() == 0 || e.getRawSlot() == 1) {
          e.setCancelled(true);
          p.sendMessage("§e【§c强化系统§e】 §c强化进行中，无法移动物品");
        }
      }
    }
  }

  // 防止拖动物品到熔炉中或战力排行榜菜单中
  @EventHandler
  public void onInventoryDrag(InventoryDragEvent e) {
    // 处理战力排行榜菜单的拖动事件
    if (e.getInventory().getTitle().equals("§e§l✦ 战力排行榜 ✦")) {
      e.setCancelled(true); // 取消所有拖动事件，防止玩家拖动物品到菜单中
      return;
    }

    // 处理玩家装备信息菜单的拖动事件
    if (e.getInventory().getTitle().contains("§e§l✦") && e.getInventory().getTitle().contains("的装备信息 ✦")) {
      e.setCancelled(true); // 取消所有拖动事件，防止玩家拖动物品到菜单中
      return;
    }

    // 处理熔炉的拖动事件
    if (e.getInventory() instanceof FurnaceInventory) {
      FurnaceInventory furnaceInv = (FurnaceInventory) e.getInventory();

      // 检查熔炉是否正在燃烧
      Furnace furnace = (Furnace) furnaceInv.getHolder();

      // 检查这个熔炉是否属于强化系统
      if (!isEnhancementFurnace(furnace)) {
        return; // 不是强化系统的熔炉，不处理
      }

      if (furnace.getBurnTime() > 0) {
        // 检查是否拖动到上方的装备槽或者下方的燃料槽
        for (int slot : e.getRawSlots()) {
          if (slot == 0 || slot == 1) {
            e.setCancelled(true);
            Player p = (Player) e.getWhoClicked();
            p.sendMessage("§e【§c强化系统§e】 §c强化进行中，无法移动物品");
            break;
          }
        }
      }
    }
  }

  @EventHandler(priority = EventPriority.LOW)
  public void onFurnaceSmelt(FurnaceSmeltEvent e) {
    // 获取熔炉
    Furnace furnace = (Furnace) e.getBlock().getState();
    int hash = e.getBlock().hashCode();

    // 获取燃料和装备
    ItemStack currentFuel = furnace.getInventory().getFuel();
    ItemStack smelt = furnace.getInventory().getSmelting();
    ItemStack source = e.getSource();

    // 获取之前保存的燃料
    ItemStack savedFuel = this.fuelItem.get(hash);
    Player player = this.player.get(hash);

    // 调试信息
    logToConsole("熔炉冶炼事件触发");
    if (currentFuel != null) {
      logToConsole("当前燃料: " + currentFuel.getType()
          + (currentFuel.hasItemMeta() && currentFuel.getItemMeta().hasDisplayName()
              ? " 名称: " + currentFuel.getItemMeta().getDisplayName()
              : ""));
    } else {
      logToConsole("当前燃料为空");
    }

    if (savedFuel != null) {
      logToConsole("保存的燃料: " + savedFuel.getType()
          + (savedFuel.hasItemMeta() && savedFuel.getItemMeta().hasDisplayName()
              ? " 名称: " + savedFuel.getItemMeta().getDisplayName()
              : ""));
    } else {
      logToConsole("保存的燃料为空");
    }

    if (smelt != null) {
      logToConsole(
          "装备: " + smelt.getType() + " 耐久度: " + smelt.getDurability() + "/" + smelt.getType().getMaxDurability());
      if (smelt.hasItemMeta()) {
        logToConsole("装备有元数据");
        if (smelt.getItemMeta().hasDisplayName()) {
          logToConsole("装备名称: " + smelt.getItemMeta().getDisplayName());
        }
        if (smelt.getItemMeta().hasLore()) {
          logToConsole("装备Lore: " + smelt.getItemMeta().getLore());
        }
        if (smelt.getItemMeta().hasEnchants()) {
          logToConsole("装备附魔: " + smelt.getItemMeta().getEnchants());
        }
      }
    } else {
      logToConsole("装备为空");
    }

    // 检查是否是强化系统的物品
    boolean isEnhancementItem = false;

    // 使用保存的燃料进行检查，而不是当前燃料
    if (savedFuel != null && savedFuel.hasItemMeta() && savedFuel.getItemMeta().hasDisplayName()) {
      // 检查是否是强化石
      if (savedFuel.getItemMeta().getDisplayName().equals(this.normalItem.getItemMeta().getDisplayName()) ||
          savedFuel.getItemMeta().getDisplayName().equals(this.luckItem.getItemMeta().getDisplayName()) ||
          savedFuel.getItemMeta().getDisplayName().equals(this.safeItem.getItemMeta().getDisplayName()) ||
          savedFuel.getItemMeta().getDisplayName().equals(this.vipItem.getItemMeta().getDisplayName())) {
        isEnhancementItem = true;
        logToConsole("燃料是强化石");
      }

      // 检查是否是强化突破石（精确匹配，避免与淬炼系统冲突）
      if (savedFuel.getItemMeta().getDisplayName().contains("强化突破石") && !savedFuel.getItemMeta().getDisplayName().contains("淬炼")) {
        isEnhancementItem = true;
        logToConsole("燃料是强化突破石");
      }

      // 检查是否是强化直升符咒（精确匹配，避免与淬炼系统冲突）
      if (savedFuel.getItemMeta().getDisplayName().contains("直升符咒") && !savedFuel.getItemMeta().getDisplayName().contains("淬炼")) {
        isEnhancementItem = true;
        logToConsole("燃料是强化直升符咒");
      }

      // 检查是否是强化棒（精确匹配，避免与淬炼系统冲突）
      if (savedFuel.getItemMeta().getDisplayName().contains("强化棒") && !savedFuel.getItemMeta().getDisplayName().contains("淬炼")) {
        isEnhancementItem = true;
        logToConsole("燃料是强化棒");
      }

      // 检查是否是管理员强化石
      if (savedFuel.getItemMeta().equals(this.adminItem.getItemMeta())) {
        isEnhancementItem = true;
        logToConsole("燃料是管理员强化石");
      }
    }

    // 如果不是强化系统的物品，让原版熔炉正常工作
    if (!isEnhancementItem) {
      logToConsole("燃料不是强化系统的物品，让原版熔炉正常工作");
      return; // 不是强化系统的物品，让Minecraft正常处理
    }

    // 检查装备是否满耐久
    if (smelt != null && smelt.getType() != Material.AIR && smelt.getType().getMaxDurability() > 0) {
      if (smelt.getDurability() > 0) {
        if (player != null) {
          player.sendMessage("§e【§c强化系统§e】 §c只能强化满耐久的装备");
          logToConsole("装备耐久度不满，无法强化");
        }

        // 保持原装备
        ItemStack arr = new ItemStack(e.getSource());
        arr.setAmount(1);
        e.setResult(arr);
        return;
      }
    }

    // 如果没有保存的燃料，让原版熔炉正常工作
    if (savedFuel == null) {
      logToConsole("没有保存的燃料，让原版熔炉正常工作");
      return;
    }

    // 执行强化操作
    ItemStack result = processEnhancement(hash, source, savedFuel, player);

    if (result != null) {
      e.setResult(result);
      if (player != null) {
        player.playSound(player.getLocation(), Sound.ANVIL_USE, 1.0f, 1.0f);
      }
    } else {
      // 如果强化失败，保持原装备
      ItemStack arr = new ItemStack(e.getSource());
      arr.setAmount(1);
      e.setResult(arr);
    }

    // 清除燃料记录
    this.fuelItem.remove(hash);
  }

  /**
   * 处理强化操作
   *
   * @param hash   熔炉哈希值
   * @param item   要强化的物品
   * @param fuel   强化燃料
   * @param player 玩家
   * @return 强化后的物品，如果强化失败则返回null
   */
  private ItemStack processEnhancement(int hash, ItemStack item, ItemStack fuel, Player player) {
    logToConsole("处理强化操作");
    logToConsole("物品: " + item.getType());
    logToConsole("燃料: " + fuel.getType()
        + (fuel.hasItemMeta() && fuel.getItemMeta().hasDisplayName() ? " 名称: " + fuel.getItemMeta().getDisplayName()
            : ""));

    // 检查物品是否可以强化
    boolean isEnhanceable = false;
    int itemTypeId = item.getType().getId();
    for (String i : this.config.getConfigurationSection("id.items").getKeys(false)) {
      int configItemId = Integer.parseInt(i);
      if (configItemId == itemTypeId) {
        isEnhanceable = true;
        break;
      }
    }

    if (!isEnhanceable) {
      logToConsole("物品不可强化");
      return null;
    }

    // 获取当前装备等级
    int currentLevel = getCurrentEnhancementLevel(item);

    // 根据燃料类型执行不同的强化操作
    if (fuel.getItemMeta().equals(this.normalItem.getItemMeta())) {
      // 获取强化石加成
      int bonus = getEnhancementBonus("normal");
      logToConsole("普通强化石加成: " + bonus + " (装备当前等级: " + currentLevel + ")");
      return this.qh(hash, item, false, false, itemTypeId, bonus);
    } else if (fuel.getItemMeta().equals(this.luckItem.getItemMeta())) {
      // 获取强化石加成
      int bonus = getEnhancementBonus("luck");
      logToConsole("幸运强化石加成: " + bonus + " (装备当前等级: " + currentLevel + ")");
      return this.qh(hash, item, false, false, itemTypeId, bonus);
    } else if (fuel.getItemMeta().equals(this.safeItem.getItemMeta())) {
      // 获取强化石加成
      int bonus = getEnhancementBonus("safe");
      logToConsole("安全强化石加成: " + bonus + " (装备当前等级: " + currentLevel + ")");
      return this.qh(hash, item, true, false, itemTypeId, bonus);
    } else if (fuel.getItemMeta().equals(this.vipItem.getItemMeta())) {
      // 获取强化石加成
      int bonus = getEnhancementBonus("vip");
      logToConsole("VIP强化石加成: " + bonus + " (装备当前等级: " + currentLevel + ")");
      return this.qh(hash, item, true, false, itemTypeId, bonus);
    } else if (fuel.getItemMeta().equals(this.adminItem.getItemMeta())) {
      return this.qh(hash, item, true, true, itemTypeId, 100);
    } else if (fuel.getItemMeta().getDisplayName().contains("强化突破石")) {
      // 处理突破石逻辑
      return handleBreakthroughStone(hash, item, fuel, player);
    } else if (fuel.getItemMeta().getDisplayName().contains("强化棒")) {
      // 处理强化棒逻辑
      return handleEnhancementRod(hash, item, fuel, player);
    } else if (fuel.getItemMeta().getDisplayName().contains("直升符咒")) {
      // 处理直升符咒逻辑
      return handleDirectUpgrade(hash, item, fuel, player);
    }

    // 如果没有匹配的燃料类型，返回null
    return null;
  }

  /**
   * 处理突破石强化
   */
  private ItemStack handleBreakthroughStone(int hash, ItemStack item, ItemStack fuel, Player player) {
    // 获取当前装备等级
    int currentLevel = getCurrentEnhancementLevel(item);

    // 获取突破石等级 - 使用锋利附魔(Enchantment.DAMAGE_ALL)
    int breakthroughLevel = fuel.getItemMeta().hasEnchant(Enchantment.DAMAGE_ALL)
        ? fuel.getItemMeta().getEnchantLevel(Enchantment.DAMAGE_ALL)
        : 0;

    // 获取突破石的成功几率 - 优先使用耐久附魔(Enchantment.DURABILITY)存储的几率
    int successChance = 100; // 默认100%

    // 如果突破石有耐久附魔，表示有自定义几率
    if (fuel.getItemMeta().hasEnchant(Enchantment.DURABILITY)) {
      successChance = fuel.getItemMeta().getEnchantLevel(Enchantment.DURABILITY);
      // 记录日志
      logToConsole("突破石有耐久附魔，使用自定义几率: " + successChance + "%");

      // 检查物品的Lore是否包含成功几率信息
      if (fuel.getItemMeta().hasLore()) {
        List<String> lore = fuel.getItemMeta().getLore();
        for (String line : lore) {
          if (line.contains("成功几率")) {
            // 尝试从Lore中提取几率，使用正则表达式移除颜色代码
            String cleanLine = line.replaceAll("§[0-9a-fk-or]", "");
            if (cleanLine.contains("100%")) {
              successChance = 100;
              logToConsole("从Lore中检测到100%成功几率，强制设置为100%");
              break;
            }
          }
        }
      }
    } else {
      // 否则从配置文件获取对应等级的成功几率
      if (this.config.contains("breakthrough.success_chance." + breakthroughLevel)) {
        successChance = this.config.getInt("breakthrough.success_chance." + breakthroughLevel);
      } else {
        // 如果没有特定等级的配置，使用默认值
        successChance = this.config.getInt("breakthrough.success_chance.default", 50);
      }
    }

    // 记录日志
    logToConsole("突破石等级: " + breakthroughLevel + ", 最终成功几率: " + successChance + "%");

    // 检查装备等级是否已经达到或超过突破石等级
    if (currentLevel >= breakthroughLevel) {
      if (player != null) {
        player.sendMessage("§e【§c强化系统§e】 §c装备等级已经达到或超过突破石等级");

        // 返还突破石给玩家
        if (player.getInventory().firstEmpty() != -1) {
          ItemStack fuelCopy = fuel.clone();
          player.getInventory().addItem(fuelCopy);
          player.sendMessage("§e【§c强化系统§e】 §a突破石已返还");
        } else {
          // 如果玩家背包已满，掉落在地上
          player.getWorld().dropItemNaturally(player.getLocation(), fuel.clone());
          player.sendMessage("§e【§c强化系统§e】 §a突破石已返还（背包已满，已掉落在地上）");
        }
      }
      return null;
    }

    // 检查装备是否有最高强化等级限制
    int itemTypeId = item.getType().getId();
    Map<Integer, Integer> maxEnhancementLevels = getMaxEnhancementLevels();
    if (maxEnhancementLevels.containsKey(itemTypeId) && breakthroughLevel > maxEnhancementLevels.get(itemTypeId)) {
      if (player != null) {
        player.sendMessage("§e【§c强化系统§e】 §c此装备最高只能强化到 " + maxEnhancementLevels.get(itemTypeId) + " 级");
      }
      return null;
    }

    // 如果几率是100%，直接成功
    if (successChance >= 100) {
      // 记录日志
      logToConsole("突破石成功几率为100%或更高，直接成功");

      // 显示详细信息给玩家
      if (player != null) {
        player.sendMessage("§e【§c强化系统§e】 §a突破石成功几率为100%，直接成功！");
      }

      ItemStack result = this.getqh(hash, item, 100, breakthroughLevel, true);

      if (player != null && result != null) {
        // 检查是否应该广播突破成功消息
        int minLevelForBroadcast = this.config.getInt("items.breakthrough_stone.broadcast_min_level", 30);
        boolean shouldBroadcast = (minLevelForBroadcast >= 0 && breakthroughLevel >= minLevelForBroadcast);

        if (shouldBroadcast) {
          String broadcastMessage = getBroadcastMessage("breakthrough_success", player.getName(), breakthroughLevel);
          this.getServer().broadcastMessage(broadcastMessage);
        } else {
          // 只向玩家显示消息
          player.sendMessage("§e【§c强化系统§e】 §6恭喜！你成功将装备突破到 §e" + breakthroughLevel + " §6级！");
        }
      }

      return result;
    }

    // 否则根据几率决定成功或失败
    Random random = new Random();
    int randomValue = random.nextInt(100);
    boolean success = randomValue < successChance;

    // 记录详细日志
    logToConsole("突破石随机数结果: " + randomValue + ", 成功几率: " + successChance + "%, 是否成功: " + success);

    // 再次检查几率，确保100%几率的突破石一定成功
    if (successChance >= 100) {
      success = true;
      logToConsole("强制设置为成功，因为几率为100%或更高");
    }

    if (success) {
      ItemStack result = this.getqh(hash, item, 100, breakthroughLevel, true);

      if (player != null && result != null) {
        // 检查是否应该广播突破成功消息
        boolean shouldBroadcast = this.shouldBroadcast("items.breakthrough_stone.broadcast_levels", breakthroughLevel);

        if (shouldBroadcast) {
          String broadcastMessage = getBroadcastMessage("breakthrough_success", player.getName(), breakthroughLevel);
          this.getServer().broadcastMessage(broadcastMessage);
        } else {
          // 只向玩家显示消息
          player.sendMessage("§e【§c强化系统§e】 §6恭喜！你成功将装备突破到 §e" + breakthroughLevel + " §6级！");
        }
      }

      return result;
    } else {
      // 失败处理 - 根据配置决定是否降级
      if (player != null) {
        player.sendMessage("§e【§c强化系统§e】 §c很遗憾，突破失败了！");
      }

      // 获取失败惩罚配置
      int downgradeChance = this.config.getInt("breakthrough.failure_penalty.downgrade_chance", 70);
      int downgradeLevels = this.config.getInt("breakthrough.failure_penalty.downgrade_levels", 1);
      int destroyChance = this.config.getInt("breakthrough.failure_penalty.destroy_chance", 0);

      // 判断是否降级
      boolean isDowngrade = random.nextInt(100) < downgradeChance;

      if (isDowngrade) {
        // 计算降级后的等级
        int newLevel = Math.max(0, currentLevel - downgradeLevels);

        if (player != null) {
          player.sendMessage("§e【§c强化系统§e】 §c装备降级了 " + downgradeLevels + " 级！");
        }

        // 设置新等级
        ItemStack result = this.getqh(hash, item, 100, newLevel, false);

        // 判断是否自毁
        if (destroyChance > 0 && random.nextInt(100) < destroyChance) {
          if (player != null) {
            player.sendMessage("§e【§c强化系统§e】 §4警告：装备强化失败导致装备自毁！");
            this.getServer().broadcastMessage("§e【§c强化系统§e】 §c玩家 §e" + player.getName() + " §c的装备因强化失败而自毁了！");
          }
          return null; // 返回null表示装备被销毁
        }

        return result;
      } else {
        if (player != null) {
          player.sendMessage("§e【§c强化系统§e】 §a幸运的是，装备等级保持不变。");
        }
        return item;
      }
    }
  }

  /**
   * 获取装备当前的强化等级
   */
  private int getCurrentEnhancementLevel(ItemStack item) {
    int itemTypeId = item.getType().getId();
    int enchantId = this.config.getInt("id.items." + itemTypeId);
    Enchantment enchantment = this.enchan.getEnchan(enchantId);
    return item.getEnchantmentLevel(enchantment);
  }

  /**
   * 根据等级获取对应的颜色代码
   *
   * @param level 强化等级
   * @return 对应的颜色代码
   */
  private String getLevelColor(int level) {
    // 确定当前等级使用哪个颜色
    int colorIndex = (level - 1) / 10;
    if (colorIndex < 0)
      colorIndex = 0;
    if (colorIndex >= 10)
      colorIndex = 9; // 最多支持到100级的颜色

    // 从配置文件中读取颜色设置，如果没有则使用默认值
    String colorKey = "style.colors.level_" + ((colorIndex + 1) * 10);
    String colorCode = this.config.getString(colorKey);

    // 记录调试信息
    if (this.config.getBoolean("style.debug_logs", false)) {
      logToConsole("等级颜色查找 - 等级: " + level + ", 颜色索引: " + colorIndex + ", 配置键: " + colorKey + ", 颜色代码: " + colorCode);
    }

    if (colorCode != null && !colorCode.isEmpty()) {
      return colorCode;
    } else {
      // 默认颜色设置
      String defaultColor;
      switch (colorIndex) {
        case 0:
          defaultColor = "§a"; // 1-10级：绿色
          break;
        case 1:
          defaultColor = "§b"; // 11-20级：青色
          break;
        case 2:
          defaultColor = "§e"; // 21-30级：黄色
          break;
        case 3:
          defaultColor = "§6"; // 31-40级：金色
          break;
        case 4:
          defaultColor = "§c"; // 41-50级：红色
          break;
        case 5:
          defaultColor = "§d"; // 51-60级：粉色
          break;
        case 6:
          defaultColor = "§5"; // 61-70级：紫色
          break;
        case 7:
          defaultColor = "§9"; // 71-80级：蓝色
          break;
        case 8:
          defaultColor = "§1"; // 81-90级：深蓝色
          break;
        case 9:
          defaultColor = "§4"; // 91-100级：深红色
          break;
        default:
          defaultColor = "§f"; // 默认白色
          break;
      }

      if (this.config.getBoolean("style.debug_logs", false)) {
        logToConsole("使用默认颜色 - 等级: " + level + ", 默认颜色: " + defaultColor);
      }

      return defaultColor;
    }
  }

  /**
   * 从配置文件获取公告消息
   *
   * @param messageType 消息类型 (enhancement_success, direct_upgrade_success,
   *                    breakthrough_success, enhancement_rod_success)
   * @param playerName  玩家名称
   * @param level       强化等级
   * @return 格式化后的公告消息
   */
  private String getBroadcastMessage(String messageType, String playerName, int level) {
    String message = this.config.getString("broadcast_messages." + messageType);
    if (message == null) {
      // 如果没有配置，使用默认消息
      switch (messageType) {
        case "enhancement_success":
          message = "§e【§c强化系统§e】 §7恭喜玩家 §b{player} §7把装备强化到了 {level} §7级！！！";
          break;
        case "direct_upgrade_success":
          message = "§e【§c强化系统§e】 §6恭喜玩家 §b{player} §6使用直升符咒将装备直升到了 {level} §6级！！！";
          break;
        case "breakthrough_success":
          message = "§e【§c强化系统§e】 §d恭喜玩家 §b{player} §d使用突破石将装备突破到了 {level} §d级！！！";
          break;
        case "enhancement_rod_success":
          message = "§e【§c强化系统§e】 §9恭喜玩家 §b{player} §9使用强化棒将装备提升到了 {level} §9级！！！";
          break;
        default:
          message = "§e【§c强化系统§e】 §7恭喜玩家 §b{player} §7把装备强化到了 {level} §7级！！！";
          break;
      }
    }

    // 获取等级对应的颜色
    String levelColor = getLevelColor(level);
    String coloredLevel = levelColor + "§l" + level;

    // 替换变量
    return message.replace("{player}", playerName).replace("{level}", coloredLevel);
  }

  /**
   * 检查是否应该发送公告
   *
   * @param configPath 配置路径
   * @param level      当前等级
   * @return 是否应该发送公告
   */
  private boolean shouldBroadcast(String configPath, int level) {
    // 先尝试获取字符串列表（支持范围格式和混合格式）
    List<String> broadcastRanges = this.config.getStringList(configPath);

    // 如果有字符串列表且不为空，处理范围和单个等级
    if (!broadcastRanges.isEmpty()) {
      // 遍历范围配置
      for (String range : broadcastRanges) {
        try {
          // 检查是否是范围格式 "起始-结束"
          if (range.contains("-")) {
            String[] parts = range.split("-");
            if (parts.length == 2) {
              int start = Integer.parseInt(parts[0].trim());
              int end = Integer.parseInt(parts[1].trim());

              // 检查当前等级是否在范围内
              if (level >= start && level <= end) {
                return true;
              }
            }
          } else {
            // 单个等级
            int singleLevel = Integer.parseInt(range.trim());
            if (singleLevel == 0) {
              return true; // 0表示所有等级
            }
            if (level == singleLevel) {
              return true;
            }
          }
        } catch (NumberFormatException e) {
          // 忽略格式错误的配置
          logToConsole("无效的公告等级配置: " + range);
        }
      }
      return false;
    }

    // 如果没有字符串列表，尝试获取整数列表（向后兼容）
    List<Integer> broadcastLevels = this.config.getIntegerList(configPath);

    // 如果整数列表也为空，不发送公告
    if (broadcastLevels.isEmpty()) {
      return false;
    }

    // 如果包含0，表示所有等级都发送公告
    if (broadcastLevels.contains(0)) {
      return true;
    }

    // 检查当前等级是否在指定列表中
    return broadcastLevels.contains(level);
  }

  /**
   * 获取最高强化等级限制
   */
  private Map<Integer, Integer> getMaxEnhancementLevels() {
    Map<Integer, Integer> maxLevels = new HashMap<>();
    ConfigurationSection section = this.config.getConfigurationSection("max_enhancement_levels");
    if (section != null) {
      for (String key : section.getKeys(false)) {
        try {
          int itemId = Integer.parseInt(key);
          int maxLevel = section.getInt(key);
          maxLevels.put(itemId, maxLevel);
        } catch (NumberFormatException e) {
          System.out.println("无效的物品ID: " + key);
        }
      }
    }
    return maxLevels;
  }

  /**
   * 获取装备当前等级的基础强化几率
   *
   * @param currentLevel 装备当前等级
   * @return 基础强化几率
   */
  private int getBaseEnhancementChance(int currentLevel) {
    // 获取基础几率配置
    ConfigurationSection defaultSection = this.config.getConfigurationSection("chance.default");
    if (defaultSection == null) {
      // 如果没有新配置，使用旧的逗号分隔格式作为兼容
      String oldDefault = this.config.getString("chance.default", "100,100,85,70,55,40,25,15,10,5,1");
      String[] chances = oldDefault.split(",");
      if (currentLevel <= chances.length) {
        try {
          return Integer.parseInt(chances[currentLevel - 1]);
        } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
          return 1; // 默认1%
        }
      }
      return 1; // 默认1%
    }

    // 遍历所有配置项，支持混合格式
    for (String key : defaultSection.getKeys(false)) {
      try {
        // 检查是否是范围格式
        if (key.contains("-")) {
          // 处理范围格式，如 "1-5", "10-20"
          String[] parts = key.split("-");
          if (parts.length == 2) {
            int start = Integer.parseInt(parts[0].trim());
            String endPart = parts[1].trim();

            // 处理 "21+" 这种格式
            if (endPart.equals("+")) {
              if (currentLevel >= start) {
                return defaultSection.getInt(key);
              }
            } else {
              int end = Integer.parseInt(endPart);
              if (currentLevel >= start && currentLevel <= end) {
                return defaultSection.getInt(key);
              }
            }
          }
        } else {
          // 处理单个等级格式
          int level = Integer.parseInt(key);
          if (level == currentLevel) {
            return defaultSection.getInt(key);
          }
        }
      } catch (NumberFormatException e) {
        // 忽略无效的配置格式
        if (this.config.getBoolean("style.debug_logs", false)) {
          logToConsole("无效的基础几率配置格式: " + key);
        }
      }
    }

    // 如果没有找到匹配的配置，查找最接近的较低等级
    int closestLevel = 0;
    int closestChance = 1; // 默认1%

    for (String key : defaultSection.getKeys(false)) {
      try {
        if (key.contains("-")) {
          // 处理范围格式
          String[] parts = key.split("-");
          if (parts.length == 2) {
            int start = Integer.parseInt(parts[0].trim());
            String endPart = parts[1].trim();

            if (endPart.equals("+")) {
              // "21+" 格式，如果当前等级小于起始等级，且起始等级是目前找到的最接近的
              if (currentLevel > start && start > closestLevel) {
                closestLevel = start;
                closestChance = defaultSection.getInt(key);
              }
            } else {
              int end = Integer.parseInt(endPart);
              // 如果当前等级大于范围结束，且结束等级是目前找到的最接近的
              if (currentLevel > end && end > closestLevel) {
                closestLevel = end;
                closestChance = defaultSection.getInt(key);
              }
            }
          }
        } else {
          // 处理单个等级格式
          int level = Integer.parseInt(key);
          if (level <= currentLevel && level > closestLevel) {
            closestLevel = level;
            closestChance = defaultSection.getInt(key);
          }
        }
      } catch (NumberFormatException e) {
        // 忽略无效的等级配置
      }
    }

    // 记录调试信息
    if (this.config.getBoolean("style.debug_logs", false)) {
      logToConsole("基础几率查找 - 等级: " + currentLevel + ", 最接近配置: " + closestLevel + ", 几率: " + closestChance + "%");
    }

    return closestChance;
  }

  /**
   * 获取强化石加成
   *
   * @param stoneType 强化石类型 (normal, luck, safe, vip)
   * @return 强化石加成值
   */
  private int getEnhancementBonus(String stoneType) {
    switch (stoneType) {
      case "normal":
        return this.config.getInt("chance.normal", 0);
      case "luck":
        return this.config.getInt("chance.luck", 15);
      case "safe":
        return this.config.getInt("chance.safe", 20);
      case "vip":
        return this.config.getInt("chance.vip", 30);
      case "admin":
        return this.config.getInt("chance.admin", 100);
      default:
        return 0;
    }
  }

  /**
   * 处理强化棒强化
   */
  private ItemStack handleEnhancementRod(int hash, ItemStack item, ItemStack fuel, Player player) {
    // 获取当前装备等级
    int currentLevel = getCurrentEnhancementLevel(item);

    // 获取强化棒的提升等级 - 使用锋利附魔(Enchantment.DAMAGE_ALL)
    int enhancementLevel = fuel.getItemMeta().hasEnchant(Enchantment.DAMAGE_ALL)
        ? fuel.getItemMeta().getEnchantLevel(Enchantment.DAMAGE_ALL)
        : 0;

    if (enhancementLevel <= 0) {
      return null;
    }

    // 获取突破等级点
    List<Integer> breakthroughLevels = new ArrayList<>();
    ConfigurationSection breakthroughSection = this.config.getConfigurationSection("breakthrough.levels");
    if (breakthroughSection != null) {
      for (String key : breakthroughSection.getKeys(false)) {
        try {
          int level = Integer.parseInt(key);
          breakthroughLevels.add(level);
        } catch (NumberFormatException e) {
          System.out.println("无效的突破等级: " + key);
        }
      }
    }

    // 如果配置文件中没有设置，则使用默认值
    if (breakthroughLevels.isEmpty()) {
      breakthroughLevels.add(30);
      breakthroughLevels.add(50);
      breakthroughLevels.add(70);
      breakthroughLevels.add(100);
    }

    // 排序突破等级点
    Collections.sort(breakthroughLevels);

    // 检查是否需要突破
    int maxNormalLevel = this.config.getInt("breakthrough.max_normal_level", 29);

    // 计算目标等级，但要考虑突破限制
    int targetLevel = currentLevel + enhancementLevel;

    // 检查是否会超过普通强化上限
    if (currentLevel < maxNormalLevel && targetLevel > maxNormalLevel) {
      targetLevel = maxNormalLevel;
      if (player != null) {
        player.sendMessage("§e【§c强化系统§e】 §c强化棒不能突破" + maxNormalLevel + "级，已调整为" + targetLevel + "级");
      }
    }

    // 如果当前等级已经达到最高普通强化等级，需要使用突破石
    if (currentLevel >= maxNormalLevel) {
      // 找到下一个突破点
      int nextBreakthroughLevel = -1;
      for (int level : breakthroughLevels) {
        if (level > currentLevel) {
          nextBreakthroughLevel = level;
          break;
        }
      }

      if (nextBreakthroughLevel > 0) {
        if (player != null) {
          player.sendMessage("§e【§c强化系统§e】 §c装备已达到" + currentLevel + "级，需要使用" + nextBreakthroughLevel + "级突破石才能继续强化");
        }
        return null;
      }
    }

    // 检查装备是否有最高强化等级限制
    int itemTypeId = item.getType().getId();
    Map<Integer, Integer> maxEnhancementLevels = getMaxEnhancementLevels();
    if (maxEnhancementLevels.containsKey(itemTypeId) && targetLevel > maxEnhancementLevels.get(itemTypeId)) {
      targetLevel = maxEnhancementLevels.get(itemTypeId);
      if (player != null) {
        player.sendMessage("§e【§c强化系统§e】 §c此装备最高只能强化到 " + maxEnhancementLevels.get(itemTypeId) + " 级");
      }

      // 如果调整后的目标等级小于等于当前等级，取消强化
      if (targetLevel <= currentLevel) {
        return null;
      }
    }

    // 获取强化棒的成功几率 - 使用耐久附魔(Enchantment.DURABILITY)
    int successChance = this.config.getInt("items.enhancement_rod.default_chance", 80); // 默认使用配置文件中的值
    if (fuel.getItemMeta().hasEnchant(Enchantment.DURABILITY)) {
      successChance = fuel.getItemMeta().getEnchantLevel(Enchantment.DURABILITY);
    }

    // 计算成功几率
    Random random = new Random();
    boolean success = random.nextInt(100) < successChance;

    if (success) {
      // 成功时，直接设置为目标等级
      ItemStack result = this.getqh(hash, item, 100, targetLevel, false);

      if (player != null && result != null) {
        // 检查是否应该广播强化棒成功消息
        boolean shouldBroadcast = this.shouldBroadcast("items.enhancement_rod.broadcast_levels", targetLevel);

        if (shouldBroadcast) {
          String broadcastMessage = getBroadcastMessage("enhancement_rod_success", player.getName(), targetLevel);
          this.getServer().broadcastMessage(broadcastMessage);
        } else {
          // 只向玩家显示消息
          player.sendMessage("§e【§c强化系统§e】 §6恭喜！你成功将装备强化到 §e" + targetLevel + " §6级！");
        }
      }

      return result;
    } else {
      // 失败处理 - 根据配置决定是否降级
      if (player != null) {
        player.sendMessage("§e【§c强化系统§e】 §c很遗憾，强化棒使用失败！");
      }

      // 获取失败惩罚配置
      int downgradeChance = this.config.getInt("items.enhancement_rod.failure_penalty.downgrade_chance", 70);
      int downgradeLevels = this.config.getInt("items.enhancement_rod.failure_penalty.downgrade_levels", 1);
      int destroyChance = this.config.getInt("items.enhancement_rod.failure_penalty.destroy_chance", 0);

      // 记录日志
      if (this.config.getBoolean("style.debug_logs", false)) {
        logToConsole("强化棒失败处理 - 玩家: " + (player != null ? player.getName() : "未知") +
            ", 当前等级: " + currentLevel + ", 降级几率: " + downgradeChance + "%, 自毁几率: " + destroyChance + "%");
      }

      // 判断是否触发降级惩罚
      Random penaltyRandom = new Random();
      boolean isDowngrade = penaltyRandom.nextInt(100) < downgradeChance;

      if (isDowngrade) {
        // 计算降级后的等级
        int newLevel = Math.max(0, currentLevel - downgradeLevels);

        if (player != null) {
          player.sendMessage("§e【§c强化系统§e】 §c装备降级了 " + downgradeLevels + " 级！");
        }

        // 设置新等级
        ItemStack result = this.getqh(hash, item, 100, newLevel, false);

        // 判断是否自毁
        if (destroyChance > 0 && penaltyRandom.nextInt(100) < destroyChance) {
          if (player != null) {
            player.sendMessage("§e【§c强化系统§e】 §4警告：装备强化失败导致装备自毁！");
            this.getServer().broadcastMessage("§e【§c强化系统§e】 §c玩家 §e" + player.getName() + " §c的装备因强化棒失败而自毁了！");
          }
          return null; // 返回null表示装备被销毁
        }

        return result;
      } else {
        if (player != null) {
          player.sendMessage("§e【§c强化系统§e】 §a幸运的是，装备等级保持不变。");
        }
        return item;
      }
    }
  }

  /**
   * 处理直升符咒强化
   */
  private ItemStack handleDirectUpgrade(int hash, ItemStack item, ItemStack fuel, Player player) {
    // 从名称中提取等级
    String displayName = fuel.getItemMeta().getDisplayName();
    String levelStr = "";

    // 根据不同格式提取等级
    if (displayName.contains("-")) {
      levelStr = displayName.substring(displayName.lastIndexOf("-") + 1).trim();
      // 移除颜色代码和非数字字符
      levelStr = levelStr.replaceAll("§[0-9a-fk-or]", "").replaceAll("[^0-9]", "");
    } else {
      // 尝试提取数字
      Pattern pattern = Pattern.compile("\\d+");
      Matcher matcher = pattern.matcher(displayName);
      if (matcher.find()) {
        levelStr = matcher.group();
      }
    }

    try {
      int directLevel = Integer.parseInt(levelStr);

      // 获取当前装备等级
      int currentLevel = getCurrentEnhancementLevel(item);

      // 检查是否满足直升符咒的等级要求
      ConfigurationSection levelRequirements = this.config
          .getConfigurationSection("items.direct_upgrade.level_requirements");
      if (levelRequirements != null && levelRequirements.contains(String.valueOf(directLevel))) {
        int requiredLevel = levelRequirements.getInt(String.valueOf(directLevel));
        if (currentLevel < requiredLevel) {
          if (player != null) {
            player.sendMessage("§e【§c强化系统§e】 §c使用" + directLevel + "级直升符咒需要装备达到" + requiredLevel + "级");
          }
          return null;
        }
      }

      // 检查装备是否有最高强化等级限制
      int itemTypeId = item.getType().getId();
      Map<Integer, Integer> maxEnhancementLevels = getMaxEnhancementLevels();
      if (maxEnhancementLevels.containsKey(itemTypeId)) {
        int maxLevel = maxEnhancementLevels.get(itemTypeId);

        // 如果当前装备等级已经达到或超过了最高强化等级限制
        if (currentLevel >= maxLevel) {
          if (player != null) {
            player.sendMessage("§e【§c强化系统§e】 §c装备已达到最高强化等级限制");
          }
          return null;
        }

        // 如果直升等级超过了最高强化等级限制，调整为最高等级
        if (directLevel > maxLevel) {
          directLevel = maxLevel;
          if (player != null) {
            player.sendMessage("§e【§c强化系统§e】 §c此装备最高只能强化到 " + maxLevel + " 级，已调整直升等级");
          }

          // 如果调整后的直升等级小于等于当前等级，取消强化
          if (directLevel <= currentLevel) {
            return null;
          }
        }
      }

      // 使用配置文件中设置的直升符咒成功几率，false表示这是直升符咒而不是突破石
      int successChance = this.config.getInt("chance.direct_upgrade", 80);
      Random random = new Random();
      boolean success = random.nextInt(100) < successChance;

      if (success) {
        ItemStack result = this.getqh(hash, item, 100, directLevel, false);

        if (player != null && result != null) {
          // 检查是否应该广播直升符咒成功消息
          boolean shouldBroadcast = this.shouldBroadcast("items.direct_upgrade.broadcast_levels", directLevel);

          if (shouldBroadcast) {
            String broadcastMessage = getBroadcastMessage("direct_upgrade_success", player.getName(), directLevel);
            this.getServer().broadcastMessage(broadcastMessage);
          } else {
            // 只向玩家显示消息
            player.sendMessage("§e【§c强化系统§e】 §6恭喜！你成功将装备强化到 §e" + directLevel + " §6级！");
          }
        }

        return result;
      } else {
        if (player != null) {
          player.sendMessage("§e【§c强化系统§e】 §c很遗憾，直升符咒使用失败！装备等级保持不变。");
        }
        return item;
      }
    } catch (NumberFormatException e) {
      System.out.println("解析直升符咒等级失败: " + levelStr);
      return null;
    }
  }

  // 计算玩家的战力值
  public int calculatePlayerPower(Player player) {
    int totalPower = 0;

    // 调试信息：打印玩家名称
    if (this.config.getBoolean("power.debug_logs", false)) {
      System.out.println("计算玩家战力: " + player.getName());
    }

    // 获取每级强化对应的战力值
    int powerPerLevel = this.config.getInt("power.per_level", 10);

    // 显示战力详情
    this.show(player, this.get(19));

    // 检查头盔
    if (player.getInventory().getHelmet() != null) {
      ItemStack helmet = player.getInventory().getHelmet();
      int power = calculateItemPower(helmet, powerPerLevel);
      if (power > 0) {
        totalPower += power;

        // 获取强化等级和额外属性
        int enhanceLevel = getItemLevel(helmet);
        String extraInfo = getItemExtraInfo(helmet);

        // 显示详细信息
        String message = this.get(20).replace("{0}", "头盔").replace("{1}", String.valueOf(power));
        if (enhanceLevel > 0) {
          message = message.replace("{2}", String.valueOf(enhanceLevel));
        } else {
          message = message.replace("{2}", "0");
        }

        // 如果有额外属性，添加到消息中
        if (!extraInfo.isEmpty()) {
          message += " " + extraInfo;
        }

        this.show(player, message);
      }
    }

    // 检查胸甲
    if (player.getInventory().getChestplate() != null) {
      ItemStack chestplate = player.getInventory().getChestplate();
      int power = calculateItemPower(chestplate, powerPerLevel);
      if (power > 0) {
        totalPower += power;

        // 获取强化等级和额外属性
        int enhanceLevel = getItemLevel(chestplate);
        String extraInfo = getItemExtraInfo(chestplate);

        // 显示详细信息
        String message = this.get(20).replace("{0}", "胸甲").replace("{1}", String.valueOf(power));
        if (enhanceLevel > 0) {
          message = message.replace("{2}", String.valueOf(enhanceLevel));
        } else {
          message = message.replace("{2}", "0");
        }

        // 如果有额外属性，添加到消息中
        if (!extraInfo.isEmpty()) {
          message += " " + extraInfo;
        }

        this.show(player, message);
      }
    }

    // 检查护腿
    if (player.getInventory().getLeggings() != null) {
      ItemStack leggings = player.getInventory().getLeggings();
      int power = calculateItemPower(leggings, powerPerLevel);
      if (power > 0) {
        totalPower += power;

        // 获取强化等级和额外属性
        int enhanceLevel = getItemLevel(leggings);
        String extraInfo = getItemExtraInfo(leggings);

        // 显示详细信息
        String message = this.get(20).replace("{0}", "护腿").replace("{1}", String.valueOf(power));
        if (enhanceLevel > 0) {
          message = message.replace("{2}", String.valueOf(enhanceLevel));
        } else {
          message = message.replace("{2}", "0");
        }

        // 如果有额外属性，添加到消息中
        if (!extraInfo.isEmpty()) {
          message += " " + extraInfo;
        }

        this.show(player, message);
      }
    }

    // 检查靴子
    if (player.getInventory().getBoots() != null) {
      ItemStack boots = player.getInventory().getBoots();
      int power = calculateItemPower(boots, powerPerLevel);
      if (power > 0) {
        totalPower += power;

        // 获取强化等级和额外属性
        int enhanceLevel = getItemLevel(boots);
        String extraInfo = getItemExtraInfo(boots);

        // 显示详细信息
        String message = this.get(20).replace("{0}", "靴子").replace("{1}", String.valueOf(power));
        if (enhanceLevel > 0) {
          message = message.replace("{2}", String.valueOf(enhanceLevel));
        } else {
          message = message.replace("{2}", "0");
        }

        // 如果有额外属性，添加到消息中
        if (!extraInfo.isEmpty()) {
          message += " " + extraInfo;
        }

        this.show(player, message);
      }
    }

    // 检查主手武器
    ItemStack weapon = player.getItemInHand();
    if (weapon != null && weapon.getType() != Material.AIR) {
      int power = calculateItemPower(weapon, powerPerLevel);
      if (power > 0) {
        totalPower += power;

        // 获取强化等级和额外属性
        int enhanceLevel = getItemLevel(weapon);
        String extraInfo = getItemExtraInfo(weapon);

        // 显示详细信息
        String message = this.get(20).replace("{0}", "武器").replace("{1}", String.valueOf(power));
        if (enhanceLevel > 0) {
          message = message.replace("{2}", String.valueOf(enhanceLevel));
        } else {
          message = message.replace("{2}", "0");
        }

        // 如果有额外属性，添加到消息中
        if (!extraInfo.isEmpty()) {
          message += " " + extraInfo;
        }

        this.show(player, message);
      }
    }

    // 显示总战力
    this.show(player, this.get(21).replace("{0}", String.valueOf(totalPower)));

    return totalPower;
  }

  /**
   * 获取物品的额外属性信息（防御和伤害）
   *
   * @param item 物品
   * @return 额外属性信息
   */
  private String getItemExtraInfo(ItemStack item) {
    StringBuilder info = new StringBuilder();

    if (item.hasItemMeta() && item.getItemMeta().hasLore()) {
      List<String> lore = item.getItemMeta().getLore();

      for (String line : lore) {
        // 检查淬炼属性中的防御值
        String cleanLine = stripColorCodes(line);
        if (cleanLine.contains("淬炼属性:") && cleanLine.contains("附加防御")) {
          try {
            String numStr = extractNumber(line);
            if (numStr != null && !numStr.isEmpty()) {
              // 处理可能包含小数点的数值
              if (numStr.contains(".")) {
                double defenseValue = Double.parseDouble(numStr);
                info.append("§7[防御:§a+").append(defenseValue).append("§7]");
              } else {
                int defenseValue = Integer.parseInt(numStr);
                info.append("§7[防御:§a+").append(defenseValue).append("§7]");
              }
            }
          } catch (NumberFormatException e) {
            // 忽略解析错误
          }
        }

        // 检查淬炼属性中的伤害值
        cleanLine = stripColorCodes(line);
        if (cleanLine.contains("淬炼属性:") && cleanLine.contains("附加伤害")) {
          try {
            String numStr = extractNumber(line);
            if (numStr != null && !numStr.isEmpty()) {
              // 处理可能包含小数点的数值
              if (numStr.contains(".")) {
                double damageValue = Double.parseDouble(numStr);
                info.append("§7[伤害:§c+").append(damageValue).append("§7]");
              } else {
                int damageValue = Integer.parseInt(numStr);
                info.append("§7[伤害:§c+").append(damageValue).append("§7]");
              }
            }
          } catch (NumberFormatException e) {
            // 忽略解析错误
          }
        }
      }
    }

    return info.toString();
  }

  /**
   * 计算单个物品的战力值
   *
   * @param item          物品
   * @param powerPerLevel 每级强化对应的战力值
   * @return 物品战力值
   */
  private int calculateItemPower(ItemStack item, int powerPerLevel) {
    if (item == null || item.getType() == Material.AIR) {
      return 0;
    }

    // 调试信息：打印物品名称
    if (this.config.getBoolean("power.debug_logs", false)) {
      if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
        System.out.println("计算物品战力: " + item.getItemMeta().getDisplayName());
      } else {
        System.out.println("计算物品战力: " + item.getType().name());
      }
    }

    int totalPower = 0;

    // 检查物品是否在配置的强化物品列表中
    boolean isEnhanceable = false;
    for (String i : this.config.getConfigurationSection("id.items").getKeys(false)) {
      int itemId = Integer.parseInt(i);
      if (itemId == item.getTypeId()) {
        isEnhanceable = true;
        break;
      }
    }

    // 计算强化等级带来的战力
    if (isEnhanceable) {
      // 获取物品的强化等级
      int level = getItemLevel(item);

      // 计算战力值 - 直接用等级乘以每级战力值
      totalPower += level * powerPerLevel;
    }

    // 检查物品lore中的防御和附加伤害
    if (item.hasItemMeta() && item.getItemMeta().hasLore()) {
      List<String> lore = item.getItemMeta().getLore();

      // 调试信息：打印所有lore
      if (this.config.getBoolean("power.debug_logs", false)) {
        System.out.println("物品lore内容:");
        for (String line : lore) {
          System.out.println(" - " + line);
        }
      }

      for (String line : lore) {
        // 检查淬炼属性中的防御值
        String cleanLine = stripColorCodes(line);
        if (cleanLine.contains("淬炼属性:") && cleanLine.contains("附加防御")) {
          try {
            // 提取数字
            String numStr = extractNumber(line);
            if (numStr != null && !numStr.isEmpty()) {
              // 处理可能包含小数点的数值
              if (numStr.contains(".")) {
                double defenseValue = Double.parseDouble(numStr);
                totalPower += (int) Math.round(defenseValue); // 四舍五入
              } else {
                int defenseValue = Integer.parseInt(numStr);
                totalPower += defenseValue;
              }
            }
          } catch (NumberFormatException e) {
            // 忽略解析错误
          }
        }

        // 检查淬炼属性中的伤害值
        cleanLine = stripColorCodes(line);
        if (cleanLine.contains("淬炼属性:") && cleanLine.contains("附加伤害")) {
          try {
            // 提取数字
            String numStr = extractNumber(line);
            if (numStr != null && !numStr.isEmpty()) {
              // 处理可能包含小数点的数值
              if (numStr.contains(".")) {
                double damageValue = Double.parseDouble(numStr);
                totalPower += (int) Math.round(damageValue); // 四舍五入
              } else {
                int damageValue = Integer.parseInt(numStr);
                totalPower += damageValue;
              }
            }
          } catch (NumberFormatException e) {
            // 忽略解析错误
          }
        }
      }
    }

    return totalPower;
  }

  /**
   * 去除Minecraft颜色代码和格式代码
   *
   * @param text 包含颜色代码的字符串
   * @return 去除颜色代码后的字符串
   */
  private String stripColorCodes(String text) {
    if (text == null)
      return "";
    return text.replaceAll("§[0-9a-fklmnor]", "");
  }

  /**
   * 从字符串中提取数字
   *
   * @param text 包含数字的字符串
   * @return 提取出的数字字符串
   */
  private String extractNumber(String text) {
    // 打印原始文本，帮助调试
    if (this.config.getBoolean("power.debug_logs", false)) {
      System.out.println("提取数字的原始文本: " + text);

      // 去除颜色代码，使提取更准确
      String cleanText = stripColorCodes(text);
      System.out.println("去除颜色代码后的文本: " + cleanText);

      // 提取数字
      String result = extractNumberFromText(cleanText);

      // 打印结果
      if (result != null && !result.isEmpty()) {
        System.out.println("提取到的数字: " + result);
      } else {
        System.out.println("未能提取到数字");
      }

      return result;
    } else {
      // 不显示调试日志时，直接处理
      String cleanText = stripColorCodes(text);
      return extractNumberFromText(cleanText);
    }
  }

  /**
   * 从清理后的文本中提取数字
   *
   * @param cleanText 已去除颜色代码的文本
   * @return 提取出的数字字符串
   */
  private String extractNumberFromText(String cleanText) {
    // 首先尝试查找"+数字"的模式，这在游戏中很常见
    int plusIndex = cleanText.lastIndexOf("+"); // 使用lastIndexOf找到最后一个+号，通常数字就在它后面
    if (plusIndex != -1 && plusIndex + 1 < cleanText.length()) {
      StringBuilder number = new StringBuilder();
      for (int i = plusIndex + 1; i < cleanText.length(); i++) {
        char c = cleanText.charAt(i);
        if (Character.isDigit(c) || c == '.') {
          number.append(c);
        } else {
          break;
        }
      }

      if (number.length() > 0) {
        return number.toString();
      }
    }

    // 如果没有找到"+数字"模式，则尝试查找任何数字
    // 从后往前搜索，因为数字通常在文本的末尾
    StringBuilder number = new StringBuilder();
    boolean foundNumber = false;

    for (int i = cleanText.length() - 1; i >= 0; i--) {
      char c = cleanText.charAt(i);

      // 检查是否是数字或小数点
      if (Character.isDigit(c) || c == '.') {
        number.insert(0, c); // 因为是从后往前，所以要插入到开头
        foundNumber = true;
      }
      // 如果找到了数字，并且当前字符是+号，说明这可能是"+数字"的格式
      else if (foundNumber && c == '+') {
        break; // 找到了完整的"+数字"格式，结束搜索
      }
      // 如果已经找到数字，但当前字符不是数字、小数点或+号，则结束提取
      else if (foundNumber) {
        break;
      }
    }

    if (foundNumber) {
      return number.toString();
    }

    return "";
  }

  /**
   * 获取物品的强化等级
   *
   * @param item 物品
   * @return 强化等级
   */
  private int getItemLevel(ItemStack item) {
    if (item == null || item.getType() == Material.AIR) {
      return 0;
    }

    // 检查物品是否在配置的强化物品列表中
    int enchantId = -1;
    for (String i : this.config.getConfigurationSection("id.items").getKeys(false)) {
      int itemId = Integer.parseInt(i);
      if (itemId == item.getTypeId()) {
        enchantId = this.config.getInt("id.items." + itemId);
        break;
      }
    }

    if (enchantId == -1) {
      return 0;
    }

    // 获取物品的强化等级
    Enchantment enchantment = this.enchan.getEnchan(enchantId);
    return item.getEnchantmentLevel(enchantment);
  }

  /**
   * 设置物品的强化等级
   *
   * @param player 玩家
   * @param item   物品
   * @param level  强化等级
   */
  private void setItemEnchantLevel(Player player, ItemStack item, int level) {
    if (item == null || item.getType() == Material.AIR) {
      return;
    }

    // 检查物品是否在配置的强化物品列表中
    int enchantId = -1;
    int itemId = -1;
    for (String i : this.config.getConfigurationSection("id.items").getKeys(false)) {
      itemId = Integer.parseInt(i);
      if (itemId == item.getTypeId()) {
        enchantId = this.config.getInt("id.items." + itemId);
        break;
      }
    }

    if (enchantId == -1) {
      return;
    }

    // 检查装备是否有最高强化等级限制
    if (maxEnhancementLevels.containsKey(itemId)) {
      int maxLevel = maxEnhancementLevels.get(itemId);

      // 如果是管理员，不受限制
      if (!player.hasPermission(this.config.getString("Admin")) && level > maxLevel) {
        this.show(player, "§e【§c强化系统§e】 §c此装备最高只能强化到 " + maxLevel + " 级");
        return; // 不进行强化
      }
    }

    // 获取物品的附魔对象
    Enchantment enchantment = this.enchan.getEnchan(enchantId);

    // 克隆物品以避免引用问题
    ItemStack newItem = item.clone();
    ItemMeta meta = newItem.getItemMeta();

    // 设置lore
    List<String> lore = this.setLore(newItem, level);
    meta.setLore(lore);
    newItem.setItemMeta(meta);

    // 设置强化等级
    if (level > 0) {
      newItem.addUnsafeEnchantment(enchantment, level);
    } else {
      newItem.removeEnchantment(enchantment);
    }

    // 替换玩家手中的物品
    player.setItemInHand(newItem);

    // 检查是否应该发送公告（管理员测试功能）
    if (level > 0) {
      // 检查普通强化公告
      boolean shouldBroadcastNormal = this.shouldBroadcast("broadcast.enhancement_levels", level);

      // 如果符合普通强化公告条件，就发送公告（用于管理员测试）
      if (shouldBroadcastNormal) {
        String broadcastMessage = getBroadcastMessage("enhancement_success", player.getName(), level);
        this.getServer().broadcastMessage(broadcastMessage);

        // 向管理员发送测试提示
        this.show(player, "§e【§c强化系统§e】 §a已发送公告测试 - 等级 " + level + " 符合公告条件");
      } else {
        // 向管理员发送未发送公告的提示
        this.show(player, "§e【§c强化系统§e】 §7等级 " + level + " 不在公告配置中，未发送公告");
      }
    }

    // 播放成功音效
    player.playSound(player.getLocation(), Sound.ANVIL_USE, 1.0f, 1.0f);
  }

  /**
   * 从配置文件创建直升符咒
   *
   * @param level  直升等级
   * @param amount 数量
   * @return 直升符咒物品
   */
  private ItemStack createDirectUpgradeItem(int level, int amount) {
    // 从配置文件获取材质
    String materialName = this.config.getString("items.direct_upgrade.material", "PAPER");
    Material material;
    try {
      material = Material.valueOf(materialName.toUpperCase());
    } catch (IllegalArgumentException e) {
      material = Material.PAPER; // 默认使用纸
    }

    // 创建物品
    ItemStack directItem = new ItemStack(material);
    ItemMeta meta = directItem.getItemMeta();

    // 从配置文件获取等级要求
    int requiredLevel = 0;
    if (this.config.contains("items.direct_upgrade.level_requirements." + level)) {
      requiredLevel = this.config.getInt("items.direct_upgrade.level_requirements." + level);
    } else {
      // 如果没有特定等级的配置，使用默认规则
      if (level == 29)
        requiredLevel = 20;
      else if (level == 49)
        requiredLevel = 29;
      else if (level == 69)
        requiredLevel = 49;
      else if (level == 99)
        requiredLevel = 69;
      else
        requiredLevel = Math.max(1, level - 10); // 默认需要前10级
    }

    // 从配置文件获取名称格式并替换占位符
    String nameFormat = this.config.getString("items.direct_upgrade.name", "§a【§b强化直升符咒§a】 - §e§l{level}");
    String name = nameFormat.replace("{level}", String.valueOf(level));
    meta.setDisplayName(name);

    // 从配置文件获取描述并替换占位符
    List<String> configLore = this.config.getStringList("items.direct_upgrade.lore");
    List<String> lore = new ArrayList<>();

    for (String line : configLore) {
      lore.add(line.replace("{level}", String.valueOf(level))
          .replace("{required_level}", String.valueOf(requiredLevel))
          .replace("{chance}", String.valueOf(this.directUpgradeChance)));
    }

    // 如果配置文件中没有包含成功几率的信息，添加一行
    boolean hasChanceInfo = false;
    for (String line : lore) {
      if (line.contains(String.valueOf(this.directUpgradeChance))) {
        hasChanceInfo = true;
        break;
      }
    }

    if (!hasChanceInfo) {
      lore.add("§7成功几率: §e" + this.directUpgradeChance + "%");
    }

    meta.setLore(lore);

    // 添加发光效果
    if (this.config.getBoolean("items.direct_upgrade.glow", true)) {
      meta.addEnchant(Enchantment.DURABILITY, 1, true);
    }

    directItem.setItemMeta(meta);
    directItem.setAmount(amount);

    return directItem;
  }

  /**
   * 从配置文件创建突破石
   *
   * @param level  突破等级
   * @param chance 成功几率
   * @param amount 数量
   * @return 突破石物品
   */
  private ItemStack createBreakthroughStone(int level, int chance, int amount) {
    // 从配置文件获取材质
    String materialName = this.config.getString("items.breakthrough_stone.material", "COAL_BLOCK");
    Material material;
    try {
      material = Material.valueOf(materialName.toUpperCase());
    } catch (IllegalArgumentException e) {
      material = Material.COAL_BLOCK; // 默认使用煤块
    }

    // 创建物品
    ItemStack breakthroughItem = new ItemStack(material);
    ItemMeta meta = breakthroughItem.getItemMeta();

    // 从配置文件获取突破石需要的装备等级
    int requiredLevel = 0;
    if (this.config.contains("breakthrough.levels." + level)) {
      requiredLevel = this.config.getInt("breakthrough.levels." + level);
    } else {
      // 如果配置文件中没有设置，则使用默认规则：需要前一级
      requiredLevel = level - 1;
    }

    // 获取稀有度文本
    String rarityText = "";
    if (this.config.contains("items.breakthrough_stone.rarity_text." + level)) {
      rarityText = this.config.getString("items.breakthrough_stone.rarity_text." + level);
    } else if (level <= 30) {
      rarityText = this.config.getString("items.breakthrough_stone.rarity_text.30", "§9稀有");
    } else if (level <= 50) {
      rarityText = this.config.getString("items.breakthrough_stone.rarity_text.50", "§d史诗");
    } else if (level <= 70) {
      rarityText = this.config.getString("items.breakthrough_stone.rarity_text.70", "§6传说");
    } else if (level <= 100) {
      rarityText = this.config.getString("items.breakthrough_stone.rarity_text.100", "§c神级至尊");
    } else {
      rarityText = this.config.getString("items.breakthrough_stone.rarity_text.default", "§4§l无上至尊");
    }

    // 几率文本
    String chanceText = "";
    if (chance != 100) {
      chanceText = " §e[" + chance + "%几率]";
    }

    // 从配置文件获取名称格式并替换占位符
    String nameFormat = this.config.getString("items.breakthrough_stone.name", "§a【§b强化突破石§a】 - {rarity}");
    String name = nameFormat.replace("{level}", String.valueOf(level))
        .replace("{rarity}", rarityText)
        .replace("{chance}", chanceText);
    meta.setDisplayName(name);

    // 从配置文件获取描述并替换占位符
    List<String> configLore = this.config.getStringList("items.breakthrough_stone.lore");
    List<String> lore = new ArrayList<>();

    // 调试信息
    if (this.config.getBoolean("style.debug_logs", false)) {
      logToConsole("创建突破石 - 等级: " + level + ", 几率: " + chance);
      logToConsole("配置文件中的lore行数: " + configLore.size());
      for (int i = 0; i < configLore.size(); i++) {
        logToConsole("Lore行 " + i + ": " + configLore.get(i));
      }
    }

    for (String line : configLore) {
      // 移除所有颜色代码，以便于检查是否包含占位符
      String cleanLine = line.replaceAll("§[0-9a-fk-or]", "");

      // 处理几率文本行 - 使用包含检查而不是完全相等
      if (cleanLine.contains("{chance_text}")) {
        if (this.config.getBoolean("style.debug_logs", false)) {
          logToConsole("找到{chance_text}占位符，原始行: " + line);
        }

        if (chance != 100) {
          lore.add("§7成功几率: §e" + chance + "%");
        } else {
          // 如果几率是100%，添加成功率100%的文本
          lore.add("§7成功几率: §a100%");
        }
        continue;
      }

      // 替换其他占位符
      String processedLine = line.replace("{level}", String.valueOf(level))
          .replace("{required_level}", String.valueOf(requiredLevel))
          .replace("{chance}", String.valueOf(chance));

      // 获取失败惩罚配置
      int downgradeChance = this.config.getInt("breakthrough.failure_penalty.downgrade_chance", 70);
      int downgradeLevels = this.config.getInt("breakthrough.failure_penalty.downgrade_levels", 1);

      // 替换失败惩罚占位符
      processedLine = processedLine.replace("{downgrade_chance}", String.valueOf(downgradeChance))
          .replace("{downgrade_levels}", String.valueOf(downgradeLevels));

      // 再次检查处理后的行，确保没有遗漏的{chance_text}占位符
      String cleanProcessedLine = processedLine.replaceAll("§[0-9a-fk-or]", "");
      if (cleanProcessedLine.contains("{chance_text}")) {
        if (this.config.getBoolean("style.debug_logs", false)) {
          logToConsole("在处理后的行中找到{chance_text}占位符: " + processedLine);
        }

        // 直接替换占位符

        if (chance != 100) {
          processedLine = processedLine.replace("{chance_text}", "§7成功几率: §e" + chance + "%");
        } else {
          processedLine = processedLine.replace("{chance_text}", "§7成功几率: §a100%");
        }
      }

      lore.add(processedLine);
    }

    meta.setLore(lore);

    // 添加发光效果和存储突破石等级
    // 使用锋利附魔(Enchantment.DAMAGE_ALL)存储突破石等级
    meta.addEnchant(Enchantment.DAMAGE_ALL, level, true);

    // 存储成功几率到NBT数据
    // 使用耐久附魔(Enchantment.DURABILITY)存储成功几率
    if (chance != 100) {
      meta.addEnchant(Enchantment.DURABILITY, chance, true);
    }

    // 添加其他附魔以产生发光效果（如果没有其他附魔）
    if (this.config.getBoolean("items.breakthrough_stone.glow", true) &&
        !meta.hasEnchants()) {
      meta.addEnchant(Enchantment.PROTECTION_ENVIRONMENTAL, 1, true);
    }

    breakthroughItem.setItemMeta(meta);
    breakthroughItem.setAmount(amount);

    return breakthroughItem;
  }

  /**
   * 创建突破石的简化方法，使用默认参数
   *
   * @param level 突破等级
   * @return 突破石物品
   */
  private ItemStack createBreakthroughStone(int level) {
    return createBreakthroughStone(level, 100, 1);
  }

  /**
   * 从配置文件创建强化棒
   *
   * @param level  提升等级
   * @param chance 成功几率
   * @param amount 数量
   * @return 强化棒物品
   */
  private ItemStack createEnhancementRod(int level, int chance, int amount) {
    // 从配置文件获取材质
    String materialName = this.config.getString("items.enhancement_rod.material", "BLAZE_ROD");
    Material material;
    try {
      material = Material.valueOf(materialName.toUpperCase());
    } catch (IllegalArgumentException e) {
      material = Material.BLAZE_ROD; // 默认使用烈焰棒
    }

    // 创建物品
    ItemStack rodItem = new ItemStack(material);
    ItemMeta meta = rodItem.getItemMeta();

    // 获取失败惩罚配置
    int downgradeChance = this.config.getInt("items.enhancement_rod.failure_penalty.downgrade_chance", 70);
    int downgradeLevels = this.config.getInt("items.enhancement_rod.failure_penalty.downgrade_levels", 1);
    int destroyChance = this.config.getInt("items.enhancement_rod.failure_penalty.destroy_chance", 0);

    // 设置物品名称
    String nameFormat = this.config.getString("items.enhancement_rod.name", "§a【§e强化棒§a】 §6+{level}");
    String name = nameFormat.replace("{level}", String.valueOf(level))
        .replace("{chance}", String.valueOf(chance));
    meta.setDisplayName(name);

    // 设置物品描述
    List<String> lore = new ArrayList<>();
    for (String loreLine : this.config.getStringList("items.enhancement_rod.lore")) {
      lore.add(loreLine.replace("{level}", String.valueOf(level))
          .replace("{chance}", String.valueOf(chance))
          .replace("{downgrade_chance}", String.valueOf(downgradeChance))
          .replace("{downgrade_levels}", String.valueOf(downgradeLevels))
          .replace("{destroy_chance}", String.valueOf(destroyChance)));
    }
    meta.setLore(lore);

    // 使用锋利附魔(Enchantment.DAMAGE_ALL)存储提升等级
    meta.addEnchant(Enchantment.DAMAGE_ALL, level, true);

    // 使用耐久附魔(Enchantment.DURABILITY)存储成功几率
    if (chance != 100) {
      meta.addEnchant(Enchantment.DURABILITY, chance, true);
    }

    // 添加其他附魔以产生发光效果（如果没有其他附魔）
    if (this.config.getBoolean("items.enhancement_rod.glow", true) &&
        !meta.hasEnchants()) {
      meta.addEnchant(Enchantment.PROTECTION_ENVIRONMENTAL, 1, true);
    }

    rodItem.setItemMeta(meta);
    rodItem.setAmount(amount);

    return rodItem;
  }

  /**
   * 创建强化棒的简化方法，使用默认参数
   *
   * @param level 提升等级
   * @return 强化棒物品
   */
  private ItemStack createEnhancementRod(int level) {
    int defaultChance = this.config.getInt("items.enhancement_rod.default_chance", 80);
    return createEnhancementRod(level, defaultChance, 1);
  }

  /**
   * 检查熔炉是否属于强化系统
   * 检查燃料类型、保存的燃料信息和装备lore来判断
   *
   * @param furnace 熔炉
   * @return 如果是强化系统的熔炉返回true，否则返回false
   */
  private boolean isEnhancementFurnace(Furnace furnace) {
    if (furnace == null) {
      return false;
    }

    FurnaceInventory inventory = furnace.getInventory();
    ItemStack fuel = inventory.getFuel();
    ItemStack smelt = inventory.getSmelting();
    int hash = furnace.getBlock().hashCode();

    // 优先检查燃料类型来判断熔炉归属，避免与淬炼系统冲突

    // 1. 检查当前燃料是否是强化系统的物品
    if (fuel != null && fuel.hasItemMeta() && fuel.getItemMeta().hasDisplayName()) {
      String fuelName = fuel.getItemMeta().getDisplayName();

      // 检查是否是强化石（精确匹配）
      if (fuelName.equals(this.normalItem.getItemMeta().getDisplayName()) ||
          fuelName.equals(this.luckItem.getItemMeta().getDisplayName()) ||
          fuelName.equals(this.safeItem.getItemMeta().getDisplayName()) ||
          fuelName.equals(this.vipItem.getItemMeta().getDisplayName()) ||
          fuelName.equals(this.adminItem.getItemMeta().getDisplayName())) {
        return true;
      }

      // 检查是否是强化突破石（精确匹配，避免与淬炼系统冲突）
      if (fuelName.contains("强化突破石") && !fuelName.contains("淬炼")) {
        return true;
      }

      // 检查是否是强化直升符咒（精确匹配，避免与淬炼系统冲突）
      if (fuelName.contains("直升符咒") && !fuelName.contains("淬炼")) {
        return true;
      }

      // 检查是否是强化棒（精确匹配，避免与淬炼系统冲突）
      if (fuelName.contains("强化棒") && !fuelName.contains("淬炼")) {
        return true;
      }
    }

    // 2. 检查是否有保存的强化系统燃料信息（熔炉工作时燃料可能已被消耗）
    ItemStack savedFuel = this.fuelItem.get(hash);
    if (savedFuel != null && savedFuel.hasItemMeta() && savedFuel.getItemMeta().hasDisplayName()) {
      String fuelName = savedFuel.getItemMeta().getDisplayName();

      // 检查是否是强化石（精确匹配）
      if (fuelName.equals(this.normalItem.getItemMeta().getDisplayName()) ||
          fuelName.equals(this.luckItem.getItemMeta().getDisplayName()) ||
          fuelName.equals(this.safeItem.getItemMeta().getDisplayName()) ||
          fuelName.equals(this.vipItem.getItemMeta().getDisplayName()) ||
          fuelName.equals(this.adminItem.getItemMeta().getDisplayName())) {
        return true;
      }

      // 检查是否是强化突破石（精确匹配，避免与淬炼系统冲突）
      if (fuelName.contains("强化突破石") && !fuelName.contains("淬炼")) {
        return true;
      }

      // 检查是否是强化直升符咒（精确匹配，避免与淬炼系统冲突）
      if (fuelName.contains("直升符咒") && !fuelName.contains("淬炼")) {
        return true;
      }

      // 检查是否是强化棒（精确匹配，避免与淬炼系统冲突）
      if (fuelName.contains("强化棒") && !fuelName.contains("淬炼")) {
        return true;
      }
    }

    // 注意：移除装备lore检测，完全依靠燃料类型来判断熔炉归属
    // 这样可以避免既有淬炼又有强化的装备导致两个系统都认为熔炉属于自己

    return false;
  }
}

/* 关闭之前打开的多行注释 */