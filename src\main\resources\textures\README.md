# 装备贴图文件夹

这个文件夹包含插件使用的装备贴图文件。插件支持**自动识别**贴图文件！

## 自动识别功能

插件会自动识别所有放在贴图文件夹中的图片文件，并尝试匹配对应的装备材质。

### 支持的文件格式
- PNG（推荐）
- JPG/JPEG
- GIF

### 文件命名规则

文件名应该与 Minecraft 的材质名称对应，支持以下格式：

**装备类：**
- `diamond_helmet.png` - 钻石头盔
- `diamond_chestplate.png` - 钻石胸甲
- `diamond_leggings.png` - 钻石护腿
- `diamond_boots.png` - 钻石靴子
- `iron_helmet.png` - 铁头盔
- `leather_boots.png` - 皮革靴子
- 等等...

**武器类：**
- `diamond_sword.png` - 钻石剑
- `iron_sword.png` - 铁剑
- `bow.png` - 弓
- 等等...

**工具类：**
- `diamond_pickaxe.png` - 钻石镐
- `iron_axe.png` - 铁斧
- 等等...

### 命名变体支持

系统支持多种命名变体：
- `diamond_helmet.png`
- `diamond-helmet.png`
- `DIAMOND_HELMET.png`

## 使用方法

### 方法1：自动识别（推荐）
1. 将贴图文件放到 `plugins/Cuilian/textures/` 文件夹
2. 按照上述命名规则命名文件
3. 重启插件或使用重载命令
4. 插件会自动识别并加载贴图

### 方法2：打包到插件
1. 将贴图文件放到 `src/main/resources/textures/` 文件夹
2. 重新编译插件
3. 插件启动时会自动复制到数据文件夹

## 贴图要求

- **格式**：PNG（推荐）、JPG、GIF
- **建议尺寸**：16x16 到 128x128 像素
- **显示尺寸**：自动缩放到 64x64 像素
- **文件大小**：建议小于 1MB

## 日志信息

插件启动时会在控制台显示：
```
[Cuilian] 初始化装备贴图管理器...
[Cuilian] 已注册 XX 个装备贴图映射
[Cuilian] 自动发现贴图: diamond_helmet.png -> DIAMOND_HELMET
[Cuilian] 自动添加了 X 个贴图映射
[Cuilian] 成功加载贴图: diamond_helmet.png -> DIAMOND_HELMET
[Cuilian] 贴图加载完成: X/Y
[Cuilian] 装备贴图管理器初始化完成！
```

## 故障排除

### 贴图没有显示
1. 检查文件名是否正确
2. 检查文件格式是否支持
3. 查看控制台日志中的错误信息
4. 确认文件没有损坏

### 找不到对应材质
1. 检查文件名是否与 Minecraft 材质名称匹配
2. 尝试使用标准的下划线命名格式
3. 查看控制台日志中的自动发现信息

## 支持的所有物品

插件现在支持**完整的 Minecraft 物品贴图集合**！根据您提供的贴图文件，以下物品都会自动识别：

### 装备类

**钻石装备**：diamond_helmet, diamond_chestplate, diamond_leggings, diamond_boots, diamond_sword, diamond_pickaxe, diamond_axe, diamond_shovel, diamond_hoe

**铁装备**：iron_helmet, iron_chestplate, iron_leggings, iron_boots, iron_sword, iron_pickaxe, iron_axe, iron_shovel, iron_hoe

**金装备**：golden_helmet, golden_chestplate, golden_leggings, golden_boots, golden_sword, golden_pickaxe, golden_axe, golden_shovel, golden_hoe

**皮革装备**：leather_helmet, leather_chestplate, leather_leggings, leather_boots

**锁链装备**：chainmail_helmet, chainmail_chestplate, chainmail_leggings, chainmail_boots

**石制工具**：stone_sword, stone_pickaxe, stone_axe, stone_shovel, stone_hoe

**木制工具**：wooden_sword, wooden_pickaxe, wooden_axe, wooden_shovel, wooden_hoe

**下界合金装备**（1.16+）：netherite_helmet, netherite_chestplate, netherite_leggings, netherite_boots, netherite_sword, netherite_pickaxe, netherite_axe, netherite_shovel, netherite_hoe

### 武器和工具

**特殊武器**：bow, crossbow_standby, trident, fishing_rod

**实用工具**：flint_and_steel, shears, compass_00, clock_00

### 食物类

apple, bread, cooked_beef, cooked_chicken, cooked_cod, golden_apple, golden_carrot, cake, cookie, melon_slice, carrot, potato, baked_potato, beetroot, beetroot_soup

### 材料类

diamond, emerald, gold_ingot, iron_ingot, coal, redstone, lapis_lazuli, quartz, stick, string, feather, leather, paper, book, slime_ball, ender_pearl, blaze_rod, ghast_tear, nether_star, netherite_ingot, netherite_scrap

### 药水和附魔

potion, glass_bottle, enchanted_book, experience_bottle, lingering_potion, splash_potion, dragon_breath

### 箭矢和投掷物

arrow, spectral_arrow, tipped_arrow_base, snowball, egg, ender_pearl

### 容器和工具

bucket, water_bucket, lava_bucket, milk_bucket, filled_map, name_tag, saddle

### 特殊物品

**1.9+**：shield, elytra, end_crystal, totem_of_undying

**1.13+**：turtle_helmet, heart_of_the_sea, nautilus_shell, phantom_membrane, chorus_fruit

**还有更多**：根据您的贴图文件，插件支持数百种物品！

## 完整支持列表

您的贴图文件夹包含了完整的 Minecraft 物品贴图集合，包括：
- 所有装备和工具
- 所有食物和材料
- 所有药水和附魔物品
- 所有装饰和实用物品
- 新版本的特殊物品

**总计支持 300+ 种物品贴图！**

## 🎬 动画贴图支持

### .mcmeta 配置文件

插件现在支持 Minecraft 材质包的 `.mcmeta` 动画配置文件！

#### 附魔光效动画

如果您有 `enchanted_item_glint.png.mcmeta` 文件，插件会自动：

1. **读取动画配置**：解析帧时间、插值等设置
2. **提取动画帧**：从垂直排列的贴图中分离各帧
3. **播放动画**：按照配置的时间间隔循环播放

#### .mcmeta 文件格式示例

```json
{
  "animation": {
    "frametime": 8,
    "interpolate": true
  }
}
```

**参数说明：**
- `frametime`: 每帧显示时间（游戏刻，1游戏刻 ≈ 50ms）
- `interpolate`: 是否在帧之间进行平滑过渡

#### 使用方法

1. **放置文件**：
   - `enchanted_item_glint.png` - 附魔光效贴图
   - `enchanted_item_glint.png.mcmeta` - 动画配置

2. **贴图格式**：
   - 动画贴图应该是垂直排列的帧
   - 每帧应该是正方形（宽度 = 高度/帧数）

3. **自动识别**：
   - 插件启动时自动检测 .mcmeta 文件
   - 如果找到配置文件，启用动画效果
   - 如果没有配置文件，使用静态显示

#### 日志输出

插件会在控制台显示动画加载信息：

```
[Cuilian] 成功加载附魔光效贴图: enchanted_item_glint
[Cuilian] 找到附魔光效动画配置: enchanted_item_glint.png.mcmeta
[Cuilian] 附魔光效帧时间设置为: 8 游戏刻
[Cuilian] 成功提取 16 个附魔光效动画帧
[Cuilian] 附魔光效动画已启用，共 16 帧
```

### 其他动画贴图

虽然目前主要支持附魔光效动画，但系统已经为将来支持更多动画贴图做好了准备。

### 兼容性

- ✅ **完全兼容** Minecraft 官方材质包的 .mcmeta 格式
- ✅ **向后兼容** 没有 .mcmeta 文件的静态贴图
- ✅ **自动降级** 如果动画加载失败，使用程序生成的光效
