package Intensify;

/**
 * 玩家战力数据类
 */
public class PowerData {
    private String playerName;
    private String uuid;
    private int power;

    /**
     * 创建玩家战力数据
     * 
     * @param playerName 玩家名称
     * @param uuid 玩家UUID
     * @param power 战力值
     */
    public PowerData(String playerName, String uuid, int power) {
        this.playerName = playerName;
        this.uuid = uuid;
        this.power = power;
    }

    /**
     * 获取玩家名称
     * 
     * @return 玩家名称
     */
    public String getPlayerName() {
        return playerName;
    }

    /**
     * 设置玩家名称
     * 
     * @param playerName 玩家名称
     */
    public void setPlayerName(String playerName) {
        this.playerName = playerName;
    }

    /**
     * 获取玩家UUID
     * 
     * @return 玩家UUID
     */
    public String getUuid() {
        return uuid;
    }

    /**
     * 设置玩家UUID
     * 
     * @param uuid 玩家UUID
     */
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    /**
     * 获取战力值
     * 
     * @return 战力值
     */
    public int getPower() {
        return power;
    }

    /**
     * 设置战力值
     * 
     * @param power 战力值
     */
    public void setPower(int power) {
        this.power = power;
    }
}
