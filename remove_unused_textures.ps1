# PowerShell脚本：删除Minecraft 1.8.8插件中未使用的纹理文件

$texturesPath = "src\main\resources\textures"
$unusedListFile = "unused_textures_list.txt"

Write-Host "开始清理未使用的纹理文件..." -ForegroundColor Green
Write-Host "纹理目录: $texturesPath" -ForegroundColor Yellow

# 检查目录是否存在
if (-not (Test-Path $texturesPath)) {
    Write-Host "错误: 纹理目录不存在: $texturesPath" -ForegroundColor Red
    exit 1
}

# 检查未使用文件列表是否存在
if (-not (Test-Path $unusedListFile)) {
    Write-Host "错误: 未使用文件列表不存在: $unusedListFile" -ForegroundColor Red
    exit 1
}

# 读取未使用文件列表
$unusedFiles = Get-Content $unusedListFile | Where-Object { 
    $_ -and -not $_.StartsWith("#") -and $_.Trim() -ne "" 
}

Write-Host "找到 $($unusedFiles.Count) 个可能未使用的文件" -ForegroundColor Yellow

$deletedCount = 0
$notFoundCount = 0
$totalSize = 0

foreach ($file in $unusedFiles) {
    $file = $file.Trim()
    $filePath = Join-Path $texturesPath $file
    
    if (Test-Path $filePath) {
        $fileInfo = Get-Item $filePath
        $fileSize = $fileInfo.Length
        $totalSize += $fileSize
        
        Write-Host "删除: $file (大小: $([math]::Round($fileSize/1024, 2)) KB)" -ForegroundColor Cyan
        Remove-Item $filePath -Force
        $deletedCount++
    } else {
        Write-Host "文件不存在: $file" -ForegroundColor Gray
        $notFoundCount++
    }
}

Write-Host "`n清理完成!" -ForegroundColor Green
Write-Host "删除文件数: $deletedCount" -ForegroundColor Yellow
Write-Host "未找到文件数: $notFoundCount" -ForegroundColor Yellow
Write-Host "节省空间: $([math]::Round($totalSize/1024/1024, 2)) MB" -ForegroundColor Green

# 显示剩余的纹理文件
$remainingFiles = Get-ChildItem $texturesPath -Filter "*.png" | Sort-Object Name
Write-Host "`n剩余纹理文件 ($($remainingFiles.Count) 个):" -ForegroundColor Green
foreach ($file in $remainingFiles) {
    Write-Host "  $($file.Name)" -ForegroundColor White
}

Write-Host "`n建议重新构建插件以确保更改生效。" -ForegroundColor Yellow
