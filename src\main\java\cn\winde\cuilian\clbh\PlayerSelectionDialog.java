package cn.winde.cuilian.clbh;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.HashSet;
import java.util.concurrent.ConcurrentHashMap;
import javax.imageio.ImageIO;

/**
 * 玩家选择对话框
 * 支持点击在线玩家列表或手动输入玩家名称
 * 自动更新在线玩家列表
 */
public class PlayerSelectionDialog extends JDialog {
    private String selectedPlayer = null;
    private JList<String> onlinePlayersList;
    private JTextField playerNameField;
    private DefaultListModel<String> listModel;
    private Timer autoUpdateTimer;
    private Set<String> lastPlayerSet = new HashSet<>();

    // 静态实例跟踪，用于自动更新
    private static Set<PlayerSelectionDialog> openDialogs = new HashSet<>();

    // 头颅图标缓存
    private static ConcurrentHashMap<String, ImageIcon> headCache = new ConcurrentHashMap<>();
    private static final ImageIcon DEFAULT_HEAD_ICON = createDefaultHeadIcon();

    public PlayerSelectionDialog(JFrame parent) {
        super(parent, "选择玩家", true);
        initializeComponents();
        loadOnlinePlayers();
        startAutoUpdate();
        setLocationRelativeTo(parent);

        // 添加到打开的对话框列表
        openDialogs.add(this);

        // 添加窗口关闭监听器
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosed(WindowEvent e) {
                stopAutoUpdate();
                openDialogs.remove(PlayerSelectionDialog.this);
            }
        });
    }

    private void initializeComponents() {
        setLayout(new BorderLayout());
        setSize(500, 400);
        setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);

        // 创建主面板
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // 在线玩家列表面板
        JPanel onlinePanel = createOnlinePlayersPanel();
        mainPanel.add(onlinePanel, BorderLayout.CENTER);

        // 手动输入面板
        JPanel inputPanel = createInputPanel();
        mainPanel.add(inputPanel, BorderLayout.SOUTH);

        add(mainPanel, BorderLayout.CENTER);

        // 底部按钮面板
        JPanel buttonPanel = createButtonPanel();
        add(buttonPanel, BorderLayout.SOUTH);
    }

    private JPanel createOnlinePlayersPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(),
                "在线玩家列表 (双击选择)",
                TitledBorder.LEFT,
                TitledBorder.TOP,
                new Font("微软雅黑", Font.BOLD, 12)));

        // 创建列表模型和列表
        listModel = new DefaultListModel<>();
        onlinePlayersList = new JList<>(listModel);
        onlinePlayersList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        onlinePlayersList.setFont(new Font("微软雅黑", Font.PLAIN, 12));

        // 设置自定义渲染器显示头颅
        onlinePlayersList.setCellRenderer(new PlayerListCellRenderer());
        onlinePlayersList.setFixedCellHeight(32); // 设置行高以适应头颅图标

        // 添加双击事件监听器
        onlinePlayersList.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    int index = onlinePlayersList.locationToIndex(e.getPoint());
                    if (index >= 0) {
                        selectedPlayer = listModel.getElementAt(index);
                        dispose();
                    }
                }
            }
        });

        // 添加选择事件监听器
        onlinePlayersList.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                String selected = onlinePlayersList.getSelectedValue();
                if (selected != null) {
                    playerNameField.setText(selected);
                }
            }
        });

        JScrollPane scrollPane = new JScrollPane(onlinePlayersList);
        scrollPane.setPreferredSize(new Dimension(450, 200));
        panel.add(scrollPane, BorderLayout.CENTER);

        // 添加手动刷新按钮（备用）
        JPanel refreshPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        JLabel autoUpdateLabel = new JLabel("自动更新中...");
        autoUpdateLabel.setFont(new Font("微软雅黑", Font.PLAIN, 10));
        autoUpdateLabel.setForeground(Color.GRAY);

        JButton refreshButton = new JButton("手动刷新");
        refreshButton.setFont(new Font("微软雅黑", Font.PLAIN, 11));
        refreshButton.setToolTipText("备用功能：手动刷新玩家列表");
        refreshButton.addActionListener(e -> loadOnlinePlayers());

        refreshPanel.add(autoUpdateLabel);
        refreshPanel.add(refreshButton);
        panel.add(refreshPanel, BorderLayout.SOUTH);

        return panel;
    }

    private JPanel createInputPanel() {
        JPanel panel = new JPanel(new BorderLayout(5, 5));
        panel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(),
                "手动输入玩家名称 (支持离线玩家)",
                TitledBorder.LEFT,
                TitledBorder.TOP,
                new Font("微软雅黑", Font.BOLD, 12)));

        JLabel label = new JLabel("玩家名称:");
        label.setFont(new Font("微软雅黑", Font.PLAIN, 12));

        playerNameField = new JTextField();
        playerNameField.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        playerNameField.setPreferredSize(new Dimension(300, 25));

        // 添加回车键监听器
        playerNameField.addActionListener(e -> confirmSelection());

        panel.add(label, BorderLayout.WEST);
        panel.add(playerNameField, BorderLayout.CENTER);

        return panel;
    }

    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout());

        JButton confirmButton = new JButton("确认");
        confirmButton.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        confirmButton.addActionListener(e -> confirmSelection());

        JButton cancelButton = new JButton("取消");
        cancelButton.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        cancelButton.addActionListener(e -> {
            selectedPlayer = null;
            dispose();
        });

        panel.add(confirmButton);
        panel.add(cancelButton);

        return panel;
    }

    private void loadOnlinePlayers() {
        listModel.clear();

        try {
            Collection<? extends Player> onlinePlayers = Bukkit.getOnlinePlayers();
            List<String> playerNames = new ArrayList<>();

            for (Player player : onlinePlayers) {
                playerNames.add(player.getName());
            }

            // 按字母顺序排序
            playerNames.sort(String.CASE_INSENSITIVE_ORDER);

            for (String name : playerNames) {
                listModel.addElement(name);
            }

            // 更新标题显示在线玩家数量
            // 找到包含TitledBorder的面板
            Container parent = onlinePlayersList.getParent(); // JScrollPane
            if (parent != null) {
                parent = parent.getParent(); // 包含TitledBorder的JPanel
                if (parent instanceof JPanel && ((JPanel) parent).getBorder() instanceof TitledBorder) {
                    TitledBorder border = (TitledBorder) ((JPanel) parent).getBorder();
                    border.setTitle("在线玩家列表 (双击选择) - 共 " + playerNames.size() + " 人在线");
                    parent.repaint();
                }
            }

        } catch (Exception e) {
            JOptionPane.showMessageDialog(this,
                    "获取在线玩家列表失败: " + e.getMessage(),
                    "错误",
                    JOptionPane.ERROR_MESSAGE);
        }
    }

    private void confirmSelection() {
        String inputName = playerNameField.getText().trim();
        if (!inputName.isEmpty()) {
            selectedPlayer = inputName;
            dispose();
        } else {
            JOptionPane.showMessageDialog(this,
                    "请选择一个在线玩家或输入玩家名称！",
                    "提示",
                    JOptionPane.WARNING_MESSAGE);
        }
    }

    public String getSelectedPlayer() {
        return selectedPlayer;
    }

    /**
     * 启动自动更新定时器（备用机制）
     */
    private void startAutoUpdate() {
        // 降低定时器频率，主要依靠事件驱动更新
        autoUpdateTimer = new Timer(5000, e -> checkAndUpdatePlayerList()); // 每5秒检查一次（备用）
        autoUpdateTimer.start();
    }

    /**
     * 停止自动更新定时器
     */
    private void stopAutoUpdate() {
        if (autoUpdateTimer != null) {
            autoUpdateTimer.stop();
            autoUpdateTimer = null;
        }
    }

    /**
     * 检查并更新玩家列表（只在有变化时更新）
     */
    private void checkAndUpdatePlayerList() {
        try {
            Collection<? extends Player> onlinePlayers = Bukkit.getOnlinePlayers();
            Set<String> currentPlayerSet = new HashSet<>();

            for (Player player : onlinePlayers) {
                currentPlayerSet.add(player.getName());
            }

            // 检查是否有变化
            if (!currentPlayerSet.equals(lastPlayerSet)) {
                // 有变化，更新列表
                SwingUtilities.invokeLater(() -> {
                    String selectedValue = onlinePlayersList.getSelectedValue();
                    loadOnlinePlayers();

                    // 尝试保持之前的选择
                    if (selectedValue != null && listModel.contains(selectedValue)) {
                        onlinePlayersList.setSelectedValue(selectedValue, true);
                    }
                });

                lastPlayerSet = currentPlayerSet;
            }
        } catch (Exception e) {
            // 静默处理异常，避免影响用户体验
        }
    }

    /**
     * 静态方法：通知所有打开的对话框更新玩家列表
     * 可以从外部调用，比如在玩家加入/退出事件中
     */
    public static void notifyPlayerListChanged() {
        // 使用SwingUtilities确保在EDT线程中执行
        SwingUtilities.invokeLater(() -> {
            // 创建副本避免并发修改异常
            Set<PlayerSelectionDialog> dialogsCopy = new HashSet<>(openDialogs);

            for (PlayerSelectionDialog dialog : dialogsCopy) {
                if (dialog.isDisplayable() && dialog.isVisible()) {
                    // 强制立即更新，不依赖变化检测
                    dialog.forceUpdatePlayerList();
                }
            }
        });
    }

    /**
     * 强制更新玩家列表（不检查变化）
     */
    private void forceUpdatePlayerList() {
        try {
            String selectedValue = onlinePlayersList.getSelectedValue();
            loadOnlinePlayers();

            // 尝试保持之前的选择
            if (selectedValue != null && listModel.contains(selectedValue)) {
                onlinePlayersList.setSelectedValue(selectedValue, true);
            }

            // 更新缓存的玩家集合
            Collection<? extends org.bukkit.entity.Player> onlinePlayers = org.bukkit.Bukkit.getOnlinePlayers();
            lastPlayerSet.clear();
            for (org.bukkit.entity.Player player : onlinePlayers) {
                lastPlayerSet.add(player.getName());
            }
        } catch (Exception e) {
            // 静默处理异常，避免影响用户体验
        }
    }

    /**
     * 创建默认头颅图标
     */
    private static ImageIcon createDefaultHeadIcon() {
        BufferedImage defaultHead = new BufferedImage(24, 24, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = defaultHead.createGraphics();

        // 绘制默认的史蒂夫头颅
        g2d.setColor(new Color(139, 69, 19)); // 棕色头发
        g2d.fillRect(0, 0, 24, 8);

        g2d.setColor(new Color(255, 220, 177)); // 肤色
        g2d.fillRect(0, 8, 24, 16);

        // 眼睛
        g2d.setColor(Color.BLACK);
        g2d.fillRect(6, 12, 2, 2);
        g2d.fillRect(16, 12, 2, 2);

        // 嘴巴
        g2d.fillRect(10, 18, 4, 1);

        g2d.dispose();
        return new ImageIcon(defaultHead);
    }

    /**
     * 获取玩家头颅图标
     */
    private static ImageIcon getPlayerHeadIcon(String playerName) {
        // 先检查缓存
        ImageIcon cached = headCache.get(playerName);
        if (cached != null) {
            return cached;
        }

        // 异步加载头颅
        SwingUtilities.invokeLater(() -> loadPlayerHeadAsync(playerName));

        // 返回默认图标
        return DEFAULT_HEAD_ICON;
    }

    /**
     * 异步加载玩家头颅
     */
    private static void loadPlayerHeadAsync(String playerName) {
        new Thread(() -> {
            try {
                // 使用 minotar.net 获取玩家头颅
                String urlString = "https://minotar.net/avatar/" + playerName + "/24";
                URL url = new URL(urlString);
                BufferedImage headImage = ImageIO.read(url);

                if (headImage != null) {
                    ImageIcon headIcon = new ImageIcon(headImage);
                    headCache.put(playerName, headIcon);

                    // 通知所有打开的对话框更新显示
                    SwingUtilities.invokeLater(() -> {
                        for (PlayerSelectionDialog dialog : openDialogs) {
                            if (dialog.isDisplayable() && dialog.isVisible()) {
                                dialog.onlinePlayersList.repaint();
                            }
                        }
                    });
                }
            } catch (IOException e) {
                // 加载失败，使用默认图标
                headCache.put(playerName, DEFAULT_HEAD_ICON);
            }
        }).start();
    }

    /**
     * 自定义列表单元格渲染器，显示玩家头颅和名称
     */
    private static class PlayerListCellRenderer extends DefaultListCellRenderer {
        @Override
        public Component getListCellRendererComponent(JList<?> list, Object value, int index,
                boolean isSelected, boolean cellHasFocus) {

            super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);

            if (value instanceof String) {
                String playerName = (String) value;

                // 设置玩家头颅图标
                ImageIcon headIcon = getPlayerHeadIcon(playerName);
                setIcon(headIcon);

                // 设置文本
                setText(playerName);

                // 设置字体
                setFont(new Font("微软雅黑", Font.PLAIN, 12));

                // 设置边距
                setBorder(BorderFactory.createEmptyBorder(4, 8, 4, 8));
            }

            return this;
        }
    }
}
