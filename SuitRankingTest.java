import javax.swing.*;
import java.awt.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 测试套装排行榜颜色代码渲染
 */
public class SuitRankingTest {
    
    /**
     * 自定义的列表单元格渲染器，用于处理Minecraft颜色代码
     */
    private static class ColorCodeListCellRenderer extends DefaultListCellRenderer {
        @Override
        public Component getListCellRendererComponent(
                JList<?> list,
                Object value,
                int index,
                boolean isSelected,
                boolean cellHasFocus) {

            super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);

            if (value != null) {
                String text = value.toString();
                
                // 移除颜色代码并设置为显示文本
                String cleanText = removeColorCodes(text);
                setText(cleanText);
                
                // 解析颜色代码并应用颜色
                Color textColor = parseMinecraftColor(text);
                if (textColor != null && !isSelected) {
                    setForeground(textColor);
                }
                
                // 解析格式代码并应用样式
                boolean isBold = text.contains("§l");
                boolean isItalic = text.contains("§o");
                
                int style = Font.PLAIN;
                if (isBold) style |= Font.BOLD;
                if (isItalic) style |= Font.ITALIC;
                
                setFont(getFont().deriveFont(style));
            }

            return this;
        }

        /**
         * 移除Minecraft颜色代码
         */
        private static String removeColorCodes(String text) {
            if (text == null) return "";
            return text.replaceAll("§[0-9a-fk-or]", "");
        }

        /**
         * 解析Minecraft颜色代码并返回对应的Java颜色
         */
        private static Color parseMinecraftColor(String text) {
            if (text == null) return null;
            
            // 查找最后一个颜色代码
            Pattern pattern = Pattern.compile("§([0-9a-f])");
            Matcher matcher = pattern.matcher(text);
            
            String lastColorCode = null;
            while (matcher.find()) {
                lastColorCode = matcher.group(1);
            }
            
            if (lastColorCode == null) return null;
            
            // Minecraft颜色代码映射
            switch (lastColorCode) {
                case "0": return new Color(0, 0, 0);        // 黑色
                case "1": return new Color(0, 0, 170);      // 深蓝色
                case "2": return new Color(0, 170, 0);      // 深绿色
                case "3": return new Color(0, 170, 170);    // 深青色
                case "4": return new Color(170, 0, 0);      // 深红色
                case "5": return new Color(170, 0, 170);    // 紫色
                case "6": return new Color(255, 170, 0);    // 金色
                case "7": return new Color(170, 170, 170);  // 灰色
                case "8": return new Color(85, 85, 85);     // 深灰色
                case "9": return new Color(85, 85, 255);    // 蓝色
                case "a": return new Color(85, 255, 85);    // 绿色
                case "b": return new Color(85, 255, 255);   // 青色
                case "c": return new Color(255, 85, 85);    // 红色
                case "d": return new Color(255, 85, 255);   // 粉色
                case "e": return new Color(255, 255, 85);   // 黄色
                case "f": return new Color(255, 255, 255);  // 白色
                default: return null;
            }
        }
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            JFrame frame = new JFrame("套装使用排行榜测试");
            frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
            frame.setSize(400, 300);
            
            // 创建测试数据 - 模拟套装使用排行榜
            String[] testData = {
                "§6§l神武套装 - 15人使用",
                "§a§l星辰套装 - 12人使用", 
                "§c§l暗影套装 - 8人使用",
                "§e§l黄金套装 - 6人使用",
                "§9§l海洋套装 - 4人使用",
                "§d§l粉色套装 - 2人使用",
                "普通套装 - 1人使用"
            };
            
            DefaultListModel<String> model = new DefaultListModel<>();
            for (String item : testData) {
                model.addElement(item);
            }
            
            JList<String> list = new JList<>(model);
            list.setCellRenderer(new ColorCodeListCellRenderer());
            list.setFont(new Font("微软雅黑", Font.PLAIN, 12));
            
            JScrollPane scrollPane = new JScrollPane(list);
            frame.add(scrollPane, BorderLayout.CENTER);
            
            JLabel titleLabel = new JLabel("套装使用排行榜", JLabel.CENTER);
            titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 16));
            frame.add(titleLabel, BorderLayout.NORTH);
            
            frame.setVisible(true);
            
            System.out.println("套装排行榜颜色代码渲染测试启动成功！");
            System.out.println("应该看到：");
            System.out.println("- 神武套装显示为金色粗体");
            System.out.println("- 星辰套装显示为绿色粗体");
            System.out.println("- 暗影套装显示为红色粗体");
            System.out.println("- 等等...");
            System.out.println("- 但是不显示颜色代码文本（§6§l等）");
        });
    }
}
