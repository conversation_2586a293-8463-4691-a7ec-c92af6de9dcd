/*
 * Decompiled with CFR 0.152.
 *
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.Effect
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.Sound
 *  org.bukkit.block.Furnace
 *  org.bukkit.command.Command
 *  org.bukkit.command.CommandExecutor
 *  org.bukkit.command.CommandSender
 *  org.bukkit.configuration.file.FileConfiguration
 *  org.bukkit.configuration.file.YamlConfiguration
 *  org.bukkit.enchantments.Enchantment
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.block.Action
 *  org.bukkit.event.inventory.FurnaceBurnEvent
 *  org.bukkit.event.inventory.FurnaceSmeltEvent
 *  org.bukkit.event.player.PlayerInteractEvent
 *  org.bukkit.inventory.FurnaceRecipe
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.Recipe
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 *  org.bukkit.plugin.java.JavaPlugin
 */
package cn.winde.cuilian;

import cn.winde.cuilian.Damage;
import cn.winde.cuilian.Loadlang;
import cn.winde.cuilian.Playermove;
import cn.winde.cuilian.CuilianTabCompleter;
import cn.winde.cuilian.clbh.Clbh;
import cn.winde.cuilian.clbh.Mygui;
import cn.winde.cuilian.lizi.Msg;
import cn.winde.cuilian.listeners.PlayerJoinLeaveListener;
import cn.winde.cuilian.listeners.InfiniteDurabilityListener;
import cn.winde.cuilian.lizi.ParticleEffect;
import cn.winde.cuilian.lizi.effectlisten;
import cn.winde.cuilian.lizi.PlayerParticlesIntegration;
import cn.winde.cuilian.suit.SuitManager;
import cn.winde.cuilian.tps.TPSMonitor;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.StringTokenizer;
import java.util.regex.Pattern;
import org.bukkit.Bukkit;
import org.bukkit.Effect;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.block.Furnace;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.inventory.FurnaceBurnEvent;
import org.bukkit.event.inventory.FurnaceSmeltEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.FurnaceInventory;
import org.bukkit.inventory.FurnaceRecipe;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.Recipe;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.java.JavaPlugin;

public class Cuilian
        extends JavaPlugin
        implements Listener {
    private static Cuilian instance;
    public static Plugin tx;
    public static HashMap<Integer, Integer> putonggl;
    public static HashMap<Integer, Integer> zhongdenggl;
    public static HashMap<Integer, Integer> gaodenggl;
    public static HashMap<Integer, Integer> wanmeigl;
    public static HashMap<Integer, Integer> wuqishanghai;
    public static HashMap<Integer, Integer> xixue;
    public static HashMap<String, Integer> tzleve;
    public static HashMap<String, Integer> lizi;
    public static HashMap<Integer, Integer> fx;
    public static HashMap<Integer, Integer> js;
    public static HashMap<Integer, Integer> jump;
    public static HashMap<Integer, Integer> hujiafangyu;
    public static HashMap<String, Integer> playercldj;

    // 淬炼石使用统计
    public static HashMap<String, Integer> stoneUsageToday = new HashMap<>();
    public static String lastResetDate = "";

    /**
     * 获取插件实例
     *
     * @return 插件实例
     */
    public static Cuilian getInstance() {
        return instance;
    }

    /**
     * 记录淬炼石使用
     */
    public static void recordStoneUsage(String stoneType) {
        try {
            // 检查是否需要重置今日统计
            String today = java.time.LocalDate.now().toString();
            if (!today.equals(lastResetDate)) {
                stoneUsageToday.clear();
                lastResetDate = today;
            }

            // 记录使用
            stoneUsageToday.put(stoneType, stoneUsageToday.getOrDefault(stoneType, 0) + 1);
        } catch (Exception e) {
            System.err.println("记录淬炼石使用失败: " + e.getMessage());
        }
    }

    /**
     * 获取今日淬炼石使用统计
     */
    public static HashMap<String, Integer> getTodayStoneUsage() {
        // 检查是否需要重置今日统计
        String today = java.time.LocalDate.now().toString();
        if (!today.equals(lastResetDate)) {
            stoneUsageToday.clear();
            lastResetDate = today;
        }
        return new HashMap<>(stoneUsageToday);
    }

    public static HashMap<Integer, Player> player;
    public static HashMap<Integer, Player> meng;
    public static HashMap<Integer, Player> wan;
    public static HashMap<String, ParticleEffect.OrdinaryColor> color;
    public static HashMap<Integer, ItemStack> fuelItem;
    public static File filess;
    public static File f;
    public static File f5;
    public static File f6;
    public static File f7;
    public static FileConfiguration Weapon;
    public static FileConfiguration Suit;
    public static FileConfiguration Probability;
    public static FileConfiguration moditem;
    public static FileConfiguration config;
    public static YamlConfiguration files;
    public static Mygui setui;
    private int[] list = new int[] { 261, 267, 268, 272, 276, 283, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307,
            308, 309, 310, 311, 312, 313, 314, 315, 316, 317 };

    public Cuilian() {
        putonggl = new HashMap();
        zhongdenggl = new HashMap();
        gaodenggl = new HashMap();
        tzleve = new HashMap();
        wanmeigl = new HashMap();
        wuqishanghai = new HashMap();
        hujiafangyu = new HashMap();
        player = new HashMap();
        fuelItem = new HashMap();
        playercldj = new HashMap();
        xixue = new HashMap();
        jump = new HashMap();
        fx = new HashMap();
        lizi = new HashMap();
        js = new HashMap();
        color = new HashMap();
    }

    public void onEnable() {
        // 设置实例
        instance = this;

        config = this.getConfig();
        this.saveDefaultConfig();
        this.loadyml();
        new effectlisten().runTaskTimer((Plugin) this, 50L, 2L);
        filess = new File(this.getDataFolder(), "config.yml");

        // 注册事件监听器
        this.getServer().getPluginManager().registerEvents((Listener) this, (Plugin) this);
        this.getServer().getPluginManager().registerEvents((Listener) new Playermove(), (Plugin) this);
        this.getServer().getPluginManager().registerEvents((Listener) new Damage(), (Plugin) this);
        this.getServer().getPluginManager().registerEvents((Listener) new PlayerJoinLeaveListener(), (Plugin) this);
        this.getServer().getPluginManager().registerEvents((Listener) new InfiniteDurabilityListener(), (Plugin) this);
        this.getServer().getPluginManager().registerEvents((Listener) new Clbh(), (Plugin) this);
        // 注册装备变更监听器，用于自动更新特效
        this.getServer().getPluginManager().registerEvents((Listener) new ArmorChangeListener(), (Plugin) this);
        // 注册套装显示更新器，用于动态更新套装效果显示
        this.getServer().getPluginManager().registerEvents((Listener) new cn.winde.cuilian.suit.SuitDisplayUpdater(),
                (Plugin) this);
        // 注册游戏内GUI事件监听器
        this.getServer().getPluginManager().registerEvents((Listener) new cn.winde.cuilian.gui.InGameGUIListener(),
                (Plugin) this);
        // 注册套装预览监听器
        this.getServer().getPluginManager().registerEvents(
                (Listener) new cn.winde.cuilian.preview.SuitPreviewListener(),
                (Plugin) this);
        // 注册玩家列表更新监听器
        this.getServer().getPluginManager().registerEvents(
                (Listener) new cn.winde.cuilian.clbh.PlayerListUpdateListener(),
                (Plugin) this);

        // 注册命令执行器
        this.getServer().getPluginCommand("cuilian").setExecutor((CommandExecutor) new Commander());
        // 注册Tab补全器
        this.getServer().getPluginCommand("cuilian").setTabCompleter(new CuilianTabCompleter());
        int i = 0;
        while (i < this.list.length) {
            FurnaceRecipe Furnace2 = new FurnaceRecipe(new ItemStack(Material.getMaterial((int) this.list[i])),
                    Material.COAL);
            Furnace2.setInput(Material.getMaterial((int) this.list[i]));
            this.getServer().addRecipe((Recipe) Furnace2);
            ++i;
        }
        setui = new Mygui();
        Mygui.setui();

        // 启动TPS监控器（如果启用）
        if (config.getBoolean("tps_auto_effect.enabled", false)) {
            TPSMonitor.startMonitoring();
        }

        // 初始化装备贴图管理器
        cn.winde.cuilian.texture.TextureManager.initialize(this);
    }

    public void onDisable() {
        // 停止TPS监控器
        TPSMonitor.stopMonitoring();
        // 清理所有预览状态
        cn.winde.cuilian.preview.SuitPreviewManager.cleanupAll();
        // 清理装备贴图管理器
        cn.winde.cuilian.texture.TextureManager.cleanup();
        setui.dispose();
    }

    public void onLoad() {
    }

    public void loadyml() {
        f = new File(this.getDataFolder(), "Weapon.yml");
        f5 = new File(this.getDataFolder(), "Suit.yml");
        f6 = new File(this.getDataFolder(), "Probability.yml");
        f7 = new File(this.getDataFolder(), "moditem.yml");
        if (!f.exists()) {
            this.getLogger().info("没有找到Weapon.yml, 正在创建");
            this.saveResource("Weapon.yml", true);
        }
        if (!f5.exists()) {
            this.getLogger().info("没有找到套装, 正在创建");
            this.saveResource("Suit.yml", true);
        }
        if (!f6.exists()) {
            this.getLogger().info("没有找到Probability.yml, 正在创建");
            this.saveResource("Probability.yml", true);
        }
        if (!f7.exists()) {
            this.getLogger().info("没有找到moditem.yml, 正在创建");
            this.saveResource("moditem.yml", true);
        }
        Loadlang.loadWeapon((FileConfiguration) YamlConfiguration.loadConfiguration((File) f));
        Weapon = YamlConfiguration.loadConfiguration((File) f);
        this.getLogger().info("攻击伤害加载成功");
        Loadlang.loadProbability((FileConfiguration) YamlConfiguration.loadConfiguration((File) f6));
        Probability = YamlConfiguration.loadConfiguration((File) f6);
        // 加载套装配置
        Suit = YamlConfiguration.loadConfiguration((File) f5);
        this.getLogger().info("套装配置加载成功");
        moditem = YamlConfiguration.loadConfiguration((File) f7);
        this.loadmoditem();
        this.loadclore();

        // 初始化PlayerParticles集成
        PlayerParticlesIntegration.initialize();

        this.getLogger().info("概率加载成功");
        this.getLogger().info("MOD支持列表加载成功");
        this.getLogger().info("淬炼插件加载成功");
    }

    public void loadmoditem() {
    }

    public void loadclore() {
        color.put("红", new ParticleEffect.OrdinaryColor(255, 0, 0));
        color.put("橙", new ParticleEffect.OrdinaryColor(255, 128, 0));
        color.put("黄", new ParticleEffect.OrdinaryColor(255, 255, 0));
        color.put("绿", new ParticleEffect.OrdinaryColor(0, 255, 0));
        color.put("蓝", new ParticleEffect.OrdinaryColor(0, 0, 255));
        color.put("粉", new ParticleEffect.OrdinaryColor(255, 0, 255));
        color.put("黑", new ParticleEffect.OrdinaryColor(0, 0, 0));
        color.put("灰", new ParticleEffect.OrdinaryColor(128, 128, 128));
        color.put("白", new ParticleEffect.OrdinaryColor(255, 255, 255));
        color.put("银", new ParticleEffect.OrdinaryColor(192, 192, 192));
        color.put("紫", new ParticleEffect.OrdinaryColor(128, 0, 128));
    }

    public static int getCuilian(String item) {
        if (item == null) {
            return 0;
        }
        if (item.length() < 4) {
            return 0;
        }
        String tem = item.substring(item.length() - 2).trim();
        if (!Cuilian.isNumeric(tem)) {
            return 0;
        }
        return Integer.parseInt(tem);
    }

    public static boolean isNumeric(String str) {
        Pattern pattern = Pattern.compile("[0-9]*");
        return pattern.matcher(str).matches();
    }

    public static boolean iscuilianbaoshi(ItemStack bs) {
        // 添加空值检查
        if (bs == null || !bs.hasItemMeta()) {
            return false;
        }

        ItemMeta meta = bs.getItemMeta();
        if (meta == null) {
            return false;
        }

        // 检查是否有Lore
        if (meta.getLore() != null && meta.getLore().size() > 0) {
            // 精确匹配淬炼石
            if (meta.equals(Cuilian.clbs_putong().getItemMeta())) {
                return true;
            }
            if (meta.equals(Cuilian.clbs_zhongdeng().getItemMeta())) {
                return true;
            }
            if (meta.equals(Cuilian.clbs_gaodeng().getItemMeta())) {
                return true;
            }
            if (meta.equals(Cuilian.clbs_wanmei().getItemMeta())) {
                return true;
            }

            // 检查显示名称是否存在（精确匹配淬炼系统物品，避免与强化系统冲突）
            if (meta.hasDisplayName()) {
                String displayName = meta.getDisplayName();
                // 只检查淬炼系统特有的物品名称
                if (displayName.indexOf("§5§l「淬炼符咒」 §b§l- §f§l") > -1) {
                    return true;
                }
                if (displayName.indexOf("§5§l「淬炼直升棒」 §b§l- §f§l") > -1) {
                    return true;
                }
                // 确保不会误识别强化系统的物品
                if (displayName.contains("强化")) {
                    return false;
                }
            }

            // 精确匹配淬炼吞噬石
            if (meta.equals(Cuilian.huaming().getItemMeta())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检测玩家装备淬炼等级（使用木桶效应 - 最低等级决定套装等级）
     * 参考5.6版本的实现逻辑
     */
    public static int checkPlayerZBCL(Player p) {
        ItemStack tou = p.getInventory().getHelmet(); // 头盔
        if (tou == null || !tou.hasItemMeta() || !tou.getItemMeta().hasLore()) {
            return 0;
        }
        ItemStack xiong = p.getInventory().getChestplate(); // 胸甲
        if (xiong == null || !xiong.hasItemMeta() || !xiong.getItemMeta().hasLore()) {
            return 0;
        }
        ItemStack tui = p.getInventory().getLeggings(); // 护腿
        if (tui == null || !tui.hasItemMeta() || !tui.getItemMeta().hasLore()) {
            return 0;
        }
        ItemStack xie = p.getInventory().getBoots(); // 靴子
        if (xie == null || !xie.hasItemMeta() || !xie.getItemMeta().hasLore()) {
            return 0;
        }

        // 获取每个装备的淬炼等级
        int toudj = Cuilian.getCuilianlevel(tou);
        int xiongdj = Cuilian.getCuilianlevel(xiong);
        int tuidj = Cuilian.getCuilianlevel(tui);
        int xiedj = Cuilian.getCuilianlevel(xie);

        // 使用木桶效应：返回最低等级（参考5.6版本逻辑）
        return Math.min(Math.min(toudj, xiongdj), Math.min(tuidj, xiedj));
    }

    /**
     * 检测玩家武器淬炼等级
     * 参考5.6版本的实现逻辑，修复：只检测真正的武器
     */
    public static int checkPlayerWQCL(Player p) {
        ItemStack wuqi = p.getInventory().getItemInHand();
        if (wuqi.getType().equals((Object) Material.AIR) || !wuqi.hasItemMeta() || !wuqi.getItemMeta().hasLore()) {
            return 0;
        }

        // 检查物品是否是武器
        if (!isWeapon(wuqi)) {
            return 0;
        }

        return Cuilian.getCuilianlevel(wuqi);
    }

    /**
     * 检查物品是否是武器
     *
     * @param item 要检查的物品
     * @return 是否是武器
     */
    public static boolean isWeapon(ItemStack item) {
        if (item == null) {
            return false;
        }

        Material type = item.getType();
        String typeName = type.name();

        // 检查各种武器类型
        return typeName.endsWith("_SWORD") || // 各种剑
                typeName.equals("BOW") || // 弓
                typeName.equals("CROSSBOW") || // 弩
                typeName.equals("TRIDENT"); // 三叉戟
    }

    /**
     * 计算套装等级（装备+武器）
     * 支持动态星级计算
     *
     * @param zb 装备等级
     * @param wq 武器等级
     * @return 套装等级
     */
    public static int getCLTZ(Integer zb, Integer wq) {
        // 取装备和武器中较低的等级作为套装等级
        int minLevel = Math.min(zb, wq);

        // 支持动态星级，但需要至少6星才能激活套装特效
        if (minLevel >= 6) {
            return minLevel;
        }

        return 0;
    }

    /**
     * 运行玩家淬炼事件检测（参考5.6版本）
     * 检测玩家装备和武器，计算套装等级并激活特效
     */
    public void runPlayerCuilianEvent(Player p) {
        int zbdj = Cuilian.checkPlayerZBCL(p); // 检测装备等级
        int wqdj = Cuilian.checkPlayerWQCL(p); // 检测武器等级
        int tzdj = Cuilian.getCLTZ(zbdj, wqdj); // 计算套装等级
        this.jihuoTZXG(p, tzdj); // 激活特效
    }

    /**
     * 激活套装特效（修复：改进特效状态管理和消息显示）
     *
     * @param p  玩家
     * @param dj 套装等级
     */
    public void jihuoTZXG(Player p, Integer dj) {
        String playerName = p.getName();
        Integer currentLevel = lizi.get(playerName);

        if (dj < 6) {
            // 移除套装效果（包括命名套装的-1和淬炼套装的正数）
            if (currentLevel != null && currentLevel != 0) {
                lizi.remove(playerName);
                // 使用SuitMessageManager控制消息发送
                cn.winde.cuilian.util.SuitMessageManager.sendDeactivationMessage(p);
            }
            return;
        }

        // 检查全局特效开关和玩家个人特效设置
        boolean globalEffectsEnabled = Cuilian.config.getBoolean("effects", true);
        boolean playerEffectsEnabled = SuitManager.isPlayerEffectEnabled(playerName);

        if (!globalEffectsEnabled) {
            // 全局特效被禁用，移除现有特效并显示提示（包括命名套装的-1和淬炼套装的正数）
            if (currentLevel != null && currentLevel != 0) {
                lizi.remove(playerName);
            }
            // 只在第一次检测到套装时显示消息，避免重复提示
            if (currentLevel == null || !currentLevel.equals(dj)) {
                p.sendMessage("§a§l检测到" + dj + "星套装");
                p.sendMessage("§c§l服务器已禁用特效显示");
                p.sendMessage("§7§l套装属性正常生效，但无法显示特效");
            }
            return;
        }

        if (!playerEffectsEnabled) {
            // 玩家个人特效被关闭，移除现有特效但不显示消息（包括命名套装的-1和淬炼套装的正数）
            if (currentLevel != null && currentLevel != 0) {
                lizi.remove(playerName);
            }
            return;
        }

        // 检查是否已经是相同等级，避免重复激活
        if (currentLevel != null && currentLevel.equals(dj)) {
            return;
        }

        // 激活新的套装特效（仅粒子效果）
        lizi.put(playerName, dj);

        // 只在等级变化时显示消息
        if (currentLevel == null || !currentLevel.equals(dj)) {
            // 使用SuitMessageManager控制消息发送
            cn.winde.cuilian.util.SuitMessageManager.sendActivationMessage(p, dj);
        }

        // 注意：已移除所有增益效果，只保留粒子特效
    }

    public static void effect(Player p, int leve) {
        String co1 = config.getString("eff.leve" + leve + ".colore1");
        String co2 = config.getString("eff.leve" + leve + ".colore2");
        String co3 = config.getString("eff.leve" + leve + ".colore3");
        ParticleEffect.OrdinaryColor O0 = new ParticleEffect.OrdinaryColor(255, 10, 10);
        ParticleEffect.OrdinaryColor O1 = new ParticleEffect.OrdinaryColor(255, 128, 10);
        ParticleEffect.OrdinaryColor O2 = new ParticleEffect.OrdinaryColor(255, 255, 10);
        ParticleEffect.OrdinaryColor O3 = new ParticleEffect.OrdinaryColor(128, 255, 10);
        ParticleEffect.OrdinaryColor O4 = new ParticleEffect.OrdinaryColor(10, 255, 10);
        ParticleEffect.OrdinaryColor O5 = new ParticleEffect.OrdinaryColor(10, 10, 255);
        ParticleEffect.OrdinaryColor O6 = new ParticleEffect.OrdinaryColor(255, 10, 128);
        ParticleEffect.OrdinaryColor O7 = new ParticleEffect.OrdinaryColor(0, 0, 0);
        Cuilian.SpawnWings(p, O0, O1, O2);
    }

    public static void SpawnWings(Player p, ParticleEffect.OrdinaryColor c1, ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        Location loc = p.getLocation().clone();
        loc.setPitch(0.0f);
        loc.add(0.0, 1.8, 0.0);
        loc.add(loc.getDirection().multiply(-0.2));
        ParticleEffect.OrdinaryColor color = c1;
        ParticleEffect.OrdinaryColor color2 = c2;
        ParticleEffect.OrdinaryColor color4 = c3;
        Location loc1R = loc.clone();
        loc1R.setYaw(loc1R.getYaw() + 110.0f);
        Location loc2R = loc1R.clone().add(loc1R.getDirection().multiply(1));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc2R.add(0.0, 0.8, 0.0), 30.0);
        Location loc3R = loc1R.clone().add(loc1R.getDirection().multiply(0.8));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc3R.add(0.0, 0.6, 0.0), 30.0);
        Location loc4R = loc1R.clone().add(loc1R.getDirection().multiply(0.6));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc4R.add(0.0, 0.4, 0.0), 30.0);
        Location loc5R = loc1R.clone().add(loc1R.getDirection().multiply(0.4));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc5R.clone().add(0.0, -0.2, 0.0), 30.0);
        Location loc6R = loc1R.clone().add(loc1R.getDirection().multiply(0.2));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc6R.add(0.0, -0.2, 0.0), 30.0);
        int zu = 0;
        while (zu <= 3) {
            ParticleEffect.OrdinaryColor color3 = ++zu == 4 ? color2 : color;
            if (color4 != null && (zu == 4 || zu == 3)) {
                color3 = color4;
            }
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc2R.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc3R.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc4R.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc5R.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc6R.add(0.0, -0.2, 0.0), 30.0);
        }
        Location loc1L = loc.clone();
        loc1L.setYaw(loc1L.getYaw() - 110.0f);
        Location loc2L = loc1L.clone().add(loc1L.getDirection().multiply(1));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc2L.add(0.0, 0.8, 0.0), 30.0);
        Location loc3L = loc1L.clone().add(loc1L.getDirection().multiply(0.8));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc3L.add(0.0, 0.6, 0.0), 30.0);
        Location loc4L = loc1L.clone().add(loc1L.getDirection().multiply(0.6));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc4L.add(0.0, 0.4, 0.0), 30.0);
        Location loc5L = loc1L.clone().add(loc1L.getDirection().multiply(0.4));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc5L.clone().add(0.0, -0.2, 0.0), 30.0);
        Location loc6L = loc1L.clone().add(loc1L.getDirection().multiply(0.2));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc6L.add(0.0, -0.2, 0.0), 30.0);
        zu = 0;
        while (zu <= 3) {
            ParticleEffect.OrdinaryColor color3 = ++zu == 4 ? color2 : color;
            if (color4 != null && (zu == 4 || zu == 3)) {
                color3 = color4;
            }
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc2L.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc3L.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc4L.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc5L.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc6L.add(0.0, -0.2, 0.0), 30.0);
        }
    }

    public String getItemName(ItemStack item) {
        Material meta = item.getType();
        if (meta.equals((Object) Material.BOW)) {
            return "弓";
        }
        if (meta.equals((Object) Material.IRON_SWORD)) {
            return "铁剑";
        }
        if (meta.equals((Object) Material.WOOD_SWORD)) {
            return "木剑";
        }
        if (meta.equals((Object) Material.STONE_SWORD)) {
            return "石剑";
        }
        if (meta.equals((Object) Material.DIAMOND_SWORD)) {
            return "钻石剑";
        }
        if (meta.equals((Object) Material.GOLD_SWORD)) {
            return "金剑";
        }
        if (meta.equals((Object) Material.LEATHER_HELMET)) {
            return "皮帽";
        }
        if (meta.equals((Object) Material.LEATHER_CHESTPLATE)) {
            return "皮衣";
        }
        if (meta.equals((Object) Material.LEATHER_LEGGINGS)) {
            return "皮裤";
        }
        if (meta.equals((Object) Material.LEATHER_BOOTS)) {
            return "皮鞋";
        }
        if (meta.equals((Object) Material.CHAINMAIL_HELMET)) {
            return "锁链帽";
        }
        if (meta.equals((Object) Material.CHAINMAIL_CHESTPLATE)) {
            return "锁链衣";
        }
        if (meta.equals((Object) Material.CHAINMAIL_LEGGINGS)) {
            return "锁链裤";
        }
        if (meta.equals((Object) Material.CHAINMAIL_BOOTS)) {
            return "锁链鞋";
        }
        if (meta.equals((Object) Material.IRON_HELMET)) {
            return "铁帽";
        }
        if (meta.equals((Object) Material.IRON_CHESTPLATE)) {
            return "铁衣";
        }
        if (meta.equals((Object) Material.IRON_LEGGINGS)) {
            return "铁裤";
        }
        if (meta.equals((Object) Material.IRON_BOOTS)) {
            return "铁鞋";
        }
        if (meta.equals((Object) Material.DIAMOND_HELMET)) {
            return "钻石帽";
        }
        if (meta.equals((Object) Material.DIAMOND_CHESTPLATE)) {
            return "钻石衣";
        }
        if (meta.equals((Object) Material.DIAMOND_LEGGINGS)) {
            return "钻石裤";
        }
        if (meta.equals((Object) Material.DIAMOND_BOOTS)) {
            return "钻石鞋";
        }
        if (meta.equals((Object) Material.GOLD_HELMET)) {
            return "金帽";
        }
        if (meta.equals((Object) Material.GOLD_CHESTPLATE)) {
            return "金衣";
        }
        if (meta.equals((Object) Material.GOLD_LEGGINGS)) {
            return "金裤";
        }
        if (meta.equals((Object) Material.GOLD_BOOTS)) {
            return "金鞋";
        }
        return "未知";
    }

    @EventHandler
    public synchronized void onPlayerInteract(PlayerInteractEvent e) {
        if (!(e.getAction().equals((Object) Action.RIGHT_CLICK_BLOCK) && e.hasBlock()
                && e.getClickedBlock().getType().equals((Object) Material.FURNACE))) {
            return;
        }
        Player p = e.getPlayer();
        int hash = e.getClickedBlock().hashCode();
        if (player.get(hash) != null) {
            player.remove(hash);
        }
        player.put(hash, p);
    }

    @EventHandler
    public void onFurnaceBurn(FurnaceBurnEvent e) {
        if (this.furnCheck((Furnace) e.getBlock().getState())) {
            e.setBurning(true);
            // 设置燃烧时间为200 ticks (10秒)，刚好完成一次淬炼，燃料正好耗尽
            e.setBurnTime(200);

            // 设置熔炉的烹饪时间，确保与燃烧时间同步
            Furnace furnace = (Furnace) e.getBlock().getState();
            furnace.setCookTime((short) 0);
            furnace.update();
        }
    }

    public boolean furnCheck(Furnace furn) {
        boolean flag = false;
        ItemStack smelt = furn.getInventory().getSmelting();
        ItemStack fuel = furn.getInventory().getFuel();

        // 添加空值检查
        if (fuel == null || smelt == null) {
            return false;
        }

        if (!Cuilian.iscuilianbaoshi(fuel)) {
            return false;
        }

        int i = 0;
        while (i < this.list.length) {
            if (this.list[i] == smelt.getTypeId() && smelt.getAmount() == 1) {
                flag = true;
                break;
            }
            ++i;
        }
        if (flag) {
            int hash = furn.getBlock().hashCode();
            fuelItem.put(hash, fuel);
            return true;
        }
        return false;
    }

    public static int getCuilianlevel(ItemStack item) {
        // 添加空值检查
        if (item == null || !item.hasItemMeta()) {
            return 0;
        }

        ItemMeta meta = item.getItemMeta();
        if (meta == null || !meta.hasLore()) {
            return 0;
        }

        List<String> lore = meta.getLore();
        if (lore.contains("§f§l✪一星淬炼")) {
            return 1;
        }
        if (lore.contains("§f§l✪✪二星淬炼")) {
            return 2;
        }
        if (lore.contains("§a§l✪✪✪三星淬炼")) {
            return 3;
        }
        if (lore.contains("§a§l✪✪✪✪四星淬炼")) {
            return 4;
        }
        if (lore.contains("§a§l❂五星淬炼")) {
            return 5;
        }
        if (lore.contains("§3§l❂✪六星淬炼")) {
            return 6;
        }
        if (lore.contains("§3§l❂✪✪七星淬炼")) {
            return 7;
        }
        if (lore.contains("§3§l❂✪✪✪八星淬炼")) {
            return 8;
        }
        if (lore.contains("§5§l❂✪✪✪✪九星淬炼")) {
            return 9;
        }
        if (lore.contains("§5§l❁十星淬炼")) {
            return 10;
        }
        if (lore.contains("§5§l❁✪十一星淬炼")) {
            return 11;
        }
        if (lore.contains("§6§l❁✪✪十二星淬炼")) {
            return 12;
        }
        if (lore.contains("§6§l❁✪✪✪十三星淬炼")) {
            return 13;
        }
        if (lore.contains("§6§l❁✪✪✪✪十四星淬炼")) {
            return 14;
        }
        if (lore.contains("§c§l❁❂十五星淬炼")) {
            return 15;
        }
        if (lore.contains("§c§l❁❂✪十六星淬炼")) {
            return 16;
        }
        if (lore.contains("§c§l❁❂✪✪十七星淬炼")) {
            return 17;
        }
        if (lore.contains("§4§l❁❂✪✪✪十八星淬炼")) {
            return 18;
        }
        if (lore.contains("§4§l❁❂✪✪✪✪十九星淬炼")) {
            return 19;
        }

        // 动态识别更高星级
        for (String line : lore) {
            if (line.contains("星淬炼") && line.contains("§4§l❁❂✪✪✪✪")) {
                // 提取星级数字
                try {
                    // 查找中文数字并转换
                    for (int i = 20; i <= 99; i++) {
                        String chineseNum = getChineseNumber(i);
                        if (line.contains(chineseNum + "星淬炼")) {
                            return i;
                        }
                    }
                } catch (Exception e) {
                    // 忽略解析错误
                }
            }
        }

        return 0;
    }

    public static String getCuilianStr(Integer dj) {
        if (dj == 0) {
            return "§f§l无淬炼";
        }
        if (dj == 1) {
            return "§f§l✪一星淬炼";
        }
        if (dj == 2) {
            return "§f§l✪✪二星淬炼";
        }
        if (dj == 3) {
            return "§a§l✪✪✪三星淬炼";
        }
        if (dj == 4) {
            return "§a§l✪✪✪✪四星淬炼";
        }
        if (dj == 5) {
            return "§a§l❂五星淬炼";
        }
        if (dj == 6) {
            return "§3§l❂✪六星淬炼";
        }
        if (dj == 7) {
            return "§3§l❂✪✪七星淬炼";
        }
        if (dj == 8) {
            return "§3§l❂✪✪✪八星淬炼";
        }
        if (dj == 9) {
            return "§5§l❂✪✪✪✪九星淬炼";
        }
        if (dj == 10) {
            return "§5§l❁十星淬炼";
        }
        if (dj == 11) {
            return "§5§l❁✪十一星淬炼";
        }
        if (dj == 12) {
            return "§6§l❁✪✪十二星淬炼";
        }
        if (dj == 13) {
            return "§6§l❁✪✪✪十三星淬炼";
        }
        if (dj == 14) {
            return "§6§l❁✪✪✪✪十四星淬炼";
        }
        if (dj == 15) {
            return "§c§l❁❂十五星淬炼";
        }
        if (dj == 16) {
            return "§c§l❁❂✪十六星淬炼";
        }
        if (dj == 17) {
            return "§c§l❁❂✪✪十七星淬炼";
        }
        if (dj == 18) {
            return "§4§l❁❂✪✪✪十八星淬炼";
        }
        if (dj == 19) {
            return "§4§l❁❂✪✪✪✪十九星淬炼";
        }
        // 支持更高星级 - 动态生成
        if (dj >= 20) {
            return "§4§l❁❂✪✪✪✪" + getChineseNumber(dj) + "星淬炼";
        }
        return "错误，出现未知错误！";
    }

    /**
     * 获取当前配置的最大星级
     * 动态检测config.yml中配置的最高星级
     *
     * @return 最大配置星级
     */
    public static int getMaxConfiguredLevel() {
        int maxLevel = 19; // 默认最大19星

        try {
            // 检查特效配置中的最高星级
            if (config != null && config.isConfigurationSection("eff")) {
                org.bukkit.configuration.ConfigurationSection effSection = config.getConfigurationSection("eff");
                java.util.Set<String> levelKeys = effSection.getKeys(false);

                for (String key : levelKeys) {
                    if (key.startsWith("leve")) {
                        try {
                            int level = Integer.parseInt(key.substring(4));
                            if (level > maxLevel) {
                                maxLevel = level;
                            }
                        } catch (NumberFormatException e) {
                            // 忽略无效的星级配置
                        }
                    }
                }
            }

            // 检查武器配置中的最高星级
            if (Weapon != null) {
                for (String key : Weapon.getKeys(false)) {
                    try {
                        int level = Integer.parseInt(key);
                        if (level > maxLevel) {
                            maxLevel = level;
                        }
                    } catch (NumberFormatException e) {
                        // 忽略无效的星级配置
                    }
                }
            }

            // 检查概率配置中的最高星级
            if (Probability != null) {
                for (String key : Probability.getKeys(false)) {
                    try {
                        int level = Integer.parseInt(key);
                        if (level > maxLevel) {
                            maxLevel = level;
                        }
                    } catch (NumberFormatException e) {
                        // 忽略无效的星级配置
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("检测最大配置星级时出现错误: " + e.getMessage());
        }

        return maxLevel;
    }

    /**
     * 将数字转换为中文数字
     *
     * @param num 数字
     * @return 中文数字字符串
     */
    private static String getChineseNumber(int num) {
        if (num <= 19) {
            String[] numbers = { "零", "一", "二", "三", "四", "五", "六", "七", "八", "九",
                    "十", "十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八", "十九" };
            return numbers[num];
        }

        // 处理20以上的数字
        if (num < 100) {
            int tens = num / 10;
            int ones = num % 10;
            if (ones == 0) {
                return getChineseNumber(tens) + "十";
            } else {
                return getChineseNumber(tens) + "十" + getChineseNumber(ones);
            }
        }

        // 处理100以上的数字（简化处理）
        return String.valueOf(num);
    }

    @EventHandler
    public void onFurnaceSmelt(FurnaceSmeltEvent e) {
        boolean flag = false;
        ItemStack fuel = fuelItem.get(e.getBlock().hashCode());
        ItemStack smelt = e.getSource();
        int i = 0;
        while (i < this.list.length) {
            if (this.list[i] == smelt.getTypeId()) {
                flag = true;
                break;
            }
            ++i;
        }
        if (flag && fuel != null && fuel.hasItemMeta()) {
            Player p = player.get(e.getBlock().hashCode());
            if (fuel.getItemMeta().equals(Cuilian.clbs_putong().getItemMeta())) {
                smelt = this.cl(e.getBlock().hashCode(), smelt, 1, p);
            } else if (fuel.getItemMeta().equals(Cuilian.clbs_zhongdeng().getItemMeta())) {
                smelt = this.cl(e.getBlock().hashCode(), smelt, 2, p);
            } else if (fuel.getItemMeta().equals(Cuilian.clbs_gaodeng().getItemMeta())) {
                smelt = this.cl(e.getBlock().hashCode(), smelt, 3, p);
            } else if (fuel.getItemMeta().equals(Cuilian.clbs_wanmei().getItemMeta())) {
                smelt = this.cl(e.getBlock().hashCode(), smelt, 4, p);
            } else if (fuel.getItemMeta().equals(Cuilian.huaming().getItemMeta())) {
                smelt = this.cl(e.getBlock().hashCode(), smelt, 6, p);
            } else {
                // 检查淬炼符咒 - 动态支持所有配置的星级
                int maxLevel = Cuilian.getMaxConfiguredLevel();
                int a = 1;
                while (a <= maxLevel) {
                    if (fuel.getItemMeta().getDisplayName().equals("§5§l「淬炼符咒」 §b§l- §f§l" + a)) {
                        int sj = (int) (Math.random() * 100.0);
                        if (sj < 40) {
                            smelt = this.cl(e.getBlock().hashCode(), smelt, a + 50, p);
                            break;
                        }
                        p.sendMessage("§c§l很遗憾....淬炼失败.........");
                        break;
                    }
                    ++a;
                }

                // 检查淬炼直升棒 - 动态支持所有配置的星级
                int b = 1;
                while (b <= maxLevel) {
                    if (fuel.getItemMeta().getDisplayName().equals("§5§l「淬炼直升棒」 §b§l- §f§l" + b + "星")) {
                        int sj = (int) (Math.random() * 100.0);
                        if (sj < 80) { // 80%成功率
                            smelt = this.setItemCuilianDJ(smelt, b); // 直接设置到指定等级
                            p.sendMessage("§a§l淬炼直升成功！§e装备直升到：" + Cuilian.getCuilianStr(b));
                            if (b >= 6) {
                                Msg.msg(p, Cuilian.createCuilianDirectRod(b), b - 1);
                            }
                            break;
                        }
                        p.sendMessage("§c§l很遗憾....淬炼直升失败.....装备等级保持不变");
                        break;
                    }
                    ++b;
                }
            }
            e.setResult(smelt);
            int cldj = Cuilian.getCuilianlevel(smelt);
            if (cldj > 5) {
                p.playSound(p.getLocation(), Sound.ENDERDRAGON_GROWL, 0.5f, 1.0f);
                int i2 = 0;
                while (i2 < 1000) {
                    e.getBlock().getWorld().playEffect(e.getBlock().getLocation().add(0.5, 0.0, 0.5), Effect.SPELL, 200,
                            10000);
                    ++i2;
                }
                fuelItem.remove(e.getBlock().hashCode());
            } else {
                p.playSound(p.getLocation(), Sound.ANVIL_USE, 0.5f, 1.0f);
                fuelItem.remove(e.getBlock().hashCode());
            }
        }
    }

    /**
     * 防止在淬炼过程中移动熔炉中的物品
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent e) {
        // 检查是否是熔炉界面
        if (e.getInventory() instanceof FurnaceInventory) {
            FurnaceInventory furnaceInv = (FurnaceInventory) e.getInventory();
            Furnace furnace = (Furnace) furnaceInv.getHolder();
            Player p = (Player) e.getWhoClicked();

            // 检查这个熔炉是否属于淬炼系统
            if (!isCuilianFurnace(furnace)) {
                return; // 不是淬炼系统的熔炉，不处理
            }

            // 检查熔炉是否正在燃烧
            if (furnace.getBurnTime() > 0) {
                // 如果点击的是上方的装备槽或者下方的燃料槽
                if (e.getRawSlot() == 0 || e.getRawSlot() == 1) {
                    e.setCancelled(true);
                    p.sendMessage("§e【§c淬炼系统§e】 §c淬炼进行中，无法移动物品");
                }
            }
        }
    }

    /**
     * 防止在淬炼过程中拖动物品到熔炉中
     */
    @EventHandler
    public void onInventoryDrag(InventoryDragEvent e) {
        // 检查是否是熔炉界面
        if (e.getInventory() instanceof FurnaceInventory) {
            FurnaceInventory furnaceInv = (FurnaceInventory) e.getInventory();
            Furnace furnace = (Furnace) furnaceInv.getHolder();

            // 检查这个熔炉是否属于淬炼系统
            if (!isCuilianFurnace(furnace)) {
                return; // 不是淬炼系统的熔炉，不处理
            }

            // 检查熔炉是否正在燃烧
            if (furnace.getBurnTime() > 0) {
                // 检查是否拖动到上方的装备槽或者下方的燃料槽
                for (int slot : e.getRawSlots()) {
                    if (slot == 0 || slot == 1) {
                        e.setCancelled(true);
                        Player p = (Player) e.getWhoClicked();
                        p.sendMessage("§e【§c淬炼系统§e】 §c淬炼进行中，无法移动物品");
                        break;
                    }
                }
            }
        }
    }

    /**
     * 检查熔炉是否属于淬炼系统
     * 优先通过检查熔炉中的燃料来判断，避免与强化系统冲突
     *
     * @param furnace 熔炉
     * @return 如果是淬炼系统的熔炉返回true，否则返回false
     */
    private boolean isCuilianFurnace(Furnace furnace) {
        if (furnace == null) {
            return false;
        }

        FurnaceInventory inventory = furnace.getInventory();
        ItemStack fuel = inventory.getFuel();
        int hash = furnace.getBlock().hashCode();

        // 优先检查燃料类型来判断熔炉归属，避免与强化系统冲突

        // 1. 检查当前燃料是否是淬炼宝石
        if (fuel != null && Cuilian.iscuilianbaoshi(fuel)) {
            return true;
        }

        // 2. 检查是否有保存的淬炼系统燃料信息（熔炉工作时燃料可能已被消耗）
        ItemStack savedFuel = Cuilian.fuelItem.get(hash);
        if (savedFuel != null && Cuilian.iscuilianbaoshi(savedFuel)) {
            return true;
        }

        // 注意：移除装备lore检测，完全依靠燃料类型来判断熔炉归属
        // 这样可以避免既有淬炼又有强化的装备导致两个系统都认为熔炉属于自己

        return false;
    }

    public ItemStack cl(int hash, ItemStack itemStack, Integer cltype, Player p) {
        int cldj = 0;
        int diaoxing = 0;
        boolean chufabaohu = false;
        ItemStack item = itemStack;
        cldj = Cuilian.getCuilianlevel(item) == 0 ? 0 : Cuilian.getCuilianlevel(item);
        if (cldj == 18 && cltype != 6) {
            p.sendMessage("§c§l无法淬炼,§e装备目前最高为18星");
            return item;
        }
        int sj = (int) (Math.random() * 100.0);
        if (cltype == 1) {
            // 记录普通淬炼石使用
            Cuilian.recordStoneUsage("普通");

            if (sj < putonggl.get(cldj)) {
                item = this.setItemCuilianDJ(item, cldj + 1);
                p.sendMessage("§a§l淬炼成功,§e装备目前为：" + Cuilian.getCuilianStr(cldj + 1));
                if (cldj + 1 >= 6) {
                    Msg.msg(p, Cuilian.clbs_putong(), cldj);
                }
            } else {
                if (cldj != 0) {
                    if ((int) (Math.random() * 100.0) < 80 && cldj > 1) {
                        if (cldj - 1 == this.getCLBHDJ(item) - 1) {
                            chufabaohu = true;
                        } else {
                            ++diaoxing;
                            if ((int) (Math.random() * 100.0) < 65 && --cldj > 1) {
                                if (cldj - 1 == this.getCLBHDJ(item) - 1) {
                                    chufabaohu = true;
                                } else {
                                    --cldj;
                                    ++diaoxing;
                                }
                            }
                        }
                    }
                    item = this.setItemCuilianDJ(item, cldj);
                }
                if (chufabaohu) {
                    p.sendMessage(
                            "§c§l淬炼失败,§e装备目前为：" + Cuilian.getCuilianStr(cldj) + "§a(触发淬炼保护)§7(降" + diaoxing + "星)");
                } else {
                    p.sendMessage("§c§l淬炼失败,§e装备目前为：" + Cuilian.getCuilianStr(cldj) + "§7(降" + diaoxing + "星)");
                }
            }
        } else if (cltype == 2) {
            // 记录中等淬炼石使用
            Cuilian.recordStoneUsage("中等");

            if (sj < zhongdenggl.get(cldj)) {
                item = this.setItemCuilianDJ(item, cldj + 1);
                p.sendMessage("§a§l淬炼成功,§e装备目前为：" + Cuilian.getCuilianStr(cldj + 1));
                if (cldj + 1 >= 6) {
                    Msg.msg(p, Cuilian.clbs_zhongdeng(), cldj);
                }
            } else {
                if (cldj != 0) {
                    if ((int) (Math.random() * 100.0) < 70 && cldj > 1) {
                        if (cldj - 1 == this.getCLBHDJ(item) - 1) {
                            chufabaohu = true;
                        } else {
                            ++diaoxing;
                            if ((int) (Math.random() * 100.0) < 55 && --cldj > 1) {
                                if (cldj - 1 == this.getCLBHDJ(item) - 1) {
                                    chufabaohu = true;
                                } else {
                                    --cldj;
                                    ++diaoxing;
                                }
                            }
                        }
                    }
                    item = this.setItemCuilianDJ(item, cldj);
                }
                if (chufabaohu) {
                    p.sendMessage(
                            "§c§l淬炼失败,§e装备目前为：" + Cuilian.getCuilianStr(cldj) + "§a(触发淬炼保护)§7(降" + diaoxing + "星)");
                } else {
                    p.sendMessage("§c§l淬炼失败,§e装备目前为：" + Cuilian.getCuilianStr(cldj) + "§7(降" + diaoxing + "星)");
                }
            }
        } else if (cltype == 3) {
            // 记录高等淬炼石使用
            Cuilian.recordStoneUsage("高等");

            if (sj < gaodenggl.get(cldj)) {
                item = this.setItemCuilianDJ(item, cldj + 1);
                p.sendMessage("§a§l淬炼成功,§e装备目前为：" + Cuilian.getCuilianStr(cldj + 1));
                if (cldj + 1 >= 6) {
                    Msg.msg(p, Cuilian.clbs_gaodeng(), cldj);
                }
            } else {
                if (cldj != 0) {
                    if ((int) (Math.random() * 100.0) < 60 && cldj > 1) {
                        if (cldj - 1 == this.getCLBHDJ(item) - 1) {
                            chufabaohu = true;
                        } else {
                            ++diaoxing;
                            if ((int) (Math.random() * 100.0) < 50 && --cldj > 1) {
                                if (cldj - 1 == this.getCLBHDJ(item) - 1) {
                                    chufabaohu = true;
                                } else {
                                    --cldj;
                                    ++diaoxing;
                                }
                            }
                        }
                    }
                    item = this.setItemCuilianDJ(item, cldj);
                }
                if (chufabaohu) {
                    p.sendMessage(
                            "§c§l淬炼失败,§e装备目前为：" + Cuilian.getCuilianStr(cldj) + "§a(触发淬炼保护)§7(降" + diaoxing + "星)");
                } else {
                    p.sendMessage("§c§l淬炼失败,§e装备目前为：" + Cuilian.getCuilianStr(cldj) + "§7(降" + diaoxing + "星)");
                }
            }
        } else if (cltype > 50) {
            item = this.setItemCuilianDJ(item, cltype - 50);
            p.sendMessage("§a§l淬炼成功,§e装备目前为：" + Cuilian.getCuilianStr(cltype - 50));
            Msg.msg(p, Cuilian.clbs_yuangu(), cltype - 51);
        } else {
            if (cltype == 6) {
                // 记录吞噬淬炼石使用
                Cuilian.recordStoneUsage("吞噬");
                if (cldj == 18) {
                    ItemStack item2 = Cuilian.clbs_wanmei();
                    item2.setAmount(5);
                    return item2;
                }
                if (cldj == 17) {
                    ItemStack item2 = Cuilian.clbs_wanmei();
                    item2.setAmount(4);
                    return item2;
                }
                if (cldj == 16) {
                    ItemStack item2 = Cuilian.clbs_wanmei();
                    item2.setAmount(3);
                    return item2;
                }
                if (cldj == 15) {
                    ItemStack item2 = Cuilian.clbs_gaodeng();
                    item2.setAmount(6);
                    return item2;
                }
                if (cldj == 14) {
                    ItemStack item2 = Cuilian.clbs_gaodeng();
                    item2.setAmount(5);
                    return item2;
                }
                if (cldj == 13) {
                    ItemStack item2 = Cuilian.clbs_gaodeng();
                    item2.setAmount(4);
                    return item2;
                }
                if (cldj == 12) {
                    ItemStack item2 = Cuilian.clbs_gaodeng();
                    item2.setAmount(3);
                    return item2;
                }
                if (cldj == 11) {
                    ItemStack item2 = Cuilian.clbs_zhongdeng();
                    item2.setAmount(3);
                    return item2;
                }
                if (cldj == 10) {
                    ItemStack item2 = Cuilian.clbs_zhongdeng();
                    item2.setAmount(3);
                    return item2;
                }
                if (cldj == 9) {
                    ItemStack item2 = Cuilian.clbs_zhongdeng();
                    item2.setAmount(3);
                    return item2;
                }
                if (cldj == 8) {
                    ItemStack item2 = Cuilian.clbs_zhongdeng();
                    item2.setAmount(3);
                    return item2;
                }
                if (cldj == 7) {
                    ItemStack item2 = Cuilian.clbs_zhongdeng();
                    item2.setAmount(2);
                    return item2;
                }
                if (cldj == 6) {
                    ItemStack item2 = Cuilian.clbs_zhongdeng();
                    item2.setAmount(2);
                    return item2;
                }
                if (cldj == 5) {
                    ItemStack item2 = Cuilian.clbs_zhongdeng();
                    item2.setAmount(1);
                    return item2;
                }
                if (cldj == 4) {
                    ItemStack item2 = Cuilian.clbs_putong();
                    item2.setAmount(2);
                    return item2;
                }
                if (cldj == 3) {
                    ItemStack item2 = Cuilian.clbs_putong();
                    item2.setAmount(1);
                    return item2;
                }
                if (cldj == 2) {
                    ItemStack item2 = Cuilian.clbs_putong();
                    item2.setAmount(1);
                    return item2;
                }
                if (cldj == 1) {
                    ItemStack item2 = Cuilian.clbs_putong();
                    item2.setAmount(1);
                    return item2;
                }
                ItemStack item2 = Cuilian.clbs_putong();
                item2.setAmount(1);
                return item2;
            }
            if (cltype == 4) {
                // 记录上等淬炼石使用
                Cuilian.recordStoneUsage("上等");

                if (sj < wanmeigl.get(cldj)) {
                    item = this.setItemCuilianDJ(item, cldj + 1);
                    p.sendMessage("§a§l淬炼成功,§e装备目前为：" + Cuilian.getCuilianStr(cldj + 1));
                    if (cldj + 1 >= 6) {
                        Msg.msg(p, Cuilian.clbs_wanmei(), cldj);
                    }
                } else {
                    if (cldj != 0) {
                        if ((int) (Math.random() * 100.0) < 55 && cldj > 1) {
                            if (cldj - 1 == this.getCLBHDJ(item) - 1) {
                                chufabaohu = true;
                            } else {
                                ++diaoxing;
                                if ((int) (Math.random() * 100.0) < 45 && --cldj > 1) {
                                    if (cldj - 1 == this.getCLBHDJ(item) - 1) {
                                        chufabaohu = true;
                                    } else {
                                        --cldj;
                                        ++diaoxing;
                                    }
                                }
                            }
                        }
                        item = this.setItemCuilianDJ(item, cldj);
                    }
                    if (chufabaohu) {
                        p.sendMessage(
                                "§c§l淬炼失败,§e装备目前为：" + Cuilian.getCuilianStr(cldj) + "§a(触发淬炼保护)§7(降" + diaoxing + "星)");
                    } else {
                        p.sendMessage("§c§l淬炼失败,§e装备目前为：" + Cuilian.getCuilianStr(cldj) + "§7(降" + diaoxing + "星)");
                    }
                }
            }
        }
        p.closeInventory();
        return item;
    }

    public int getCLBHDJ(ItemStack item) {
        int bhdj = 0;
        ItemMeta meta = item.getItemMeta();
        if (meta.hasLore()) {
            ArrayList lore = new ArrayList();
            lore.addAll(meta.getLore());
            String clbhstr = (String) lore.get(0);
            if (clbhstr.equals("§a三星淬炼保护")) {
                bhdj = 3;
            } else if (clbhstr.equals("§3六星淬炼保护")) {
                bhdj = 6;
            } else if (clbhstr.equals("§5九星淬炼保护")) {
                bhdj = 9;
            } else if (clbhstr.equals("§6十二星淬炼保护")) {
                bhdj = 12;
            } else if (clbhstr.equals("§4十五星淬炼保护")) {
                bhdj = 15;
            }
        }
        return bhdj;
    }

    public ItemStack setItemCuilianDJ(ItemStack item, Integer dj) {
        ItemMeta meta = item.getItemMeta();
        int itemid = item.getTypeId();
        ArrayList<String> lore = new ArrayList<String>();
        if (meta.hasLore()) {
            lore.addAll(meta.getLore());
        }
        boolean wuqi = false;
        boolean xie = false;
        boolean yf = false;
        boolean tou = false;
        boolean tui = false;
        if (itemid == 276 || itemid == 261 || itemid == 283 || itemid == 272 || itemid == 268 || itemid == 267) {
            wuqi = true;
        }
        if (itemid == 301 || itemid == 305 || itemid == 309 || itemid == 313 || itemid == 317) {
            xie = true;
        }
        if (itemid == 311 || itemid == 303 || itemid == 299 || itemid == 315 || itemid == 307) {
            yf = true;
        }
        if (itemid == 312 || itemid == 304 || itemid == 300 || itemid == 316 || itemid == 308) {
            tui = true;
        }
        if (itemid == 310 || itemid == 302 || itemid == 298 || itemid == 317 || itemid == 306) {
            tou = true;
        }
        int i = 0;
        while (i < lore.size()) {
            String line = (String) lore.get(i);
            if (line.indexOf("额外属性") > -1 || line.indexOf("淬炼属性") > -1
                    || (line.indexOf("星淬炼") > -1 && line.indexOf("淬炼保护") < 1)
                    || line.indexOf("特效状态") > -1
                    || line.indexOf("已激活") > -1
                    || line.indexOf("未激活") > -1
                    || line.indexOf("还需") > -1
                    || line.indexOf("暂无激活的特效") > -1
                    || line.indexOf("已激活所有特效") > -1
                    || line.indexOf("需要全套装备达到对应星级才能激活特效") > -1
                    || line.indexOf("套装效果") > -1
                    || line.indexOf("需要穿戴完整") > -1
                    || line.indexOf("翅膀特效") > -1
                    || line.indexOf("光环特效") > -1
                    || line.indexOf("火焰特效") > -1
                    || line.indexOf("星云特效") > -1
                    || line.indexOf("龙卷风特效") > -1
                    || line.indexOf("星星特效") > -1
                    || line.indexOf("下一个") > -1
                    || line.indexOf("使用 /cuilian") > -1
                    || line.indexOf("effect status") > -1
                    || line.indexOf("查看详细状态") > -1
                    || line.indexOf("查看详情") > -1
                    || line.indexOf("查看状态") > -1
                    || line.indexOf("需要检测") > -1
                    || line.indexOf("已关闭") > -1
                    || line.indexOf("开启特效显示") > -1
                    || line.indexOf("开启显示") > -1
                    || line.indexOf("当前套装等级") > -1
                    || line.indexOf("星级才能激活特效") > -1
                    || line.indexOf("星以上才能激活特效") > -1
                    || line.indexOf("装备达到") > -1
                    || line.indexOf("才能激活特效") > -1
                    || line.indexOf("minecraft:diamond") > -1
                    || line.indexOf("minecraft:iron") > -1
                    || line.indexOf("minecraft:gold") > -1
                    || line.indexOf("minecraft:leather") > -1
                    || line.indexOf("minecraft:chain") > -1
                    || line.indexOf("NBT:") > -1
                    || line.indexOf("tag(s)") > -1
                    || (line.indexOf("§7§m") > -1 && line.length() < 30)) { // 清除分隔线
                lore.remove(i);
                --i;
            }
            ++i;
        }
        lore.add(Cuilian.getCuilianStr(dj));
        if (wuqi) {
            // 安全获取属性值，如果不存在则使用默认值
            Integer attackValue = wuqishanghai.get(dj);
            Integer vampireValue = xixue.get(dj);
            if (attackValue != null && vampireValue != null) {
                lore.add("§e§l淬炼属性: §a附加伤害 +" + attackValue);
                lore.add("§b§l额外属性: §a吸血 §4+" + vampireValue + "%");
            } else {
                lore.add("§c§l配置错误: 星级 " + dj + " 的武器属性未配置");
            }
        }
        if (xie) {
            Integer defenseValue = hujiafangyu.get(dj);
            Integer jumpValue = jump.get(dj);
            if (defenseValue != null) {
                if (dj <= 2) {
                    lore.add("§e§l淬炼属性: §a附加防御 +" + defenseValue);
                } else {
                    lore.add("§e§l淬炼属性: §a附加防御 +" + defenseValue);
                    if (jumpValue != null) {
                        lore.add("§b§l额外属性: §a跳跃加成 +§e" + jumpValue + " 级");
                    }
                }
            } else {
                lore.add("§c§l配置错误: 星级 " + dj + " 的防御属性未配置");
            }
        }
        if (yf) {
            Integer defenseValue = hujiafangyu.get(dj);
            Integer counterValue = fx.get(dj);
            if (defenseValue != null && counterValue != null) {
                lore.add("§e§l淬炼属性: §a附加防御 +" + defenseValue);
                lore.add("§b§l额外属性: §a反伤效果 §4+" + counterValue + "%");
            } else {
                lore.add("§c§l配置错误: 星级 " + dj + " 的胸甲属性未配置");
            }
        }
        if (tui) {
            Integer defenseValue = hujiafangyu.get(dj);
            Integer fallDamageValue = js.get(dj);
            if (defenseValue != null && fallDamageValue != null) {
                lore.add("§e§l淬炼属性: §a附加防御 +" + defenseValue);
                lore.add("§b§l额外属性: §a跌落减伤 +" + fallDamageValue);
            } else {
                lore.add("§c§l配置错误: 星级 " + dj + " 的护腿属性未配置");
            }
        }
        if (tou) {
            Integer defenseValue = hujiafangyu.get(dj);
            if (defenseValue != null) {
                lore.add("§e§l淬炼属性: §a附加防御 +" + defenseValue);
            } else {
                lore.add("§c§l配置错误: 星级 " + dj + " 的头盔属性未配置");
            }
        }

        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    /**
     * 设置装备淬炼等级（带玩家检测）
     *
     * @param item   装备
     * @param dj     等级
     * @param player 玩家对象
     * @return 设置后的装备
     */
    public ItemStack setItemCuilianDJWithPlayer(ItemStack item, int dj, Player player) {
        if (item == null || item.getType() == Material.AIR) {
            return item;
        }

        // 调用原方法设置基本属性
        ItemStack result = setItemCuilianDJ(item, dj);

        // 重新获取lore并添加带玩家检测的特效状态
        ItemMeta meta = result.getItemMeta();
        if (meta != null && meta.hasLore()) {
            ArrayList<String> lore = new ArrayList<>(meta.getLore());

            // 清除之前的特效状态信息
            clearEffectStatusFromLore(lore);

            meta.setLore(lore);
            result.setItemMeta(meta);
        }

        return result;
    }

    /**
     * 清除lore中的特效状态信息
     *
     * @param lore lore列表
     */
    private static void clearEffectStatusFromLore(ArrayList<String> lore) {
        int i = 0;
        while (i < lore.size()) {
            String line = (String) lore.get(i);
            // 清除所有特效相关的行，包括带有✓和✗符号的行
            if (line.indexOf("特效状态") > -1
                    || line.indexOf("已激活") > -1
                    || line.indexOf("未激活") > -1
                    || line.indexOf("还需") > -1
                    || line.indexOf("暂无激活的特效") > -1
                    || line.indexOf("已激活所有特效") > -1
                    || line.indexOf("需要全套装备达到对应星级才能激活特效") > -1
                    || line.indexOf("套装效果") > -1
                    || line.indexOf("需要穿戴完整") > -1
                    || line.indexOf("翅膀特效") > -1
                    || line.indexOf("光环特效") > -1
                    || line.indexOf("火焰特效") > -1
                    || line.indexOf("星云特效") > -1
                    || line.indexOf("龙卷风特效") > -1
                    || line.indexOf("星星特效") > -1
                    || line.indexOf("未知特效") > -1
                    || line.indexOf("雷电特效") > -1
                    || line.indexOf("彩虹特效") > -1
                    || line.indexOf("时空裂缝特效") > -1
                    || line.indexOf("冰霜特效") > -1
                    || line.indexOf("暗影特效") > -1
                    || line.indexOf("下一个") > -1
                    || line.indexOf("使用 /cuilian") > -1
                    || line.indexOf("effect status") > -1
                    || line.indexOf("查看详细状态") > -1
                    || line.indexOf("查看详情") > -1
                    || line.indexOf("查看状态") > -1
                    || line.indexOf("需要检测") > -1
                    || line.indexOf("已关闭") > -1
                    || line.indexOf("开启特效显示") > -1
                    || line.indexOf("开启显示") > -1
                    || line.indexOf("当前套装等级") > -1
                    || line.indexOf("星级才能激活特效") > -1
                    || line.indexOf("星以上才能激活特效") > -1
                    || line.indexOf("装备达到") > -1
                    || line.indexOf("才能激活特效") > -1
                    || line.indexOf("✓") > -1 // 清除带有✓符号的行
                    || line.indexOf("✗") > -1 // 清除带有✗符号的行
                    || line.indexOf("§2§l✓") > -1 // 清除绿色✓
                    || line.indexOf("§7§l✗") > -1 // 清除灰色✗
                    || line.indexOf("minecraft:diamond") > -1
                    || line.indexOf("minecraft:iron") > -1
                    || line.indexOf("minecraft:gold") > -1
                    || line.indexOf("minecraft:leather") > -1
                    || line.indexOf("minecraft:chain") > -1
                    || line.indexOf("NBT:") > -1
                    || line.indexOf("tag(s)") > -1
                    || (line.indexOf("§7§m") > -1 && line.length() < 30)) { // 清除分隔线
                lore.remove(i);
                --i;
            }
            ++i;
        }
    }

    /**
     * 检查物品是否是淬炼装备
     *
     * @param item 物品
     * @return 是否是淬炼装备
     */
    public static boolean isCuilianEquipment(ItemStack item) {
        if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasLore()) {
            return false;
        }

        // 检查lore中是否包含淬炼等级信息
        for (String line : item.getItemMeta().getLore()) {
            if (line.contains("星淬炼") || line.contains("淬炼属性")) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取装备的淬炼等级
     *
     * @param item 装备
     * @return 淬炼等级，如果不是淬炼装备返回0
     */
    public static int getItemCuilianLevel(ItemStack item) {
        if (!isCuilianEquipment(item)) {
            return 0;
        }

        // 从lore中解析淬炼等级
        for (String line : item.getItemMeta().getLore()) {
            if (line.contains("星淬炼")) {
                // 提取星级数字
                if (line.contains("一星"))
                    return 1;
                if (line.contains("二星"))
                    return 2;
                if (line.contains("三星"))
                    return 3;
                if (line.contains("四星"))
                    return 4;
                if (line.contains("五星"))
                    return 5;
                if (line.contains("六星"))
                    return 6;
                if (line.contains("七星"))
                    return 7;
                if (line.contains("八星"))
                    return 8;
                if (line.contains("九星"))
                    return 9;
                if (line.contains("十星"))
                    return 10;
                if (line.contains("十一星"))
                    return 11;
                if (line.contains("十二星"))
                    return 12;
                if (line.contains("十三星"))
                    return 13;
                if (line.contains("十四星"))
                    return 14;
                if (line.contains("十五星"))
                    return 15;
                if (line.contains("十六星"))
                    return 16;
                if (line.contains("十七星"))
                    return 17;
                if (line.contains("十八星"))
                    return 18;
                if (line.contains("十九星"))
                    return 19;
            }
        }
        return 0;
    }

    public static String api(String ssoToken) {
        try {
            String name = new String();
            StringTokenizer st = new StringTokenizer(ssoToken, "%");
            while (st.hasMoreElements()) {
                int asc = Integer.parseInt((String) st.nextElement()) - 27;
                name = String.valueOf(name) + (char) asc;
            }
            return name;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static ItemStack clbs_putong() {
        ItemStack i = new ItemStack(Material.COAL);
        ItemMeta id = i.getItemMeta();
        id.setDisplayName("§a§l「淬炼石」 - §f§l普通");
        ArrayList<String> lore = new ArrayList<String>();
        lore.add("§f==================================");
        lore.add("§e【§c使用方法§e】:");
        lore.add("§f    - §7放到熔炉内,烧制武器/护甲");
        lore.add("§f    - §d(必须是满耐久装备)");
        lore.add("§f==================================");
        lore.add("§e【§d淬炼信息§e】:");
        lore.add("§f    - §6成功几率: §b§l很低");
        lore.add("§f    - §c淬炼失败,随机掉落 §4§l2-3 §c星.");
        lore.add("§f    - §e淬炼成功,装备升星.");
        lore.add("§f==================================");
        lore.add("§7【淬炼升星可大幅度提升 §f护甲/武器 §7属性】");
        id.setLore(lore);
        id.addEnchant(Enchantment.OXYGEN, 1, true);
        i.setItemMeta(id);
        return i;
    }

    public static ItemStack clbs_zhongdeng() {
        ItemStack i = new ItemStack(Material.COAL);
        ItemMeta id = i.getItemMeta();
        id.setDisplayName("§b§l「淬炼石」 - §a§l中等");
        ArrayList<String> lore = new ArrayList<String>();
        lore.add("§f==================================");
        lore.add("§e【§c使用方法§e】:");
        lore.add("§f    - §7放到熔炉内,烧制武器/护甲");
        lore.add("§f    - §d(必须是满耐久装备)");
        lore.add("§f==================================");
        lore.add("§e【§d淬炼信息§e】:");
        lore.add("§f    - §6成功几率: §9§l较低");
        lore.add("§f    - §c淬炼失败,随机掉落 §4§l2-3 §c星.");
        lore.add("§f    - §e淬炼成功,装备升星.");
        lore.add("§f==================================");
        lore.add("§7【淬炼升星可大幅度提升 §f护甲/武器 §7属性】");
        id.setLore(lore);
        id.addEnchant(Enchantment.OXYGEN, 1, true);
        i.setItemMeta(id);
        return i;
    }

    public static ItemStack huaming() {
        ItemStack i = new ItemStack(Material.COAL);
        ItemMeta id = i.getItemMeta();
        id.setDisplayName("§d§l「淬炼吞噬石」");
        ArrayList<String> lore = new ArrayList<String>();
        lore.add("§f==================================");
        lore.add("§e【§c使用方法§e】:");
        lore.add("§f    - §7放到熔炉内,烧制武器/护甲");
        lore.add("§f    - §d(必须是满耐久装备)");
        lore.add("§f    - §e吞噬该装备所有属性..");
        lore.add("§f    - §b随机获得淬炼宝石.");
        lore.add("§f==================================");
        lore.add("§7【吞噬装备后..装备会消失..】");
        id.setLore(lore);
        id.addEnchant(Enchantment.OXYGEN, 1, true);
        i.setItemMeta(id);
        return i;
    }

    /**
     * 创建淬炼直升棒
     *
     * @param level 直升等级
     * @return 淬炼直升棒物品
     */
    public static ItemStack createCuilianDirectRod(int level) {
        ItemStack i = new ItemStack(Material.STICK);
        ItemMeta id = i.getItemMeta();
        id.setDisplayName("§5§l「淬炼直升棒」 §b§l- §f§l" + level + "星");
        ArrayList<String> lore = new ArrayList<String>();
        lore.add("§f==================================");
        lore.add("§e【§c使用方法§e】:");
        lore.add("§f    - §7放到熔炉内,烧制武器/护甲");
        lore.add("§f    - §d(必须是满耐久装备)");
        lore.add("§f==================================");
        lore.add("§e【§d淬炼信息§e】:");
        lore.add("§f    - §6成功几率: §d§l80%");
        lore.add("§f    - §e淬炼成功,直升 §c§l" + level + " §e星");
        lore.add("§f    - §b淬炼失败,无任何副作用");
        lore.add("§f==================================");
        lore.add("§7【淬炼直升棒可快速提升装备星级】");
        id.setLore(lore);
        id.addEnchant(Enchantment.OXYGEN, 1, true);
        i.setItemMeta(id);
        return i;
    }

    /**
     * 创建淬炼符咒
     *
     * @param level 符咒等级
     * @return 淬炼符咒物品
     */
    public static ItemStack createCuilianCharm(int level) {
        ItemStack i = new ItemStack(Material.PAPER);
        ItemMeta id = i.getItemMeta();
        id.setDisplayName("§5§l「淬炼符咒」 §b§l- §f§l" + level);
        ArrayList<String> lore = new ArrayList<String>();
        lore.add("§f==================================");
        lore.add("§e【§c使用方法§e】:");
        lore.add("§f    - §7放到熔炉内,烧制武器/护甲");
        lore.add("§f    - §d(必须是满耐久装备)");
        lore.add("§f==================================");
        lore.add("§e【§d淬炼信息§e】:");
        lore.add("§f    - §6成功几率: §d§l40%");
        lore.add("§f    - §e淬炼成功,提升 §c§l" + level + " §e级");
        lore.add("§f    - §b淬炼失败,无任何副作用");
        lore.add("§f==================================");
        lore.add("§7【淬炼符咒可提升装备星级】");
        id.setLore(lore);
        id.addEnchant(Enchantment.OXYGEN, 1, true);
        i.setItemMeta(id);
        return i;
    }

    public static ItemStack clbs_gaodeng() {
        ItemStack i = new ItemStack(Material.COAL);
        ItemMeta id = i.getItemMeta();
        id.setDisplayName("§5§l「淬炼石」 - §b§l高等");
        ArrayList<String> lore = new ArrayList<String>();
        lore.add("§f==================================");
        lore.add("§e【§c使用方法§e】:");
        lore.add("§f    - §7放到熔炉内,烧制武器/护甲");
        lore.add("§f    - §d(必须是满耐久装备)");
        lore.add("§f==================================");
        lore.add("§e【§d淬炼信息§e】:");
        lore.add("§f    - §6成功几率: §5§l较高");
        lore.add("§f    - §c淬炼失败,随机掉落 §4§l2-3 §c星.");
        lore.add("§f    - §e淬炼成功,装备升星.");
        lore.add("§f==================================");
        lore.add("§7【淬炼升星可大幅度提升 §f护甲/武器 §7属性】");
        id.setLore(lore);
        id.addEnchant(Enchantment.OXYGEN, 1, true);
        i.setItemMeta(id);
        return i;
    }

    public static ItemStack clbs_yuangu() {
        ItemStack i = new ItemStack(Material.STICK);
        ItemMeta id = i.getItemMeta();
        id.setDisplayName("§5§l「淬炼符咒」 §b§l- §f§l18");
        ArrayList<String> lore = new ArrayList<String>();
        lore.add("§f==================================");
        lore.add("§e【§c使用方法§e】:");
        lore.add("§f    - §7放到熔炉内,烧制武器/护甲");
        lore.add("§f    - §d(必须是满耐久装备)");
        lore.add("§f==================================");
        lore.add("§e【§d淬炼信息§e】:");
        lore.add("§f    - §6成功几率: §d§l40%");
        lore.add("§f    - §e淬炼成功,直升 §c§l18 §e星");
        lore.add("§f    - §b淬炼失败,无任何副作用");
        lore.add("§f==================================");
        lore.add("§7【淬炼升星可大幅度提升 §f护甲/武器 §7属性】");
        id.setLore(lore);
        id.addEnchant(Enchantment.OXYGEN, 1, true);
        i.setItemMeta(id);
        return i;
    }

    public static ItemStack clbs_wanmei() {
        ItemStack i = new ItemStack(Material.COAL);
        ItemMeta id = i.getItemMeta();
        id.setDisplayName("§6§l「淬炼石」 - §5§l上等");
        ArrayList<String> lore = new ArrayList<String>();
        lore.add("§f==================================");
        lore.add("§e【§c使用方法§e】:");
        lore.add("§f    - §7放到熔炉内,烧制武器/护甲");
        lore.add("§f    - §d(必须是满耐久装备)");
        lore.add("§f==================================");
        lore.add("§e【§d淬炼信息§e】:");
        lore.add("§f    - §6成功几率: §6§l很高");
        lore.add("§f    - §c淬炼失败,随机掉落 §b§l0-3 §c星.");
        lore.add("§f    - §e淬炼成功,装备升星.");
        lore.add("§f==================================");
        lore.add("§7【淬炼升星可大幅度提升 §f护甲/武器 §7属性】");
        id.setLore(lore);
        id.addEnchant(Enchantment.OXYGEN, 1, true);
        i.setItemMeta(id);
        return i;
    }

    private class Commander
            implements CommandExecutor {
        private Commander() {
        }

        public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
            Player p;

            // 检查是否是玩家可用的命令
            boolean isPlayerCommand = false;
            if (args.length == 0) {
                // 显示帮助命令，所有人都可以使用
                isPlayerCommand = true;
            } else if (args.length >= 1) {
                String firstArg = args[0].toLowerCase();
                if (firstArg.equals("effect") || firstArg.equals("suit") || firstArg.equals("gui")) {
                    // effect、suit和gui命令玩家可以使用
                    isPlayerCommand = true;
                }
            }

            // 如果不是OP且不是玩家命令，则拒绝
            if (!sender.isOp() && !isPlayerCommand) {
                sender.sendMessage("§c§l此命令仅限管理员使用！");
                return true;
            }
            if (args.length == 1 && args[0].equals("opensetui")) {
                setui.setVisible(true);
            }
            if (args.length == 1 && args[0].equals("name") && sender instanceof Player) {
                ItemStack arr = ((Player) sender).getItemInHand();
                if (arr != null && arr.getType() != Material.AIR) {
                    if (arr.getItemMeta() != null && arr.getItemMeta().getDisplayName() != null
                            && (arr.getItemMeta().getDisplayName().indexOf("生命果实") > -1
                                    || arr.getItemMeta().getDisplayName().indexOf("淬炼符咒") > -1)) {
                        sender.sendMessage("§c§l小样？？ 还以为改名字就能刷BUG吗？？ - By 稀饭.");
                    } else if (arr.getItemMeta() != null && arr.getItemMeta().getDisplayName() != null) {
                        ItemMeta str = arr.getItemMeta();
                        if (str.getDisplayName().indexOf("->") > -1) {
                            str.setDisplayName(str.getDisplayName().replace("&", "§"));
                        } else {
                            str.setDisplayName("§f-> " + str.getDisplayName().replace("&", "§") + " §f<-");
                        }
                        arr.setItemMeta(str);
                        sender.sendMessage("§a§l更新彩色名称成功！ " + arr.getItemMeta().getDisplayName());
                    }
                } else {
                    sender.sendMessage("§c§l修改失败....(请检查手中是否存在物品...或不能修改敏感词汇");
                }
            }
            if (args.length == 2 && args[0].equals("set")) {
                if (!sender.isOp()) {
                    sender.sendMessage("§c§l此命令仅限管理员使用！");
                    return true;
                }
                p = (Player) sender;
                p.sendMessage(String.valueOf(p.getHealthScale()));
                p.setItemInHand(
                        Cuilian.this.setItemCuilianDJWithPlayer(p.getItemInHand(), Integer.parseInt(args[1]), p));

                // 检查玩家套装状态并更新lizi哈希表（需要装备+武器）
                int zbdj = Cuilian.checkPlayerZBCL(p); // 检测装备等级
                int wqdj = Cuilian.checkPlayerWQCL(p); // 检测武器等级
                int tzdj = Cuilian.getCLTZ(zbdj, wqdj); // 计算套装等级
                if (tzdj >= 6) {
                    // 检查全局特效开关和玩家个人特效设置
                    boolean globalEffectsEnabled = Cuilian.config.getBoolean("effects", true);
                    boolean playerEffectsEnabled = cn.winde.cuilian.suit.SuitManager.isPlayerEffectEnabled(p.getName());

                    if (globalEffectsEnabled && playerEffectsEnabled) {
                        // 全局和个人特效都启用，激活特效
                        Cuilian.lizi.put(p.getName(), tzdj);
                        p.sendMessage("§a§l套装特效已激活！等级: " + tzdj);
                        p.sendMessage("§7§l装备等级: " + zbdj + "星, 武器等级: " + wqdj + "星");
                    } else if (!globalEffectsEnabled) {
                        // 全局特效被禁用
                        p.sendMessage("§a§l检测到" + tzdj + "星套装");
                        p.sendMessage("§c§l服务器已禁用特效显示");
                        p.sendMessage("§7§l套装属性正常生效，但无法显示特效");
                        p.sendMessage("§7§l装备等级: " + zbdj + "星, 武器等级: " + wqdj + "星");
                    } else {
                        // 玩家个人特效被关闭
                        p.sendMessage("§a§l检测到" + tzdj + "星套装");
                        p.sendMessage("§7§l套装特效已关闭，使用 §e/cuilian effect on §7开启");
                        p.sendMessage("§7§l装备等级: " + zbdj + "星, 武器等级: " + wqdj + "星");
                    }
                } else {
                    // 如果套装等级不足，移除特效
                    if (Cuilian.lizi.containsKey(p.getName())) {
                        Cuilian.lizi.remove(p.getName());
                        p.sendMessage("§c§l套装特效已移除");
                        p.sendMessage("§c§l需要装备和武器都达到6星以上才能激活特效");
                        p.sendMessage("§7§l当前装备等级: " + zbdj + "星, 武器等级: " + wqdj + "星");
                    }
                }
            }
            if (args.length == 2 && args[0].equals("setall")) {
                if (!sender.isOp()) {
                    sender.sendMessage("§c§l此命令仅限管理员使用！");
                    return true;
                }
                p = (Player) sender;
                int level = Integer.valueOf(args[1]);

                // 设置头盔
                ItemStack helmet = p.getInventory().getHelmet();
                if (helmet != null && helmet.getType() != Material.AIR) {
                    p.getInventory().setHelmet(Cuilian.this.setItemCuilianDJWithPlayer(helmet, level, p));
                }

                // 设置胸甲
                ItemStack chestplate = p.getInventory().getChestplate();
                if (chestplate != null && chestplate.getType() != Material.AIR) {
                    p.getInventory().setChestplate(Cuilian.this.setItemCuilianDJWithPlayer(chestplate, level, p));
                }

                // 设置护腿
                ItemStack leggings = p.getInventory().getLeggings();
                if (leggings != null && leggings.getType() != Material.AIR) {
                    p.getInventory().setLeggings(Cuilian.this.setItemCuilianDJWithPlayer(leggings, level, p));
                }

                // 设置靴子
                ItemStack boots = p.getInventory().getBoots();
                if (boots != null && boots.getType() != Material.AIR) {
                    p.getInventory().setBoots(Cuilian.this.setItemCuilianDJWithPlayer(boots, level, p));
                }

                // 检查玩家套装状态并更新lizi哈希表（需要装备+武器）
                int zbdj = Cuilian.checkPlayerZBCL(p); // 检测装备等级
                int wqdj = Cuilian.checkPlayerWQCL(p); // 检测武器等级
                int tzdj = Cuilian.getCLTZ(zbdj, wqdj); // 计算套装等级
                p.sendMessage("§a§l已将所有装备设置为" + level + "星！");
                if (tzdj >= 6) {
                    // 更新lizi哈希表以激活特效
                    Cuilian.lizi.put(p.getName(), tzdj);
                    p.sendMessage("§a§l套装特效已激活！等级: " + tzdj);
                    p.sendMessage("§7§l装备等级: " + zbdj + "星, 武器等级: " + wqdj + "星");
                } else {
                    p.sendMessage("§c§l套装特效未激活");
                    p.sendMessage("§c§l需要装备和武器都达到6星以上才能激活特效");
                    p.sendMessage("§7§l当前装备等级: " + zbdj + "星, 武器等级: " + wqdj + "星");
                }
            }

            if (args.length >= 2 && args[0].equals("effect")) {
                p = (Player) sender;
                String action = args[1].toLowerCase();

                if (action.equals("on")) {
                    // 检查全局特效开关
                    boolean globalEffectsEnabled = Cuilian.config.getBoolean("effects", true);
                    if (!globalEffectsEnabled) {
                        p.sendMessage("§c§l服务器已禁用特效显示");
                        p.sendMessage("§7§l无法开启个人特效，请联系管理员");
                        return true;
                    }

                    // 开启玩家特效显示
                    SuitManager.setPlayerEffectEnabled(p.getName(), true);
                    p.sendMessage("§a§l特效显示已开启！");

                    // 检查当前套装状态
                    String namedSuit = SuitManager.getPlayerSuit(p.getName());
                    if (namedSuit != null) {
                        p.sendMessage("§a§l检测到套装: §e" + namedSuit + " §a§l特效已激活！");
                    } else {
                        int zbdj = Cuilian.checkPlayerZBCL(p); // 检测装备等级
                        int wqdj = Cuilian.checkPlayerWQCL(p); // 检测武器等级
                        int tzdj = Cuilian.getCLTZ(zbdj, wqdj); // 计算套装等级
                        if (tzdj >= 6) {
                            p.sendMessage("§a§l检测到" + tzdj + "星套装特效已激活！");
                            p.sendMessage("§7§l装备等级: " + zbdj + "星, 武器等级: " + wqdj + "星");
                        } else {
                            p.sendMessage("§7§l当前没有可激活的套装特效");
                            p.sendMessage("§7§l装备等级: " + zbdj + "星, 武器等级: " + wqdj + "星");
                            p.sendMessage("§7§l需要装备和武器都达到6星以上才能激活特效");
                        }
                    }
                } else if (action.equals("off")) {
                    // 关闭玩家特效显示
                    SuitManager.setPlayerEffectEnabled(p.getName(), false);
                    p.sendMessage("§c§l特效显示已关闭！");
                } else if (action.equals("status")) {
                    // 查看特效状态
                    boolean enabled = SuitManager.isPlayerEffectEnabled(p.getName());
                    p.sendMessage("§6§l=== 特效状态 ===");
                    p.sendMessage("§a特效显示: " + (enabled ? "§2§l开启" : "§c§l关闭"));

                    String namedSuit = SuitManager.getPlayerSuit(p.getName());
                    if (namedSuit != null) {
                        p.sendMessage("§a当前套装: §e" + namedSuit);
                        p.sendMessage("§a特效状态: " + (enabled ? "§2§l激活中" : "§7§l已关闭"));
                    } else {
                        int zbdj = Cuilian.checkPlayerZBCL(p); // 检测装备等级
                        int wqdj = Cuilian.checkPlayerWQCL(p); // 检测武器等级
                        int tzdj = Cuilian.getCLTZ(zbdj, wqdj); // 计算套装等级
                        if (tzdj >= 6) {
                            p.sendMessage("§a淬炼套装: §e" + tzdj + "星");
                            p.sendMessage("§a特效状态: " + (enabled ? "§2§l激活中" : "§7§l已关闭"));
                            p.sendMessage("§7装备等级: " + zbdj + "星, 武器等级: " + wqdj + "星");
                        } else {
                            p.sendMessage("§7当前没有可激活的套装特效");
                            p.sendMessage("§7装备等级: " + zbdj + "星, 武器等级: " + wqdj + "星");
                            p.sendMessage("§7需要装备和武器都达到6星以上才能激活特效");
                        }
                    }
                } else if (action.equals("6") || action.equals("9") || action.equals("12") ||
                        action.equals("15") || action.equals("18") || action.equals("19")) {
                    // 管理员命令：设置指定等级的特效
                    if (!p.isOp()) {
                        p.sendMessage("§c§l此命令仅限管理员使用！");
                        return true;
                    }

                    int level = Integer.parseInt(action);
                    Cuilian.lizi.put(p.getName(), level);
                    SuitManager.setPlayerEffectEnabled(p.getName(), true);

                    // 如果有第三个参数，设置特效类型
                    if (args.length >= 3) {
                        String effectType = args[2].toLowerCase();
                        if (effectType.equals("wings") || effectType.equals("halo") ||
                                effectType.equals("flame") || effectType.equals("nebula") ||
                                effectType.equals("tornado") || effectType.equals("stars")) {

                            // 临时修改配置文件中的特效类型
                            config.set("eff.leve" + level + ".effect_type", effectType);
                            p.sendMessage("§a§l套装特效已手动设置为" + level + "级，类型: " + effectType + "！");
                        } else {
                            p.sendMessage("§c§l无效的特效类型！可用类型: wings, halo, flame, nebula, tornado, stars");
                        }
                    } else {
                        p.sendMessage("§a§l套装特效已手动设置为" + level + "级！");
                    }
                } else if (action.equals("type") && args.length >= 3) {
                    // 管理员命令：设置特效类型
                    if (!p.isOp()) {
                        p.sendMessage("§c§l此命令仅限管理员使用！");
                        return true;
                    }

                    String effectType = args[2].toLowerCase();
                    if (effectType.equals("wings") || effectType.equals("halo") ||
                            effectType.equals("flame") || effectType.equals("nebula") ||
                            effectType.equals("tornado") || effectType.equals("stars")) {

                        if (Cuilian.lizi.containsKey(p.getName())) {
                            int level = Cuilian.lizi.get(p.getName());
                            // 临时修改配置文件中的特效类型
                            config.set("eff.leve" + level + ".effect_type", effectType);
                            p.sendMessage("§a§l套装特效类型已设置为: " + effectType);
                        } else {
                            // 如果玩家没有激活特效，先激活6级特效
                            Cuilian.lizi.put(p.getName(), 6);
                            SuitManager.setPlayerEffectEnabled(p.getName(), true);
                            // 临时修改配置文件中的特效类型
                            config.set("eff.leve6.effect_type", effectType);
                            p.sendMessage("§a§l已激活6级套装特效，类型: " + effectType);
                        }
                    } else {
                        p.sendMessage("§c§l无效的特效类型！可用类型: wings, halo, flame, nebula, tornado, stars");
                    }
                } else {
                    p.sendMessage("§c§l无效的参数！");
                    p.sendMessage("§e§l玩家命令:");
                    p.sendMessage("§e§l  /cuilian effect on - 开启特效显示");
                    p.sendMessage("§e§l  /cuilian effect off - 关闭特效显示");
                    p.sendMessage("§e§l  /cuilian effect status - 查看特效状态");
                    if (p.isOp()) {
                        p.sendMessage("§e§l管理员命令:");
                        p.sendMessage("§e§l  /cuilian effect 6/9/12/15/18/19 [类型] - 设置特效等级和类型");
                        p.sendMessage("§e§l  /cuilian effect type <类型> - 设置特效类型");
                        p.sendMessage(
                                "§e§l可用特效类型: wings(翅膀), halo(光环), flame(火焰), nebula(星云), tornado(龙卷风), stars(星星)");
                    }
                }
            }

            // GUI命令处理
            if (args.length >= 1 && args[0].equals("gui")) {
                if (!(sender instanceof Player)) {
                    sender.sendMessage("§c§l此命令只能由玩家使用！");
                    return true;
                }
                p = (Player) sender;

                if (args.length == 1) {
                    // 默认打开装备管理界面
                    cn.winde.cuilian.gui.InGameGUI.openEquipmentGUI(p);
                    p.sendMessage("§a§l已打开装备管理界面！");
                    return true;
                } else if (args.length == 2) {
                    String guiType = args[1].toLowerCase();
                    switch (guiType) {
                        case "equipment":
                        case "eq":
                            cn.winde.cuilian.gui.InGameGUI.openEquipmentGUI(p);
                            p.sendMessage("§a§l已打开装备管理界面！");
                            break;
                        case "suit":
                        case "preview":
                            cn.winde.cuilian.gui.InGameGUI.openSuitPreviewGUI(p);
                            p.sendMessage("§a§l已打开套装预览界面！");
                            break;
                        case "effect":
                        case "fx":
                            cn.winde.cuilian.gui.InGameGUI.openEffectSettingsGUI(p);
                            p.sendMessage("§a§l已打开特效设置界面！");
                            break;
                        case "enhance":
                        case "info":
                            cn.winde.cuilian.gui.InGameGUI.openEnhanceInfoGUI(p);
                            p.sendMessage("§a§l已打开淬炼信息界面！");
                            break;
                        case "help":
                            p.sendMessage("§6§l=== GUI界面帮助 ===");
                            p.sendMessage("§e/cuilian gui §7- 打开装备管理界面（默认）");
                            p.sendMessage("§e/cuilian gui equipment §7- 打开装备管理界面");
                            p.sendMessage("§e/cuilian gui suit §7- 打开套装预览界面");
                            p.sendMessage("§e/cuilian gui effect §7- 打开特效设置界面");
                            p.sendMessage("§e/cuilian gui enhance §7- 打开淬炼信息界面");
                            break;
                        default:
                            p.sendMessage("§c§l未知的GUI类型！可用类型：equipment, suit, effect, enhance");
                            p.sendMessage("§7使用 §e/cuilian gui help §7查看详细帮助");
                            break;
                    }
                    return true;
                }
            }

            // 套装相关命令
            if (args.length >= 1 && args[0].equals("suit")) {
                if (args.length == 1) {
                    // 显示套装帮助
                    sender.sendMessage(
                            "§c§m§l  §6§m§l  §e§m§l  §a§m§l  §b§m§l  §e§l套装系统§b§m§l  §a§m§l  §e§m§l  §6§m§l  §c§m§l  ");

                    if (sender.isOp()) {
                        // 管理员看到所有命令
                        sender.sendMessage("§c§l▏   §c/cuilian suit list - 查看所有可用套装");
                        sender.sendMessage("§c§l▏   §c/cuilian suit info <套装名> - 查看套装详细信息");
                        sender.sendMessage("§c§l▏   §c/cuilian suit check - 检查当前穿戴的套装");
                        sender.sendMessage("§c§l▏   §c/cuilian suit toggle - 切换套装接收偏好");
                        sender.sendMessage("§c§l▏   §c/cuilian suit preview - 查看预览冷却状态");
                        sender.sendMessage("§c§l▏   §c/cuilian suit give <套装名> <玩家> - 给予玩家套装");
                        sender.sendMessage("§c§l▏   §c/cuilian suit space <玩家> - 检查玩家背包空间");
                    } else {
                        // 普通玩家看到toggle和preview命令
                        sender.sendMessage("§c§l▏   §c/cuilian suit toggle - 切换套装接收偏好");
                        sender.sendMessage("§c§l▏   §c/cuilian suit preview - 查看预览冷却状态");
                    }

                    sender.sendMessage(
                            "§c§m§l  §6§m§l  §e§m§l  §a§m§l  §b§m§l  §e§l套装系统§b§m§l  §a§m§l  §e§m§l  §6§m§l  §c§m§l  ");
                    return true;
                } else if (args.length == 2 && args[1].equals("list")) {
                    // 显示所有套装列表 (管理员命令)
                    if (!sender.isOp()) {
                        sender.sendMessage("§c§l此命令仅限管理员使用！");
                        return true;
                    }
                    sender.sendMessage("§a§l可用套装列表：");
                    for (String suitName : SuitManager.getAllSuits()) {
                        sender.sendMessage("§e- " + suitName);
                    }
                    return true;
                } else if (args.length == 2 && args[1].equals("check")) {
                    // 检查玩家当前套装 (管理员命令)
                    if (!sender.isOp()) {
                        sender.sendMessage("§c§l此命令仅限管理员使用！");
                        return true;
                    }
                    if (!(sender instanceof Player)) {
                        sender.sendMessage("§c§l此命令只能由玩家执行！");
                        return true;
                    }
                    Player player = (Player) sender;
                    String currentSuit = SuitManager.getPlayerSuit(player.getName());
                    if (currentSuit != null) {
                        sender.sendMessage("§a§l当前穿戴套装: §e" + currentSuit);
                        SuitManager.SuitAttribute attribute = SuitManager.getPlayerSuitAttribute(player.getName());
                        if (attribute != null && attribute.description != null) {
                            for (String line : attribute.description) {
                                sender.sendMessage(line);
                            }
                        }
                    } else {
                        sender.sendMessage("§c§l您当前没有穿戴完整套装");
                    }
                    return true;
                } else if (args.length == 3 && args[1].equals("info")) {
                    // 显示套装信息 (管理员命令)
                    if (!sender.isOp()) {
                        sender.sendMessage("§c§l此命令仅限管理员使用！");
                        return true;
                    }
                    String suitName = args[2];
                    SuitManager.SuitAttribute attribute = SuitManager.getSuitAttribute(suitName);
                    if (attribute != null) {
                        // 获取品质配置
                        SuitManager.QualityConfig quality = SuitManager.getQualityConfig(attribute.quality);

                        sender.sendMessage("§6§l=== " + suitName + " 详细信息 ===");
                        sender.sendMessage("§7品质: " + quality.name + " §7(" + quality.description + ")");
                        sender.sendMessage("§c攻击伤害: +" + attribute.attackDamage);
                        sender.sendMessage("§9防御力: +" + attribute.defense);
                        sender.sendMessage("§a生命值: +" + attribute.health);
                        sender.sendMessage("§f移动速度: +" + (int) (attribute.speed * 100) + "%");
                        sender.sendMessage("§e跳跃高度: +" + attribute.jump);
                        sender.sendMessage("§d吸血: +" + attribute.vampire + "%");
                        sender.sendMessage("§b特效类型: " + attribute.effectType);
                        sender.sendMessage("§7装备名称:");
                        sender.sendMessage("§7- 头盔: " + quality.color + attribute.helmet + " §7(宝石等级: §e+"
                                + SuitManager.getGemLevel(suitName, "head") + "§7)");
                        sender.sendMessage("§7- 胸甲: " + quality.color + attribute.chestplate + " §7(宝石等级: §e+"
                                + SuitManager.getGemLevel(suitName, "chest") + "§7)");
                        sender.sendMessage("§7- 护腿: " + quality.color + attribute.leggings + " §7(宝石等级: §e+"
                                + SuitManager.getGemLevel(suitName, "leg") + "§7)");
                        sender.sendMessage("§7- 靴子: " + quality.color + attribute.boots + " §7(宝石等级: §e+"
                                + SuitManager.getGemLevel(suitName, "foot") + "§7)");
                        sender.sendMessage("§7武器名称:");
                        sender.sendMessage("§7- 剑: " + quality.color + attribute.sword + " §7(宝石等级: §e+"
                                + SuitManager.getGemLevel(suitName, "sword") + "§7)");
                        sender.sendMessage("§7- 弓: " + quality.color + attribute.bow + " §7(宝石等级: §e+"
                                + SuitManager.getGemLevel(suitName, "bow") + "§7)");

                        if (attribute.description != null && !attribute.description.isEmpty()) {
                            sender.sendMessage("§a套装描述:");
                            for (String line : attribute.description) {
                                sender.sendMessage(line);
                            }
                        }
                    } else {
                        sender.sendMessage("§c§l套装 '" + suitName + "' 不存在！");
                    }
                    return true;
                } else if (args.length == 2 && args[1].equals("toggle")) {
                    // 玩家切换自己的自动装备偏好
                    if (!(sender instanceof Player)) {
                        sender.sendMessage("§c§l此命令只能由玩家执行！");
                        return true;
                    }

                    Player player = (Player) sender;
                    boolean newMode = SuitManager.togglePlayerAutoEquip(player.getName());

                    player.sendMessage("§a§l您的套装接收偏好已切换为: " + (newMode ? "§2§l自动装备" : "§e§l放入背包"));
                    player.sendMessage("§7现在管理员给您套装时将" + (newMode ? "自动装备到身上" : "放入您的背包"));
                    return true;
                } else if (args.length == 2 && args[1].equals("preview")) {
                    // 查看预览冷却状态
                    if (!(sender instanceof Player)) {
                        sender.sendMessage("§c§l此命令只能由玩家执行！");
                        return true;
                    }

                    Player player = (Player) sender;
                    String playerName = player.getName();

                    // 获取配置信息
                    int previewDuration = Cuilian.config.getInt("suit_preview.duration", 30);
                    int previewCooldown = Cuilian.config.getInt("suit_preview.cooldown", 60);

                    player.sendMessage("§6§l=== 套装预览状态 ===");
                    player.sendMessage("§7预览时间: §e" + previewDuration + "秒");
                    player.sendMessage("§7预览冷却: §c" + previewCooldown + "秒");

                    // 检查当前预览状态
                    if (cn.winde.cuilian.preview.SuitPreviewManager.isPlayerPreviewing(playerName)) {
                        String previewInfo = cn.winde.cuilian.preview.SuitPreviewManager.getPreviewInfo(playerName);
                        player.sendMessage("§a§l当前状态: §e" + previewInfo);
                    } else {
                        // 检查冷却状态
                        if (cn.winde.cuilian.preview.SuitPreviewManager.isPlayerInCooldown(playerName)) {
                            long remainingCooldown = cn.winde.cuilian.preview.SuitPreviewManager
                                    .getRemainingCooldown(playerName);
                            player.sendMessage("§c§l当前状态: §7冷却中 (剩余 " + remainingCooldown + " 秒)");
                        } else {
                            player.sendMessage("§a§l当前状态: §2可以预览");
                        }
                    }

                    player.sendMessage("§7使用 §e/cuilian gui suit §7打开预览界面");
                    return true;
                } else if (args.length == 3 && args[1].equals("space")) {
                    // 检查玩家背包空间 (管理员命令)
                    if (!sender.isOp()) {
                        sender.sendMessage("§c§l此命令仅限管理员使用！");
                        return true;
                    }
                    String playerName = args[2];
                    Player targetPlayer = Bukkit.getPlayer(playerName);

                    if (targetPlayer == null) {
                        sender.sendMessage("§c§l玩家 '" + playerName + "' 不在线！");
                        return true;
                    }

                    int emptySlots = SuitManager.getPlayerEmptySlots(targetPlayer);
                    boolean hasSpace = SuitManager.hasEnoughSpaceForSuit(targetPlayer);

                    sender.sendMessage("§6§l=== 玩家背包空间检查 ===");
                    sender.sendMessage("§a玩家: §e" + playerName);
                    sender.sendMessage("§a空格子数量: §e" + emptySlots + "§7/36");
                    sender.sendMessage("§a套装空间: " + (hasSpace ? "§2§l充足" : "§c§l不足"));
                    if (!hasSpace) {
                        sender.sendMessage("§c需要至少6个空格子来放置套装和武器");
                    }
                    return true;
                } else if (args.length == 4 && args[1].equals("give")) {
                    // 给予玩家套装 (管理员命令)
                    if (!sender.isOp()) {
                        sender.sendMessage("§c§l此命令仅限管理员使用！");
                        return true;
                    }
                    String suitName = args[2];
                    String playerName = args[3];
                    Player targetPlayer = Bukkit.getPlayer(playerName);

                    if (targetPlayer == null) {
                        sender.sendMessage("§c§l玩家 '" + playerName + "' 不在线！");
                        return true;
                    }

                    SuitManager.SuitAttribute attribute = SuitManager.getSuitAttribute(suitName);
                    if (attribute == null) {
                        sender.sendMessage("§c§l套装 '" + suitName + "' 不存在！");
                        return true;
                    }

                    // 给予套装装备
                    boolean success = SuitManager.giveSuitToPlayer(targetPlayer, suitName);
                    if (success) {
                        sender.sendMessage("§a§l已给予玩家 " + playerName + " 套装: §e" + suitName);
                        targetPlayer.sendMessage("§a§l您获得了套装: §e" + suitName);

                        // 根据玩家偏好发送不同的消息
                        if (SuitManager.isPlayerAutoEquipEnabled(targetPlayer.getName())) {
                            sender.sendMessage("§a§l套装已自动装备，特效将在1秒后激活！");
                            targetPlayer.sendMessage("§a§l套装已自动装备，特效即将激活！");
                        } else {
                            sender.sendMessage("§a§l套装已放入玩家背包！");
                            targetPlayer.sendMessage("§a§l套装已放入背包，请手动穿戴以激活特效！");
                        }
                    } else {
                        // 根据玩家偏好给出不同的失败原因
                        if (SuitManager.isPlayerAutoEquipEnabled(targetPlayer.getName())) {
                            sender.sendMessage("§c§l给予套装失败！请检查服务器状态。");
                        } else {
                            sender.sendMessage("§c§l给予套装失败！玩家背包空间不足，需要至少4个空格子。");
                            sender.sendMessage("§7§l提示: 玩家可以使用 §e/cuilian suit toggle §7切换为自动装备模式");
                        }
                    }
                    return true;
                }
            }

            if (args.length == 0) {
                if (sender.isOp()) {
                    // 管理员看到所有命令
                    sender.sendMessage(
                            "§c§m§l  §6§m§l  §e§m§l  §a§m§l  §b§m§l  §e§l淬炼指令帮助§m§l  §a§m§l  §e§m§l  §6§m§l  §c§m§l  ");
                    sender.sendMessage("§a§l玩家命令:");
                    sender.sendMessage("§c§l▏   §c/cuilian effect on/off/status - 特效开关和状态");
                    sender.sendMessage("§c§l▏   §c/cuilian suit - 套装系统命令");
                    sender.sendMessage("§c§l▏   §c/cuilian gui [类型] - 打开游戏内GUI界面");
                    sender.sendMessage("§e§l管理员命令:");
                    sender.sendMessage("§c§l▏   §c/cuilian 品质 数量 玩家 - 给予淬炼石");
                    sender.sendMessage("§c§l▏   §c/cuilian stone <类型> <数量> <玩家> - 给予淬炼石");
                    sender.sendMessage("§c§l▏   §c/cuilian rod <等级> <数量> <玩家> - 给予淬炼直升棒");
                    sender.sendMessage("§c§l▏   §c/cuilian set 等级 - 设置手持装备等级");
                    sender.sendMessage("§c§l▏   §c/cuilian setall 等级 - 设置全身装备等级");
                    sender.sendMessage("§c§l▏   §c/cuilian reload - 重载配置");
                    sender.sendMessage("§c§l▏   §c/cuilian opensetui - 打开UI设置界面（在服务端）");
                    sender.sendMessage(
                            "§c§m§l  §6§m§l  §e§m§l  §a§m§l  §b§m§l  §e§l淬炼指令帮助§b§m§l  §a§m§l  §e§m§l  §6§m§l  §c§m§l  ");
                } else {
                    // 普通玩家只看到玩家命令
                    sender.sendMessage(
                            "§c§m§l  §6§m§l  §e§m§l  §a§m§l  §b§m§l  §e§l淬炼指令帮助§b§m§l  §a§m§l  §e§m§l  §6§m§l  §c§m§l  ");
                    sender.sendMessage("§a§l可用命令:");
                    sender.sendMessage("§c§l▏   §c/cuilian effect on/off/status - 特效开关和状态");
                    sender.sendMessage("§c§l▏   §c/cuilian suit toggle - 切换套装接收偏好");
                    sender.sendMessage("§c§l▏   §c/cuilian gui [类型] - 打开游戏内GUI界面");
                    sender.sendMessage(
                            "§c§m§l  §6§m§l  §e§m§l  §a§m§l  §b§m§l  §e§l淬炼指令帮助§b§m§l  §a§m§l  §e§m§l  §6§m§l  §c§m§l  ");
                }
                return true;
            }
            if (args.length == 1 && args[0].equals("reload")) {
                if (!sender.isOp()) {
                    sender.sendMessage("§c§l此命令仅限管理员使用！");
                    return true;
                }
                p = (Player) sender;

                // 重新加载配置文件
                Cuilian.this.reloadConfig();
                Cuilian.this.loadyml();

                // 延迟通知界面更新，确保配置完全加载
                Bukkit.getScheduler().runTaskLater(Cuilian.this, () -> {
                    cn.winde.cuilian.gui.InGameGUIListener.notifyConfigReload();
                }, 1L);

                p.sendMessage("重载完成！");
            }
            // 新的友好指令：/cuilian stone <类型> <数量> <玩家>
            if (args.length == 4 && args[0].equals("stone")) {
                if (!sender.isOp()) {
                    sender.sendMessage("§c§l此命令仅限管理员使用！");
                    return true;
                }
                Player gp = Bukkit.getPlayer(args[3]);
                if (gp == null) {
                    sender.sendMessage("§c玩家不在线");
                    return true;
                }

                try {
                    int amount = Integer.parseInt(args[2]);
                    String stoneType = args[1].toLowerCase();
                    ItemStack item = null;
                    String typeName = "";

                    switch (stoneType) {
                        case "普通":
                        case "putong":
                        case "normal":
                        case "1":
                            item = Cuilian.clbs_putong();
                            typeName = "普通淬炼石";
                            break;
                        case "中等":
                        case "zhongdeng":
                        case "medium":
                        case "2":
                            item = Cuilian.clbs_zhongdeng();
                            typeName = "中等淬炼石";
                            break;
                        case "高等":
                        case "gaodeng":
                        case "high":
                        case "3":
                            item = Cuilian.clbs_gaodeng();
                            typeName = "高等淬炼石";
                            break;
                        case "上等":
                        case "wanmei":
                        case "perfect":
                        case "4":
                            item = Cuilian.clbs_wanmei();
                            typeName = "上等淬炼石";
                            break;
                        case "符咒":
                        case "yuangu":
                        case "charm":
                        case "5":
                            item = Cuilian.clbs_yuangu();
                            typeName = "淬炼符咒";
                            break;
                        case "吞噬":
                        case "tunshi":
                        case "devour":
                        case "6":
                            item = Cuilian.huaming();
                            typeName = "淬炼吞噬石";
                            break;
                        default:
                            sender.sendMessage("§c无效的淬炼石类型！可用类型：普通、中等、高等、上等、符咒、吞噬");
                            return true;
                    }

                    if (item != null) {
                        item.setAmount(amount);
                        gp.getInventory().addItem(item);
                        sender.sendMessage("§a成功给予玩家 §e" + gp.getName() + " §a" + amount + "个 §6" + typeName);
                        gp.sendMessage("§a你获得了 §6" + amount + "个 " + typeName);
                    }
                } catch (NumberFormatException e) {
                    sender.sendMessage("§c数量必须是数字！");
                }
                return true;
            }

            // 新的友好指令：/cuilian rod <等级> <数量> <玩家>
            if (args.length == 4 && args[0].equals("rod")) {
                if (!sender.isOp()) {
                    sender.sendMessage("§c§l此命令仅限管理员使用！");
                    return true;
                }
                Player gp = Bukkit.getPlayer(args[3]);
                if (gp == null) {
                    sender.sendMessage("§c玩家不在线");
                    return true;
                }

                try {
                    int level = Integer.parseInt(args[1]);
                    int amount = Integer.parseInt(args[2]);

                    if (level < 1) {
                        sender.sendMessage("§c等级必须大于0！");
                        return true;
                    }

                    // 动态检测最大等级，支持UI配置的新星级
                    int maxLevel = Cuilian.getMaxConfiguredLevel();
                    if (level > maxLevel) {
                        sender.sendMessage("§c等级不能超过 " + maxLevel + "！当前最高配置星级为 " + maxLevel + " 星");
                        return true;
                    }

                    ItemStack item = Cuilian.createCuilianDirectRod(level);
                    item.setAmount(amount);
                    gp.getInventory().addItem(item);
                    sender.sendMessage("§a成功给予玩家 §e" + gp.getName() + " §a" + amount + "个 §6" + level + "星淬炼直升棒");
                    gp.sendMessage("§a你获得了 §6" + amount + "个 " + level + "星淬炼直升棒");
                } catch (NumberFormatException e) {
                    sender.sendMessage("§c等级和数量必须是数字！");
                }
                return true;
            }

            // 新的符咒命令：/cuilian charm <等级> <数量> <玩家>
            if (args.length == 4 && args[0].equals("charm")) {
                if (!sender.isOp()) {
                    sender.sendMessage("§c§l此命令仅限管理员使用！");
                    return true;
                }
                Player gp = Bukkit.getPlayer(args[3]);
                if (gp == null) {
                    sender.sendMessage("§c玩家不在线");
                    return true;
                }

                try {
                    int level = Integer.parseInt(args[1]);
                    int amount = Integer.parseInt(args[2]);

                    if (level < 1) {
                        sender.sendMessage("§c等级必须大于0！");
                        return true;
                    }

                    // 动态检测最大等级，支持UI配置的新星级
                    int maxLevel = Cuilian.getMaxConfiguredLevel();
                    if (level > maxLevel) {
                        sender.sendMessage("§c等级不能超过 " + maxLevel + "！当前最高配置星级为 " + maxLevel + " 星");
                        return true;
                    }

                    ItemStack item = Cuilian.createCuilianCharm(level);
                    item.setAmount(amount);
                    gp.getInventory().addItem(item);
                    sender.sendMessage("§a成功给予玩家 §e" + gp.getName() + " §a" + amount + "个 §6" + level + "星淬炼符咒");
                    gp.sendMessage("§a你获得了 §6" + amount + "个 " + level + "星淬炼符咒");
                } catch (NumberFormatException e) {
                    sender.sendMessage("§c等级和数量必须是数字！");
                }
                return true;
            }

            // TPS测试命令
            if (args.length == 1 && args[0].equals("tpstest")) {
                if (!sender.isOp()) {
                    sender.sendMessage("§c§l此命令仅限管理员使用！");
                    return true;
                }

                String status = TPSMonitor.getTPSStatus();
                sender.sendMessage("§e§l[TPS监控] " + status);

                boolean isDisabled = TPSMonitor.isEffectDisabledByTPS();
                sender.sendMessage("§e§l[特效状态] " + (isDisabled ? "§c因TPS过低被禁用" : "§a正常"));

                boolean autoEnabled = Cuilian.config.getBoolean("tps_auto_effect.enabled", false);
                double threshold = Cuilian.config.getDouble("tps_auto_effect.threshold", 15.0);
                sender.sendMessage("§e§l[自动开关] " + (autoEnabled ? "§a启用" : "§7禁用") + " §e§l[阈值] §f" + threshold);

                return true;
            }

            // 传统数字命令处理 (只处理纯数字的第一个参数)
            if (args.length == 3 && !args[0].equals("suit")) {
                // 检查第一个参数是否为数字，避免与套装命令冲突
                try {
                    Integer.parseInt(args[0]); // 尝试解析第一个参数为数字
                } catch (NumberFormatException e) {
                    // 如果第一个参数不是数字，跳过这个处理逻辑
                    return true;
                }

                if (!sender.isOp()) {
                    sender.sendMessage("§c§l此命令仅限管理员使用！");
                    return true;
                }
                Player gp = Bukkit.getPlayer((String) args[2]);
                if (gp == null) {
                    sender.sendMessage("§c玩家不在线");
                    return true;
                }

                try {
                    int sl = Integer.parseInt(args[1]);
                    if (args[0].equals("1")) {
                        ItemStack item = Cuilian.clbs_putong();
                        item.setAmount(sl);
                        gp.getInventory().addItem(new ItemStack[] { item });
                    } else if (args[0].equals("2")) {
                        ItemStack item = Cuilian.clbs_zhongdeng();
                        item.setAmount(sl);
                        gp.getInventory().addItem(new ItemStack[] { item });
                    } else if (args[0].equals("3")) {
                        ItemStack item = Cuilian.clbs_gaodeng();
                        item.setAmount(sl);
                        gp.getInventory().addItem(new ItemStack[] { item });
                    } else if (args[0].equals("4")) {
                        ItemStack item = Cuilian.clbs_wanmei();
                        item.setAmount(sl);
                        gp.getInventory().addItem(new ItemStack[] { item });
                    } else if (args[0].equals("6")) {
                        ItemStack item = Cuilian.huaming();
                        item.setAmount(sl);
                        gp.getInventory().addItem(new ItemStack[] { item });
                    } else if (args[0].equals("5")) {
                        ItemStack item = Cuilian.clbs_yuangu();
                        item.setAmount(sl);
                        gp.getInventory().addItem(new ItemStack[] { item });
                    } else if (args[0].equals("11")) {
                        gp.getInventory().addItem(new ItemStack[] { Clbh.clbh_3() });
                    } else if (args[0].equals("12")) {
                        gp.getInventory().addItem(new ItemStack[] { Clbh.clbh_6() });
                    } else if (args[0].equals("13")) {
                        gp.getInventory().addItem(new ItemStack[] { Clbh.clbh_9() });
                    } else if (args[0].equals("14")) {
                        gp.getInventory().addItem(new ItemStack[] { Clbh.clbh_12() });
                    }
                } catch (NumberFormatException e) {
                    sender.sendMessage("§c数量必须是数字！");
                }
            }
            return true;
        }
    }
}