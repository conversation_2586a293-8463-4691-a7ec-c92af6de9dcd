/*
 * Decompiled with CFR 0.152.
 *
 * Could not load the following classes:
 *  cn.winde.baoshi.baoshi
 *  org.bukkit.Material
 *  org.bukkit.enchantments.Enchantment
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.event.inventory.InventoryType
 *  org.bukkit.event.player.PlayerQuitEvent
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 */
package cn.winde.cuilian.clbh;

import cn.winde.baoshi.baoshi;
import java.util.ArrayList;
import java.util.HashMap;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryType;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

public class Clbh
implements Listener {
    private static HashMap<String, Integer> clbhing = new HashMap();
    private int[] list = new int[]{261, 267, 268, 272, 276, 283, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317};

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent e) {
        if (clbhing.containsKey(e.getPlayer().getName())) {
            clbhing.remove(e.getPlayer().getName());
        }
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent e) {
        if (!e.isRightClick()) {
            return;
        }
        Player p = (Player)e.getWhoClicked();
        ItemStack item = e.getCurrentItem();
        if (clbhing.containsKey(p.getName())) {
            if (e.getInventory().getType() != InventoryType.CRAFTING) {
                p.sendMessage("§f[§d淬炼保护符§f]§c请在背包内右键");
                return;
            }
            if (item == null || item.getType().equals((Object)Material.AIR)) {
                e.setCancelled(true);
                p.closeInventory();
                p.sendMessage("§f[§d淬炼保护符§f]§c你取消了本次淬炼保护!");
                clbhing.remove(p.getName());
                return;
            }
            boolean yunxu = false;
            int i = 0;
            while (i < this.list.length) {
                if (item.getTypeId() == this.list[i]) {
                    yunxu = true;
                }
                ++i;
            }
            if (!yunxu) {
                p.sendMessage("§f[§d淬炼保护符§f]§c这个物品不能够进行淬炼保护.");
                e.setCancelled(true);
                return;
            }
            if (clbhing.get(p.getName()) == 3) {
                if (!baoshi.TakePlayerItem((Player)p, (ItemStack)Clbh.clbh_3())) {
                    p.sendMessage("§c所需物品不足。");
                    e.setCancelled(true);
                    p.closeInventory();
                    p.sendMessage("§f[§d淬炼保护符§f]§c你取消了本次淬炼保护!");
                    clbhing.remove(p.getName());
                    return;
                }
                this.clbh(p, "§a三星淬炼保护", item);
                p.sendMessage("§a§l附加淬炼保护成功,§d成功对装备附加:§a三星淬炼保护");
            } else if (clbhing.get(p.getName()) == 6) {
                if (!baoshi.TakePlayerItem((Player)p, (ItemStack)Clbh.clbh_6())) {
                    p.sendMessage("§c所需物品不足。");
                    e.setCancelled(true);
                    p.closeInventory();
                    p.sendMessage("§f[§d淬炼保护符§f]§c你取消了本次淬炼保护!");
                    clbhing.remove(p.getName());
                    return;
                }
                this.clbh(p, "§3六星淬炼保护", item);
                p.sendMessage("§a§l附加淬炼保护成功,§d成功对装备附加:§3六星淬炼保护");
            } else if (clbhing.get(p.getName()) == 9) {
                if (!baoshi.TakePlayerItem((Player)p, (ItemStack)Clbh.clbh_9())) {
                    p.sendMessage("§c所需物品不足。");
                    e.setCancelled(true);
                    p.closeInventory();
                    p.sendMessage("§f[§d淬炼保护符§f]§c你取消了本次淬炼保护!");
                    clbhing.remove(p.getName());
                    return;
                }
                this.clbh(p, "§5九星淬炼保护", item);
                p.sendMessage("§a§l附加淬炼保护成功,§d成功对装备附加:§5九星淬炼保护");
            } else if (clbhing.get(p.getName()) == 12) {
                if (!baoshi.TakePlayerItem((Player)p, (ItemStack)Clbh.clbh_12())) {
                    p.sendMessage("§c所需物品不足。");
                    e.setCancelled(true);
                    p.closeInventory();
                    p.sendMessage("§f[§d淬炼保护符§f]§c你取消了本次淬炼保护!");
                    clbhing.remove(p.getName());
                    return;
                }
                this.clbh(p, "§6十二星淬炼保护", item);
                p.sendMessage("§a§l附加淬炼保护成功,§d成功对装备附加:§6十二星淬炼保护");
            }
            e.setCancelled(true);
            e.setCurrentItem(item);
            clbhing.remove(p.getName());
            p.closeInventory();
            return;
        }
        if (item == null || item.getType().equals((Object)Material.AIR) || !item.getItemMeta().hasDisplayName()) {
            return;
        }
        ItemMeta meta = item.getItemMeta();
        if (meta.getDisplayName().equals(Clbh.clbh_3().getItemMeta().getDisplayName())) {
            e.setCancelled(true);
            p.closeInventory();
            p.sendMessage("§f[§d淬炼保护符§f]§a你正在使用§a§l三星淬炼保护符");
            p.sendMessage("§a按E打开背包，鼠标右键点击一件可淬炼的装备来对它附加淬炼保护");
            p.sendMessage("§a或者右键点击背包空位来取消本次精炼");
            clbhing.put(p.getName(), 3);
        } else if (meta.getDisplayName().equals(Clbh.clbh_6().getItemMeta().getDisplayName())) {
            e.setCancelled(true);
            p.closeInventory();
            p.sendMessage("§f[§d淬炼保护符§f]§a你正在使用§3§l六星淬炼保护符");
            p.sendMessage("§a按E打开背包，鼠标右键点击一件可淬炼的装备来对它附加淬炼保护");
            p.sendMessage("§a或者右键点击背包空位来取消本次精炼");
            clbhing.put(p.getName(), 6);
        } else if (meta.getDisplayName().equals(Clbh.clbh_9().getItemMeta().getDisplayName())) {
            e.setCancelled(true);
            p.closeInventory();
            p.sendMessage("§f[§d淬炼保护符§f]§a你正在使用§5§l九星淬炼保护符");
            p.sendMessage("§a按E打开背包，鼠标右键点击一件可淬炼的装备来对它附加淬炼保护");
            p.sendMessage("§a或者右键点击背包空位来取消本次精炼");
            clbhing.put(p.getName(), 9);
        } else if (meta.getDisplayName().equals(Clbh.clbh_12().getItemMeta().getDisplayName())) {
            e.setCancelled(true);
            p.closeInventory();
            p.sendMessage("§f[§d淬炼保护符§f]§a你正在使用§6§l十二星淬炼保护符");
            p.sendMessage("§a按E打开背包，鼠标右键点击一件可淬炼的装备来对它附加淬炼保护");
            p.sendMessage("§a或者右键点击背包空位来取消本次精炼");
            clbhing.put(p.getName(), 12);
        }
    }

    public void clbh(Player p, String bhstr, ItemStack item) {
        ItemMeta meta = item.getItemMeta();
        ArrayList<String> lore = new ArrayList<String>();
        if (meta.hasLore()) {
            lore.addAll(meta.getLore());
        }
        int i = 0;
        while (i < lore.size()) {
            if (((String)lore.get(i)).indexOf("星淬炼保护") > -1) {
                lore.remove(i);
                --i;
            }
            ++i;
        }
        lore.add(0, bhstr);
        meta.setLore(lore);
        item.setItemMeta(meta);
    }

    public static ItemStack clbh_3() {
        ItemStack i = new ItemStack(Material.PAPER);
        ItemMeta id = i.getItemMeta();
        id.setDisplayName("§3§2§1§a§l三星淬炼保护符");
        ArrayList<String> lore = new ArrayList<String>();
        lore.add("§e用法：§a打开背包，鼠标右键物品");
        lore.add("§e作用：§a对装备附加淬炼保护效果");
        lore.add("§e说明：");
        lore.add("§7  -§a保护装备淬炼失败时最低保底三星");
        lore.add("§7  -§c装备低于三星,却有三星保护,则保护不生效.");
        lore.add("§7  -§a三星保护要求装备淬炼星级大于等于三星");
        id.setLore(lore);
        id.addEnchant(Enchantment.OXYGEN, 1, true);
        i.setItemMeta(id);
        return i;
    }

    public static ItemStack clbh_6() {
        ItemStack i = new ItemStack(Material.PAPER);
        ItemMeta id = i.getItemMeta();
        id.setDisplayName("§3§2§1§3§l六星淬炼保护符");
        ArrayList<String> lore = new ArrayList<String>();
        lore.add("§e用法：§a打开背包，鼠标右键物品");
        lore.add("§e作用：§a对装备附加淬炼保护效果");
        lore.add("§e说明：");
        lore.add("§7  -§a保护装备淬炼失败时最低保底六星");
        lore.add("§7  -§c装备低于六星,却有六星保护,则保护不生效.");
        lore.add("§7  -§a六星保护要求装备淬炼星级大于等于六星");
        id.setLore(lore);
        id.addEnchant(Enchantment.OXYGEN, 1, true);
        i.setItemMeta(id);
        return i;
    }

    public static ItemStack clbh_9() {
        ItemStack i = new ItemStack(Material.PAPER);
        ItemMeta id = i.getItemMeta();
        id.setDisplayName("§3§2§1§5§l九星淬炼保护符");
        ArrayList<String> lore = new ArrayList<String>();
        lore.add("§e用法：§a打开背包，鼠标右键物品");
        lore.add("§e作用：§a对装备附加淬炼保护效果");
        lore.add("§e说明：");
        lore.add("§7  -§a保护装备淬炼失败时最低保底九星");
        lore.add("§7  -§c装备低于九星,却有九星保护,则保护不生效.");
        lore.add("§7  -§a九星保护要求装备淬炼星级大于等于九星");
        id.setLore(lore);
        id.addEnchant(Enchantment.OXYGEN, 1, true);
        i.setItemMeta(id);
        return i;
    }

    public static ItemStack clbh_12() {
        ItemStack i = new ItemStack(Material.PAPER);
        ItemMeta id = i.getItemMeta();
        id.setDisplayName("§3§2§1§6§l十二星淬炼保护符");
        ArrayList<String> lore = new ArrayList<String>();
        lore.add("§e用法：§a打开背包，鼠标右键物品");
        lore.add("§e作用：§a对装备附加淬炼保护效果");
        lore.add("§e说明：");
        lore.add("§7  -§a保护装备淬炼失败时最低保底十二星");
        lore.add("§7  -§c装备低于十二星,却有十二星保护,则保护不生效.");
        lore.add("§7  -§a十二星保护要求装备淬炼星级大于等于十二星");
        id.setLore(lore);
        id.addEnchant(Enchantment.OXYGEN, 1, true);
        i.setItemMeta(id);
        return i;
    }
}

