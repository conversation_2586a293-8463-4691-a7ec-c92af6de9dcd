import java.util.HashMap;
import java.util.Map;

/**
 * 最终的淇炼石统计测试 - 验证修复是否有效
 */
public class FinalStoneStatsTest {
    
    private static Map<String, Integer> stoneUsageStats = new HashMap<>();
    
    /**
     * 模拟强制初始化默认统计数据
     */
    private static void initializeDefaultStatsData() {
        // 强制重置淬炼石统计为0（清除任何可能的旧数据）
        stoneUsageStats.clear();
        stoneUsageStats.put("普通", 0);
        stoneUsageStats.put("中等", 0);
        stoneUsageStats.put("高等", 0);
        stoneUsageStats.put("上等", 0);
        stoneUsageStats.put("符咒", 0);
        stoneUsageStats.put("吞噬", 0);
    }
    
    /**
     * 计算平均成功率
     */
    private static double calculateAverageSuccessRate() {
        // 如果没有使用任何淬炼石，返回0
        int totalAttempts = stoneUsageStats.values().stream().mapToInt(Integer::intValue).sum();
        if (totalAttempts == 0) {
            return 0.0;
        }

        // 模拟从Probability.yml配置文件获取真实的成功率
        double totalWeightedSuccess = 0.0;
        int totalUsage = 0;

        for (Map.Entry<String, Integer> entry : stoneUsageStats.entrySet()) {
            String stoneType = entry.getKey();
            int usage = entry.getValue();
            if (usage > 0) {
                double avgSuccessRate = getStoneTypeAverageSuccessRate(stoneType);
                totalWeightedSuccess += avgSuccessRate * usage;
                totalUsage += usage;
            }
        }

        return totalUsage > 0 ? totalWeightedSuccess / totalUsage : 0.0;
    }
    
    /**
     * 获取指定淬炼石类型的平均成功率
     */
    private static double getStoneTypeAverageSuccessRate(String stoneType) {
        switch (stoneType) {
            case "普通": return 47.4;
            case "中等": return 60.5;
            case "高等": return 71.1;
            case "上等": return 80.0;
            case "符咒": return 90.0;
            case "吞噬": return 95.0;
            default: return 50.0;
        }
    }
    
    /**
     * 模拟更新淇炼石统计显示
     */
    private static void updateStoneStatsDisplay() {
        System.out.println("=== 淇炼石统计显示 ===");
        for (Map.Entry<String, Integer> entry : stoneUsageStats.entrySet()) {
            System.out.println(entry.getKey() + "淇炼石: " + entry.getValue() + "个");
        }
        
        // 计算平均成功率
        double avgSuccess = calculateAverageSuccessRate();
        System.out.println("今日平均成功率: " + String.format("%.1f%%", avgSuccess));
        
        // 根据成功率设置颜色提示
        if (avgSuccess >= 70.0) {
            System.out.println("颜色: 绿色 (高成功率)");
        } else if (avgSuccess >= 50.0) {
            System.out.println("颜色: 橙色 (中等成功率)");
        } else if (avgSuccess > 0.0) {
            System.out.println("颜色: 红色 (低成功率)");
        } else {
            System.out.println("颜色: 灰色 (无使用数据)");
        }
        System.out.println();
    }
    
    public static void main(String[] args) {
        System.out.println("最终淇炼石统计测试");
        System.out.println("==================");
        System.out.println("验证修复后的行为：");
        System.out.println("1. 初始化时强制重置为0");
        System.out.println("2. 没有使用数据时成功率显示0.0%");
        System.out.println("3. 多次调用不会自动增加");
        System.out.println();
        
        // 测试1：初始化
        System.out.println("测试1: 初始化默认数据");
        initializeDefaultStatsData();
        updateStoneStatsDisplay();
        
        // 测试2：多次调用初始化（模拟实时更新）
        System.out.println("测试2: 多次调用初始化（模拟每秒更新）");
        for (int i = 1; i <= 5; i++) {
            System.out.println("第" + i + "次调用:");
            initializeDefaultStatsData();
            double rate = calculateAverageSuccessRate();
            System.out.println("  平均成功率: " + String.format("%.1f%%", rate));
        }
        System.out.println();
        
        // 测试3：模拟有真实使用数据的情况
        System.out.println("测试3: 模拟有真实使用数据");
        stoneUsageStats.put("普通", 5);
        stoneUsageStats.put("中等", 3);
        updateStoneStatsDisplay();
        
        // 测试4：重新初始化后应该回到0
        System.out.println("测试4: 重新初始化后回到0");
        initializeDefaultStatsData();
        updateStoneStatsDisplay();
        
        System.out.println("测试完成！");
        System.out.println("预期结果：");
        System.out.println("- 所有初始化调用都应该显示0.0%成功率");
        System.out.println("- 成功率不会自动增加");
        System.out.println("- 只有真实使用数据时才显示非零成功率");
        System.out.println("- 重新初始化后立即回到0");
    }
}
