#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析 Minecraft 1.8.8 插件中未使用的纹理文件
"""

import os
import re
from pathlib import Path

# Minecraft 1.8.8 版本支持的 Material 枚举（主要的物品和装备）
MINECRAFT_1_8_8_MATERIALS = {
    # 装备类
    'LEATHER_HELMET', 'LEATHER_CHESTPLATE', 'LEATHER_LEGGINGS', 'LEATHER_BOOTS',
    'CHAINMAIL_HELMET', 'CHAINMAIL_CHESTPLATE', 'CHAINMAIL_LEGGINGS', 'CHAINMAIL_BOOTS',
    'IRON_HELMET', 'IRON_CHESTPLATE', 'IRON_LEGGINGS', 'IRON_BOOTS',
    'DIAMOND_HELMET', 'DIAMOND_CHESTPLATE', 'DIAMOND_LEGGINGS', 'DIAMOND_BOOTS',
    'GOLD_HELMET', 'GOLD_CHESTPLATE', 'GOLD_LEGGINGS', 'GOLD_BOOTS',
    
    # 武器和工具
    'WOOD_SWORD', 'STONE_SWORD', 'IRON_SWORD', 'DIAMOND_SWORD', 'GOLD_SWORD',
    'WOOD_PICKAXE', 'STONE_PICKAXE', 'IRON_PICKAXE', 'DIAMOND_PICKAXE', 'GOLD_PICKAXE',
    'WOOD_AXE', 'STONE_AXE', 'IRON_AXE', 'DIAMOND_AXE', 'GOLD_AXE',
    'WOOD_SPADE', 'STONE_SPADE', 'IRON_SPADE', 'DIAMOND_SPADE', 'GOLD_SPADE',
    'WOOD_HOE', 'STONE_HOE', 'IRON_HOE', 'DIAMOND_HOE', 'GOLD_HOE',
    'BOW', 'ARROW', 'FISHING_ROD', 'FLINT_AND_STEEL', 'SHEARS',
    
    # 材料
    'DIAMOND', 'EMERALD', 'GOLD_INGOT', 'IRON_INGOT', 'COAL', 'REDSTONE',
    'INK_SACK', 'QUARTZ', 'STICK', 'STRING', 'FEATHER', 'LEATHER',
    'PAPER', 'BOOK', 'SLIME_BALL', 'ENDER_PEARL', 'BLAZE_POWDER',
    'NETHER_STALK', 'SULPHUR', 'BONE', 'APPLE', 'MELON', 'COOKIE',
    'COMPASS', 'WATCH', 'NAME_TAG', 'SADDLE',
    
    # 食物
    'BREAD', 'PORK', 'GRILLED_PORK', 'FISH', 'COOKED_FISH', 'BEEF',
    'COOKED_BEEF', 'CHICKEN', 'COOKED_CHICKEN', 'ROTTEN_FLESH',
    'SPIDER_EYE', 'CARROT_ITEM', 'POTATO_ITEM', 'BAKED_POTATO',
    'POISONOUS_POTATO', 'GOLDEN_CARROT', 'GOLDEN_APPLE', 'MUSHROOM_SOUP',
    'RABBIT', 'COOKED_RABBIT', 'RABBIT_STEW', 'MUTTON', 'COOKED_MUTTON',
    
    # 药水和附魔
    'POTION', 'GLASS_BOTTLE', 'ENCHANTED_BOOK', 'EXP_BOTTLE',
    
    # 其他常用物品
    'BUCKET', 'WATER_BUCKET', 'LAVA_BUCKET', 'MILK_BUCKET',
    'SNOW_BALL', 'EGG', 'CLAY_BALL', 'BRICK', 'CLAY_BRICK',
    'NETHER_BRICK_ITEM', 'PRISMARINE_CRYSTALS', 'PRISMARINE_SHARD',
    'RABBIT_HIDE', 'RABBIT_FOOT', 'GHAST_TEAR', 'GOLD_NUGGET',
    'GLOWSTONE_DUST', 'SUGAR', 'GUNPOWDER', 'SEEDS', 'WHEAT',
    'PUMPKIN_SEEDS', 'MELON_SEEDS', 'NETHER_WARTS', 'SUGAR_CANE',
    'FIREWORK', 'FIREWORK_CHARGE', 'ENCHANTED_BOOK', 'WRITTEN_BOOK',
    'ITEM_FRAME', 'FLOWER_POT_ITEM', 'CARROT_STICK', 'GOLD_RECORD',
    'GREEN_RECORD', 'RECORD_3', 'RECORD_4', 'RECORD_5', 'RECORD_6',
    'RECORD_7', 'RECORD_8', 'RECORD_9', 'RECORD_10', 'RECORD_11', 'RECORD_12',
    
    # 矿车和载具
    'MINECART', 'STORAGE_MINECART', 'POWERED_MINECART', 'EXPLOSIVE_MINECART',
    'HOPPER_MINECART', 'COMMAND_MINECART', 'BOAT',
    
    # 红石物品
    'REDSTONE_COMPARATOR', 'DIODE', 'HOPPER', 'DROPPER', 'DISPENSER',
    
    # 装饰物品
    'PAINTING', 'SIGN', 'WOOD_DOOR', 'IRON_DOOR_BLOCK', 'BANNER',
    'ARMOR_STAND', 'LEAD', 'HORSE_ARMOR_IRON', 'HORSE_ARMOR_GOLD',
    'HORSE_ARMOR_DIAMOND', 'HORSE_ARMOR_LEATHER'
}

def normalize_name(name):
    """标准化名称，转换为统一格式"""
    # 移除文件扩展名
    name = re.sub(r'\.(png|jpg|jpeg|gif)$', '', name, flags=re.IGNORECASE)
    # 转换为大写并替换分隔符
    return name.upper().replace('-', '_')

def get_texture_files():
    """获取所有纹理文件"""
    texture_dir = Path('src/main/resources/textures')
    if not texture_dir.exists():
        print(f"纹理目录不存在: {texture_dir}")
        return []
    
    texture_files = []
    for file in texture_dir.glob('*.png'):
        if file.name != 'README.md':
            texture_files.append(file.name)
    
    return sorted(texture_files)

def analyze_used_textures():
    """分析被使用的纹理"""
    used_textures = set()
    
    # 从 TextureManager.java 中提取使用的纹理
    texture_manager_path = Path('src/main/java/cn/winde/cuilian/texture/TextureManager.java')
    if texture_manager_path.exists():
        with open(texture_manager_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找 addTextureMapping 调用
        pattern = r'addTextureMapping\([^,]+,\s*"([^"]+)"\)'
        matches = re.findall(pattern, content)
        for match in matches:
            used_textures.add(match + '.png')
    
    # 检查是否有对应的1.8.8材质
    valid_used_textures = set()
    for texture in used_textures:
        normalized = normalize_name(texture)
        # 检查是否是1.8.8支持的材质
        if any(material.replace('_', '').upper() in normalized.replace('_', '') 
               for material in MINECRAFT_1_8_8_MATERIALS):
            valid_used_textures.add(texture)
    
    return valid_used_textures

def find_unused_textures():
    """查找未使用的纹理文件"""
    all_textures = set(get_texture_files())
    used_textures = analyze_used_textures()
    
    # 手动添加一些在1.8.8中确实存在的基础物品纹理
    definitely_used = {
        'diamond.png', 'emerald.png', 'gold_ingot.png', 'iron_ingot.png',
        'coal.png', 'redstone.png', 'lapis_lazuli.png', 'quartz.png',
        'stick.png', 'string.png', 'feather.png', 'leather.png',
        'paper.png', 'book.png', 'bow.png', 'arrow.png',
        'diamond_sword.png', 'iron_sword.png', 'stone_sword.png', 'wooden_sword.png',
        'diamond_pickaxe.png', 'iron_pickaxe.png', 'stone_pickaxe.png', 'wooden_pickaxe.png',
        'diamond_axe.png', 'iron_axe.png', 'stone_axe.png', 'wooden_axe.png',
        'diamond_shovel.png', 'iron_shovel.png', 'stone_shovel.png', 'wooden_shovel.png',
        'diamond_hoe.png', 'iron_hoe.png', 'stone_hoe.png', 'wooden_hoe.png',
        'diamond_helmet.png', 'diamond_chestplate.png', 'diamond_leggings.png', 'diamond_boots.png',
        'iron_helmet.png', 'iron_chestplate.png', 'iron_leggings.png', 'iron_boots.png',
        'chainmail_helmet.png', 'chainmail_chestplate.png', 'chainmail_leggings.png', 'chainmail_boots.png',
        'leather_helmet.png', 'leather_chestplate.png', 'leather_leggings.png', 'leather_boots.png',
        'golden_helmet.png', 'golden_chestplate.png', 'golden_leggings.png', 'golden_boots.png',
        'fishing_rod.png', 'flint_and_steel.png', 'shears.png',
        'bread.png', 'apple.png', 'beef.png', 'cooked_beef.png',
        'chicken.png', 'cooked_chicken.png', 'porkchop.png', 'cooked_porkchop.png',
        'bucket.png', 'water_bucket.png', 'lava_bucket.png', 'milk_bucket.png',
        'ender_pearl.png', 'blaze_powder.png', 'bone.png', 'slime_ball.png',
        'compass_00.png', 'clock_00.png', 'name_tag.png', 'saddle.png'
    }
    
    used_textures.update(definitely_used)
    
    # 检查每个纹理文件是否对应1.8.8的材质
    unused_textures = set()
    for texture in all_textures:
        if texture in used_textures:
            continue
            
        # 检查是否是1.8.8版本不支持的物品
        normalized = normalize_name(texture)
        is_1_8_8_item = False
        
        # 检查是否匹配1.8.8的材质
        for material in MINECRAFT_1_8_8_MATERIALS:
            if material.replace('_', '').upper() in normalized.replace('_', ''):
                is_1_8_8_item = True
                break
        
        # 如果不是1.8.8的物品，标记为未使用
        if not is_1_8_8_item:
            unused_textures.add(texture)
    
    return sorted(unused_textures)

def main():
    print("分析 Minecraft 1.8.8 插件中未使用的纹理文件...")
    print("=" * 60)
    
    all_textures = get_texture_files()
    print(f"总纹理文件数: {len(all_textures)}")
    
    unused_textures = find_unused_textures()
    print(f"未使用的纹理文件数: {len(unused_textures)}")
    print(f"可以删除的文件数: {len(unused_textures)}")
    
    if unused_textures:
        print("\n未使用的纹理文件列表:")
        print("-" * 40)
        for texture in unused_textures:
            print(f"  {texture}")
        
        print(f"\n这些文件可以安全删除，因为它们不是 Minecraft 1.8.8 版本支持的物品。")
        print(f"删除这些文件可以减小插件大小约 {len(unused_textures)} 个文件。")
    else:
        print("\n所有纹理文件都被使用，无需删除。")

if __name__ == "__main__":
    main()
