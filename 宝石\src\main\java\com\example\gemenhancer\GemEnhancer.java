package com.example.gemenhancer;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.java.JavaPlugin;

import com.example.gemenhancer.commands.GemCommand;
import com.example.gemenhancer.listeners.FurnaceListener;
import com.example.gemenhancer.utils.GemManager;

public class GemEnhancer extends JavaPlugin {

    private GemManager gemManager;

    @Override
    public void onEnable() {
        // 保存默认配置
        saveDefaultConfig();

        // 初始化宝石管理器
        gemManager = new GemManager(this);

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(new FurnaceListener(this), this);

        // 注册命令和Tab补全器
        GemCommand gemCommand = new GemCommand(this);
        getCommand("gemenhancer").setExecutor(gemCommand);
        getCommand("gemenhancer").setTabCompleter(new com.example.gemenhancer.commands.GemTabCompleter());

        // 注册宝石为可燃烧物品
        registerGemAsFuel();

        getLogger().info("GemEnhancer 插件已启用!");
    }

    /**
     * 注册宝石为可燃烧物品
     */
    private void registerGemAsFuel() {
        try {
            // 获取宝石物品
            ItemStack gemItem = gemManager.getGemItem();

            // 在控制台输出信息
            getLogger().info("注册宝石为可燃烧物品: " + gemItem.getType().name());

            // 注册宝石为可燃烧物品
            // 注意：这里我们不需要实际注册，因为我们使用事件监听器来处理
            // 这个方法主要是为了记录日志
        } catch (Exception e) {
            getLogger().warning("注册宝石为可燃烧物品时出错: " + e.getMessage());
        }
    }

    @Override
    public void onDisable() {
        getLogger().info("GemEnhancer 插件已禁用!");
    }

    /**
     * 重新加载配置文件
     */
    public void reloadPlugin() {
        reloadConfig();
        gemManager.reload();
    }

    /**
     * 获取宝石管理器
     *
     * @return 宝石管理器实例
     */
    public GemManager getGemManager() {
        return gemManager;
    }

    /**
     * 发送带有前缀的消息
     *
     * @param sender  消息接收者
     * @param message 消息内容
     */
    public void sendMessage(CommandSender sender, String message) {
        String prefix = ChatColor.translateAlternateColorCodes('&',
                getConfig().getString("messages.prefix", "&8[&b宝石强化&8] "));
        sender.sendMessage(prefix + ChatColor.translateAlternateColorCodes('&', message));
    }
}
