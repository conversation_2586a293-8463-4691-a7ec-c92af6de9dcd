/*
 * Decompiled with CFR 0.152.
 *
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.Location
 *  org.bukkit.entity.Player
 *  org.bukkit.event.Listener
 *  org.bukkit.scheduler.BukkitRunnable
 */
package cn.winde.cuilian.lizi;

import cn.winde.cuilian.Cuilian;
import cn.winde.cuilian.suit.SuitManager;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;
import java.util.List;
import java.util.Map;

public class effectlisten
        extends BukkitRunnable
        implements Listener {
    public void run() {
        // 检查全局特效开关
        if (!Cuilian.config.getBoolean("effects", true)) {
            // 如果全局特效被禁用，清空所有玩家的特效并跳过
            return;
        }

        for (Player player : Bukkit.getOnlinePlayers()) {
            String playerName = player.getName();
            if (!Cuilian.lizi.containsKey(playerName))
                continue;

            int level = Cuilian.lizi.get(playerName);

            // 检查是否是预览模式（level = -2）
            if (level == -2) {
                // 预览模式由 SuitPreviewManager 单独处理，这里跳过
                continue;
            }
            // 检查是否正在预览中，如果是则跳过正常特效处理
            else if (cn.winde.cuilian.preview.SuitPreviewManager.isPlayerPreviewing(playerName)) {
                // 玩家正在预览中，跳过正常特效处理以避免冲突
                continue;
            }
            // 检查是否是套装特效（level = -1）
            else if (level == -1) {
                // 处理命名套装特效
                SuitManager.SuitAttribute suitAttribute = SuitManager.getPlayerSuitAttribute(player.getName());
                if (suitAttribute != null) {
                    effectlisten.effectSuit(player, suitAttribute);
                }
            } else {
                // 处理淬炼等级特效 - 动态支持所有配置的星级
                if (level >= 6) {
                    // 检查配置文件中是否存在该星级的特效配置
                    if (Cuilian.config.isConfigurationSection("eff.leve" + level) ||
                            Cuilian.config.isList("eff.leve" + level + ".effects")) {
                        // 如果配置文件中有该星级的特效配置，直接使用
                        effectlisten.effect(player, level);
                    } else {
                        // 如果没有配置，使用传统的switch逻辑
                        switch (level) {
                            case 6:
                            case 9:
                            case 12:
                            case 15:
                            case 18:
                            case 19:
                                effectlisten.effect(player, level);
                                break;
                            default:
                                // 对于其他高星级，尝试使用最接近的已配置星级
                                if (level >= 19) {
                                    effectlisten.effect(player, 19);
                                } else if (level >= 18) {
                                    effectlisten.effect(player, 18);
                                } else if (level >= 15) {
                                    effectlisten.effect(player, 15);
                                } else if (level >= 12) {
                                    effectlisten.effect(player, 12);
                                } else if (level >= 9) {
                                    effectlisten.effect(player, 9);
                                } else {
                                    effectlisten.effect(player, 6);
                                }
                                break;
                        }
                    }
                }
            }
        }
    }

    public static void effect(Player p, int leve) {
        // 检查是否启用特效叠加
        boolean enableStacking = Cuilian.config.getBoolean("effect_settings.enable_stacking", true);

        if (enableStacking) {
            // 新的多特效系统
            spawnMultipleEffects(p, leve);
        } else {
            // 兼容旧的单特效系统
            // 获取颜色配置
            String co1 = Cuilian.config.getString("eff.leve" + leve + ".colore1");
            String co2 = Cuilian.config.getString("eff.leve" + leve + ".colore2");
            String co3 = Cuilian.config.getString("eff.leve" + leve + ".colore3");

            // 获取颜色对象
            ParticleEffect.OrdinaryColor color1 = getColor(co1, new ParticleEffect.OrdinaryColor(255, 0, 0));
            ParticleEffect.OrdinaryColor color2 = getColor(co2, new ParticleEffect.OrdinaryColor(0, 0, 255));
            ParticleEffect.OrdinaryColor color3 = getColor(co3, new ParticleEffect.OrdinaryColor(0, 255, 0));

            spawnSingleEffect(p, leve, color1, color2, color3);
        }
    }

    /**
     * 生成多个特效（叠加模式）
     */
    private static void spawnMultipleEffects(Player p, int leve) {
        // 获取特效列表
        List<Map<?, ?>> effects = Cuilian.config.getMapList("eff.leve" + leve + ".effects");

        if (effects.isEmpty()) {
            // 如果没有配置特效列表，回退到单特效模式
            // 获取默认颜色配置
            String co1 = Cuilian.config.getString("eff.leve" + leve + ".colore1");
            String co2 = Cuilian.config.getString("eff.leve" + leve + ".colore2");
            String co3 = Cuilian.config.getString("eff.leve" + leve + ".colore3");

            ParticleEffect.OrdinaryColor color1 = getColor(co1, new ParticleEffect.OrdinaryColor(255, 0, 0));
            ParticleEffect.OrdinaryColor color2 = getColor(co2, new ParticleEffect.OrdinaryColor(0, 0, 255));
            ParticleEffect.OrdinaryColor color3 = getColor(co3, new ParticleEffect.OrdinaryColor(0, 255, 0));

            spawnSingleEffect(p, leve, color1, color2, color3);
            return;
        }

        // 显示所有启用的特效，每个特效使用自己的颜色
        for (Map<?, ?> effect : effects) {
            Boolean enabled = (Boolean) effect.get("enabled");
            // 默认启用，只有明确设置为false才禁用
            if (enabled == null || enabled) {
                String effectType = (String) effect.get("type");
                if (effectType != null) {
                    // 获取每个特效的颜色配置
                    String co1 = (String) effect.get("colore1");
                    String co2 = (String) effect.get("colore2");
                    String co3 = (String) effect.get("colore3");

                    // 获取颜色对象
                    ParticleEffect.OrdinaryColor color1 = getColor(co1, new ParticleEffect.OrdinaryColor(255, 0, 0));
                    ParticleEffect.OrdinaryColor color2 = getColor(co2, new ParticleEffect.OrdinaryColor(0, 0, 255));
                    ParticleEffect.OrdinaryColor color3 = getColor(co3, new ParticleEffect.OrdinaryColor(0, 255, 0));

                    spawnEffectByType(p, effectType.trim().replace("'", "").replace("\"", ""),
                            color1, color2, color3);
                }
            }
        }
    }

    /**
     * 生成单个特效（兼容模式）
     */
    private static void spawnSingleEffect(Player p, int leve, ParticleEffect.OrdinaryColor color1,
            ParticleEffect.OrdinaryColor color2, ParticleEffect.OrdinaryColor color3) {
        String effectType = Cuilian.config.getString("eff.leve" + leve + ".effect_type", "wings");
        if (effectType != null) {
            effectType = effectType.trim().replace("'", "").replace("\"", "");
            spawnEffectByType(p, effectType, color1, color2, color3);
        }
    }

    /**
     * 根据特效类型生成特效
     */
    private static void spawnEffectByType(Player p, String effectType, ParticleEffect.OrdinaryColor color1,
            ParticleEffect.OrdinaryColor color2, ParticleEffect.OrdinaryColor color3) {
        switch (effectType.toLowerCase()) {
            // 淬炼5.6版本特效
            case "flame_basic":
                effectlisten.SpawnFlameBasic(p, color1, color2, color3);
                break;
            case "footstep":
                effectlisten.SpawnFootstep(p, color1, color2, color3);
                break;
            case "spiral_flame":
                effectlisten.SpawnSpiralFlame(p, color1, color2, color3);
                break;
            case "rotating_halo":
                effectlisten.SpawnRotatingHalo(p, color1, color2, color3);
                break;
            case "four_direction_spell":
                effectlisten.SpawnFourDirectionSpell(p, color1, color2, color3);
                break;
            case "wings_56":
                effectlisten.SpawnWings56(p, color1, color2, color3);
                break;
            // 现代版本特效
            case "halo":
                effectlisten.SpawnHalo(p, color1, color2, color3);
                break;
            case "flame":
                effectlisten.SpawnFlame(p, color1, color2, color3);
                break;
            case "nebula":
                effectlisten.SpawnNebula(p, color1, color2, color3);
                break;
            case "tornado":
                effectlisten.SpawnTornado(p, color1, color2, color3);
                break;
            case "stars":
                effectlisten.SpawnStars(p, color1, color2, color3);
                break;
            case "spiral":
                effectlisten.SpawnSpiral(p, color1, color2, color3);
                break;
            case "sphere":
                effectlisten.SpawnSphere(p, color1, color2, color3);
                break;
            case "orbit":
                effectlisten.SpawnOrbit(p, color1, color2, color3);
                break;
            case "cube":
                effectlisten.SpawnCube(p, color1, color2, color3);
                break;
            case "helix":
                effectlisten.SpawnHelix(p, color1, color2, color3);
                break;
            case "batman":
                effectlisten.SpawnBatman(p, color1, color2, color3);
                break;
            case "popper":
                effectlisten.SpawnPopper(p, color1, color2, color3);
                break;
            case "pulse":
                effectlisten.SpawnPulse(p, color1, color2, color3);
                break;
            case "whirl":
                effectlisten.SpawnWhirl(p, color1, color2, color3);
                break;
            case "whirlwind":
                effectlisten.SpawnWhirlwind(p, color1, color2, color3);
                break;
            case "invocation":
                effectlisten.SpawnInvocation(p, color1, color2, color3);
                break;
            case "lightning":
                effectlisten.SpawnLightning(p, color1, color2, color3);
                break;
            case "frost":
                effectlisten.SpawnFrost(p, color1, color2, color3);
                break;
            case "shadow":
                effectlisten.SpawnShadow(p, color1, color2, color3);
                break;
            case "rainbow":
                effectlisten.SpawnRainbow(p, color1, color2, color3);
                break;
            case "spacerift":
                effectlisten.SpawnSpaceRift(p, color1, color2, color3);
                break;
            case "wings":
            default:
                effectlisten.SpawnWings(p, color1, color2, color3);
                break;
        }
    }

    /**
     * 获取颜色对象
     */
    private static ParticleEffect.OrdinaryColor getColor(String colorName, ParticleEffect.OrdinaryColor defaultColor) {
        if (colorName == null)
            return defaultColor;

        ParticleEffect.OrdinaryColor color = Cuilian.color.get(colorName);
        if (color != null)
            return color;

        // 如果颜色映射中没有，尝试直接解析
        switch (colorName) {
            case "红":
                return new ParticleEffect.OrdinaryColor(255, 0, 0);
            case "橙":
                return new ParticleEffect.OrdinaryColor(255, 128, 0);
            case "黄":
                return new ParticleEffect.OrdinaryColor(255, 255, 0);
            case "绿":
                return new ParticleEffect.OrdinaryColor(0, 255, 0);
            case "蓝":
                return new ParticleEffect.OrdinaryColor(0, 0, 255);
            case "粉":
                return new ParticleEffect.OrdinaryColor(255, 0, 255);
            case "黑":
                return new ParticleEffect.OrdinaryColor(0, 0, 0);
            case "灰":
                return new ParticleEffect.OrdinaryColor(128, 128, 128);
            case "白":
                return new ParticleEffect.OrdinaryColor(255, 255, 255);
            case "银":
                return new ParticleEffect.OrdinaryColor(192, 192, 192);
            case "紫":
                return new ParticleEffect.OrdinaryColor(128, 0, 128);
            default:
                return defaultColor;
        }
    }

    // ==================== 淬炼5.6版本特效 - 完全按照原版实现 ====================

    // 5.6版本的全局参数 (从原版源代码提取)
    private static float step56 = 0.0f;
    private static final double Angle56 = 0.5235987755982988; // 5.6版本的角度常量
    private static float i56 = 0.0f;
    private static final double angularVelocityX56 = 0.01570796326794897;
    private static final double angularVelocityY56 = 0.01847995678582231;
    private static final double angularVelocityZ56 = 0.0202683397005793;
    private static final float radius56 = 1.5f;
    private static final int particles56 = 20;

    /**
     * 更新5.6版本的全局步进参数 (模拟原版的step++和i递增)
     */
    private static void update56Params() {
        step56 += 1.0f;
        i56 = (float) ((double) i56 + 0.1);
        if (i56 > 6.0f) {
            i56 = 0.0f;
        }
    }

    /**
     * 基础火焰特效 (6星) - 完全按照淬炼5.6版本实现，支持颜色配置
     * 原版代码: double x6 = Math.cos(0.5235987755982988 * (double)this.step) *
     * (double)0.4f;
     */
    public static void SpawnFlameBasic(Player p, ParticleEffect.OrdinaryColor c1, ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        update56Params(); // 更新全局参数

        // 完全按照5.6版本的计算公式
        double x6 = Math.cos(Angle56 * (double) step56) * (double) 0.4f;
        double y6 = 2.0;
        double z6 = Math.sin(Angle56 * (double) step56) * (double) 0.4f;

        Location loc6 = p.getLocation();
        loc6.add(x6, y6, z6);

        // 使用配置的颜色显示红石粒子，保持5.6版本的位置计算
        ParticleEffect.REDSTONE.display(c1, loc6, 10.0);

        // 添加一些额外的颜色层次
        Location loc6_2 = loc6.clone().add(0, 0.1, 0);
        ParticleEffect.REDSTONE.display(c2, loc6_2, 10.0);

        Location loc6_3 = loc6.clone().add(0, 0.2, 0);
        ParticleEffect.REDSTONE.display(c3, loc6_3, 10.0);
    }

    /**
     * 脚步特效 (6星) - 完全按照淬炼5.6版本实现，支持颜色配置
     * 原版代码: loc6_2.setY(loc6_2.getY() + 0.05);
     */
    public static void SpawnFootstep(Player p, ParticleEffect.OrdinaryColor c1, ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        Location loc6_2 = p.getLocation();
        loc6_2.setY(loc6_2.getY() + 0.05);

        // 使用配置的颜色显示红石粒子，保持5.6版本的位置
        ParticleEffect.REDSTONE.display(c1, loc6_2, 10.0);

        // 在脚步周围添加一些颜色变化
        for (int i = 0; i < 3; i++) {
            double angle = 2 * Math.PI * i / 3;
            double x = 0.2 * Math.cos(angle);
            double z = 0.2 * Math.sin(angle);
            Location footLoc = loc6_2.clone().add(x, 0, z);

            if (i == 0) {
                ParticleEffect.REDSTONE.display(c1, footLoc, 10.0);
            } else if (i == 1) {
                ParticleEffect.REDSTONE.display(c2, footLoc, 10.0);
            } else {
                ParticleEffect.REDSTONE.display(c3, footLoc, 10.0);
            }
        }
    }

    /**
     * 螺旋火焰特效 (9星) - 完全按照淬炼5.6版本实现，支持颜色配置
     * 原版代码: double x9 = Math.sin(0.3141592653589793 * (double)this.step) * 1.0;
     */
    public static void SpawnSpiralFlame(Player p, ParticleEffect.OrdinaryColor c1, ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        // 完全按照5.6版本的计算公式
        double x9 = Math.sin(0.3141592653589793 * (double) step56) * 1.0;
        double y9 = 0.3 * (double) i56;
        double z9 = Math.cos(0.3141592653589793 * (double) step56) * 1.0;

        Location loc9 = p.getLocation();
        loc9.add(x9, y9, z9);

        // 使用配置的颜色显示红石粒子，保持5.6版本的螺旋计算
        ParticleEffect.REDSTONE.display(c1, loc9, 10.0);

        // 添加螺旋的颜色层次
        Location loc9_2 = loc9.clone().add(0, 0.1, 0);
        ParticleEffect.REDSTONE.display(c2, loc9_2, 10.0);

        Location loc9_3 = loc9.clone().add(0, 0.2, 0);
        ParticleEffect.REDSTONE.display(c3, loc9_3, 10.0);
    }

    /**
     * 旋转光环特效 (12星) - 完全按照淬炼5.6版本实现，支持颜色配置
     * 原版代码完整还原，包含Vector和rotateVector
     */
    public static void SpawnRotatingHalo(Player p, ParticleEffect.OrdinaryColor c1, ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        // 完全按照5.6版本的实现
        Location loc12 = p.getLocation();
        loc12.add(0.0, 1.0, 0.0);
        loc12.subtract(0.0, 0.0, 0.0); // xSubtract, ySubtract, zSubtract都是0

        // 5.6版本的角度计算
        double inc = Math.PI * 2 / (double) particles56;
        double angle = (double) step56 * inc;

        // 创建向量 (完全按照5.6版本)
        Vector v12 = new Vector();
        v12.setX(Math.cos(angle) * (double) radius56);
        v12.setZ(Math.sin(angle) * (double) radius56);

        // 应用静态旋转 (5.6版本的rotateVector)
        rotateVector56(v12, 0.0, 0.0, 5.0); // xRotation=0, yRotation=0, zRotation=5.0

        // 应用动态旋转 (5.6版本的enableRotation)
        rotateVector56(v12, angularVelocityX56 * (double) step56,
                angularVelocityY56 * (double) step56,
                angularVelocityZ56 * (double) step56);

        // 使用配置的颜色显示红石粒子，保持5.6版本的旋转计算
        Location finalLoc = loc12.add(v12);
        ParticleEffect.REDSTONE.display(c1, finalLoc, 10.0);

        // 添加光环的多层颜色效果
        Location haloLoc2 = finalLoc.clone().add(0, 0.1, 0);
        ParticleEffect.REDSTONE.display(c2, haloLoc2, 10.0);

        Location haloLoc3 = finalLoc.clone().add(0, -0.1, 0);
        ParticleEffect.REDSTONE.display(c3, haloLoc3, 10.0);
    }

    /**
     * 5.6版本的向量旋转函数 - 完全按照原版实现
     */
    private static Vector rotateVector56(Vector v, double angleX, double angleY, double angleZ) {
        rotateAroundAxisX56(v, angleX);
        rotateAroundAxisY56(v, angleY);
        rotateAroundAxisZ56(v, angleZ);
        return v;
    }

    private static Vector rotateAroundAxisX56(Vector v, double angle) {
        double cos = Math.cos(angle);
        double sin = Math.sin(angle);
        double y = v.getY() * cos - v.getZ() * sin;
        double z = v.getY() * sin + v.getZ() * cos;
        return v.setY(y).setZ(z);
    }

    private static Vector rotateAroundAxisY56(Vector v, double angle) {
        double cos = Math.cos(angle);
        double sin = Math.sin(angle);
        double x = v.getX() * cos + v.getZ() * sin;
        double z = v.getX() * -sin + v.getZ() * cos;
        return v.setX(x).setZ(z);
    }

    private static Vector rotateAroundAxisZ56(Vector v, double angle) {
        double cos = Math.cos(angle);
        double sin = Math.sin(angle);
        double x = v.getX() * cos - v.getY() * sin;
        double y = v.getX() * sin + v.getY() * cos;
        return v.setX(x).setY(y);
    }

    /**
     * 四方向法术特效 (15星) - 完全按照淬炼5.6版本实现，支持颜色配置
     * 原版代码完整还原
     */
    public static void SpawnFourDirectionSpell(Player p, ParticleEffect.OrdinaryColor c1,
            ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        // 完全按照5.6版本的实现
        Location locc = p.getLocation().clone();
        locc.setPitch(0.0f);
        Location locc1 = locc.clone();
        Location locc2 = locc.clone();
        Location locc3 = locc.clone();

        // 设置四个方向的Yaw角度 (完全按照5.6版本)
        locc.setYaw(45.0f);
        locc.add(locc.getDirection().multiply(1));
        locc1.setYaw(-45.0f);
        locc1.add(locc1.getDirection().multiply(1));
        locc2.setYaw(135.0f);
        locc2.add(locc2.getDirection().multiply(1));
        locc3.setYaw(-135.0f);
        locc3.add(locc3.getDirection().multiply(1));

        // 使用配置的颜色显示红石粒子，保持5.6版本的四方向计算
        ParticleEffect.REDSTONE.display(c1, locc, 10.0);
        ParticleEffect.REDSTONE.display(c2, locc1, 10.0);
        ParticleEffect.REDSTONE.display(c3, locc2, 10.0);
        ParticleEffect.REDSTONE.display(c1, locc3, 10.0); // 第四个方向使用c1颜色

        // 添加一些法术光效
        ParticleEffect.SPELL_INSTANT.display(0.0f, 0.0f, 0.0f, 0.0f, 1, locc, 10.0);
        ParticleEffect.SPELL_INSTANT.display(0.0f, 0.0f, 0.0f, 0.0f, 1, locc1, 10.0);
        ParticleEffect.SPELL_INSTANT.display(0.0f, 0.0f, 0.0f, 0.0f, 1, locc2, 10.0);
        ParticleEffect.SPELL_INSTANT.display(0.0f, 0.0f, 0.0f, 0.0f, 1, locc3, 10.0);
    }

    /**
     * 5.6版本翅膀特效 (18星) - 完全按照淬炼5.6版本实现
     * 原版代码完整还原，包含颜色配置读取
     */
    public static void SpawnWings56(Player p, ParticleEffect.OrdinaryColor c1, ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        // 完全按照5.6版本的实现
        Location loc = p.getLocation().clone();
        loc.setPitch(0.0f);
        loc.add(0.0, 1.8, 0.0);
        loc.add(loc.getDirection().multiply(-0.2));

        // 右翅膀基础位置
        Location loc1R = loc.clone();
        loc1R.setYaw(loc1R.getYaw() + 110.0f);

        // 右翅膀各个点位 (完全按照5.6版本)
        Location loc2R = loc1R.clone().add(loc1R.getDirection().multiply(1));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) c2, loc2R.add(0.0, 0.8, 0.0), 30.0);

        Location loc3R = loc1R.clone().add(loc1R.getDirection().multiply(0.8));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) c2, loc3R.add(0.0, 0.4, 0.0), 30.0);

        Location loc4R = loc1R.clone().add(loc1R.getDirection().multiply(0.6));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) c2, loc4R.add(0.0, 0.4, 0.0), 30.0);

        Location loc5R = loc1R.clone().add(loc1R.getDirection().multiply(0.4));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) c2, loc5R.clone().add(0.0, -0.2, 0.0), 30.0);

        Location loc6R = loc1R.clone().add(loc1R.getDirection().multiply(0.2));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) c2, loc6R.add(0.0, -0.2, 0.0), 30.0);

        // 右翅膀细节层 (完全按照5.6版本的zu循环)
        int zu = 0;
        while (zu <= 3) {
            ParticleEffect.OrdinaryColor color3 = ++zu == 4 ? c2 : c1;
            if (c3 != null && (zu == 4 || zu == 3)) {
                color3 = c3;
            }
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) c2, loc2R.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc3R.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc4R.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc5R.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc6R.add(0.0, -0.2, 0.0), 30.0);
        }

        // 左翅膀基础位置
        Location loc1L = loc.clone();
        loc1L.setYaw(loc1L.getYaw() - 110.0f);

        // 左翅膀各个点位 (完全按照5.6版本)
        Location loc2L = loc1L.clone().add(loc1L.getDirection().multiply(1));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) c2, loc2L.add(0.0, 0.8, 0.0), 30.0);

        Location loc3L = loc1L.clone().add(loc1L.getDirection().multiply(0.8));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) c2, loc3L.add(0.0, 0.6, 0.0), 30.0);

        Location loc4L = loc1L.clone().add(loc1L.getDirection().multiply(0.6));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) c2, loc4L.add(0.0, 0.4, 0.0), 30.0);

        Location loc5L = loc1L.clone().add(loc1L.getDirection().multiply(0.4));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) c2, loc5L.clone().add(0.0, -0.2, 0.0), 30.0);

        Location loc6L = loc1L.clone().add(loc1L.getDirection().multiply(0.2));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) c2, loc6L.add(0.0, -0.2, 0.0), 30.0);

        // 左翅膀细节层 (完全按照5.6版本的zu循环)
        zu = 0;
        while (zu <= 3) {
            ParticleEffect.OrdinaryColor color3 = ++zu == 4 ? c2 : c1;
            if (c3 != null && (zu == 4 || zu == 3)) {
                color3 = c3;
            }
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) c2, loc2L.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc3L.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc4L.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc5L.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc6L.add(0.0, -0.2, 0.0), 30.0);
        }
    }

    public static void SpawnWings(Player p, ParticleEffect.OrdinaryColor c1, ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        Location loc = p.getLocation().clone();
        loc.setPitch(0.0f);
        loc.add(0.0, 1.8, 0.0);
        loc.add(loc.getDirection().multiply(-0.2));
        ParticleEffect.OrdinaryColor color = c1;
        ParticleEffect.OrdinaryColor color2 = c2;
        ParticleEffect.OrdinaryColor color4 = c3;
        Location loc1R = loc.clone();
        loc1R.setYaw(loc1R.getYaw() + 110.0f);
        Location loc2R = loc1R.clone().add(loc1R.getDirection().multiply(1));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc2R.add(0.0, 0.8, 0.0), 30.0);
        Location loc3R = loc1R.clone().add(loc1R.getDirection().multiply(0.8));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc3R.add(0.0, 0.6, 0.0), 30.0);
        Location loc4R = loc1R.clone().add(loc1R.getDirection().multiply(0.6));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc4R.add(0.0, 0.4, 0.0), 30.0);
        Location loc5R = loc1R.clone().add(loc1R.getDirection().multiply(0.4));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc5R.clone().add(0.0, -0.2, 0.0), 30.0);
        Location loc6R = loc1R.clone().add(loc1R.getDirection().multiply(0.2));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc6R.add(0.0, -0.2, 0.0), 30.0);
        int zu = 0;
        while (zu <= 3) {
            ParticleEffect.OrdinaryColor color3 = ++zu == 4 ? color2 : color;
            if (color4 != null && (zu == 4 || zu == 3)) {
                color3 = color4;
            }
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc2R.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc3R.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc4R.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc5R.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc6R.add(0.0, -0.2, 0.0), 30.0);
        }
        Location loc1L = loc.clone();
        loc1L.setYaw(loc1L.getYaw() - 110.0f);
        Location loc2L = loc1L.clone().add(loc1L.getDirection().multiply(1));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc2L.add(0.0, 0.8, 0.0), 30.0);
        Location loc3L = loc1L.clone().add(loc1L.getDirection().multiply(0.8));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc3L.add(0.0, 0.6, 0.0), 30.0);
        Location loc4L = loc1L.clone().add(loc1L.getDirection().multiply(0.6));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc4L.add(0.0, 0.4, 0.0), 30.0);
        Location loc5L = loc1L.clone().add(loc1L.getDirection().multiply(0.4));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc5L.clone().add(0.0, -0.2, 0.0), 30.0);
        Location loc6L = loc1L.clone().add(loc1L.getDirection().multiply(0.2));
        ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc6L.add(0.0, -0.2, 0.0), 30.0);
        zu = 0;
        while (zu <= 3) {
            ParticleEffect.OrdinaryColor color3 = ++zu == 4 ? color2 : color;
            if (color4 != null && (zu == 4 || zu == 3)) {
                color3 = color4;
            }
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color2, loc2L.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc3L.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc4L.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc5L.add(0.0, -0.2, 0.0), 30.0);
            ParticleEffect.REDSTONE.display((ParticleEffect.ParticleColor) color3, loc6L.add(0.0, -0.2, 0.0), 30.0);
        }
    }

    /**
     * 生成光环特效 (9星套装)
     */
    public static void SpawnHalo(Player p, ParticleEffect.OrdinaryColor c1, ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        Location loc = p.getLocation().clone();
        loc.add(0.0, 2.1, 0.0); // 头顶上方

        // 创建水平光环
        double radius = 0.6;
        int particles = 30;
        for (int i = 0; i < particles; i++) {
            double angle = 2 * Math.PI * i / particles;
            double x = radius * Math.cos(angle);
            double z = radius * Math.sin(angle);

            Location particleLoc = loc.clone().add(x, 0, z);

            // 使用三种颜色交替
            if (i % 3 == 0) {
                ParticleEffect.REDSTONE.display(c1, particleLoc, 30.0);
            } else if (i % 3 == 1) {
                ParticleEffect.REDSTONE.display(c2, particleLoc, 30.0);
            } else {
                ParticleEffect.REDSTONE.display(c3, particleLoc, 30.0);
            }
        }

        // 添加一些向上飘的粒子
        for (int i = 0; i < 5; i++) {
            double angle = 2 * Math.PI * i / 5;
            double x = radius * 0.7 * Math.cos(angle);
            double z = radius * 0.7 * Math.sin(angle);

            Location particleLoc = loc.clone().add(x, -0.3, z);
            ParticleEffect.SPELL_MOB.display(c1, particleLoc, 30.0);
        }
    }

    /**
     * 生成火焰特效 (12星套装)
     */
    public static void SpawnFlame(Player p, ParticleEffect.OrdinaryColor c1, ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        Location loc = p.getLocation().clone();

        // 在脚下生成火焰粒子
        for (int i = 0; i < 8; i++) {
            double angle = 2 * Math.PI * i / 8;
            double radius = 0.4;
            double x = radius * Math.cos(angle);
            double z = radius * Math.sin(angle);

            Location flameLoc = loc.clone().add(x, 0.1, z);

            // 火焰上升效果
            for (int j = 0; j < 3; j++) {
                Location particleLoc = flameLoc.clone().add(0, j * 0.3, 0);

                // 使用三种颜色模拟火焰
                if (j == 0) {
                    ParticleEffect.REDSTONE.display(c1, particleLoc, 30.0); // 火焰底部
                } else if (j == 1) {
                    ParticleEffect.REDSTONE.display(c2, particleLoc, 30.0); // 火焰中部
                } else {
                    ParticleEffect.REDSTONE.display(c3, particleLoc, 30.0); // 火焰顶部
                }
            }
        }

        // 添加一些火花
        for (int i = 0; i < 5; i++) {
            double angle = 2 * Math.PI * i / 5;
            double radius = 0.6;
            double x = radius * Math.cos(angle);
            double z = radius * Math.sin(angle);

            Location sparkLoc = loc.clone().add(x, 0.8 + Math.random() * 0.5, z);
            ParticleEffect.FLAME.display(0, 0, 0, 0, 1, sparkLoc, 30.0);
        }
    }

    /**
     * 生成星云特效 (15星套装)
     */
    public static void SpawnNebula(Player p, ParticleEffect.OrdinaryColor c1, ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        Location loc = p.getLocation().clone();

        // 创建环绕玩家的星云粒子
        double radius = 1.0;
        int layers = 3;
        int particlesPerLayer = 12;

        for (int layer = 0; layer < layers; layer++) {
            double layerHeight = layer * 0.5; // 每层高度
            double layerRadius = radius - (layer * 0.2); // 每层半径略微减小

            for (int i = 0; i < particlesPerLayer; i++) {
                double angle = 2 * Math.PI * i / particlesPerLayer;
                // 添加一些随机性
                angle += Math.random() * 0.2 - 0.1;

                double x = layerRadius * Math.cos(angle);
                double z = layerRadius * Math.sin(angle);

                Location particleLoc = loc.clone().add(x, layerHeight, z);

                // 使用三种颜色
                if (layer == 0) {
                    ParticleEffect.REDSTONE.display(c1, particleLoc, 30.0);
                } else if (layer == 1) {
                    ParticleEffect.REDSTONE.display(c2, particleLoc, 30.0);
                } else {
                    ParticleEffect.REDSTONE.display(c3, particleLoc, 30.0);
                }
            }
        }

        // 添加一些星星点缀
        for (int i = 0; i < 8; i++) {
            double angle = 2 * Math.PI * i / 8;
            double x = (radius + 0.3) * Math.cos(angle);
            double z = (radius + 0.3) * Math.sin(angle);
            double y = Math.random() * 1.5;

            Location starLoc = loc.clone().add(x, y, z);
            ParticleEffect.SPELL_INSTANT.display(0, 0, 0, 0, 1, starLoc, 30.0);
        }
    }

    /**
     * 生成龙卷风特效 (18星套装)
     */
    public static void SpawnTornado(Player p, ParticleEffect.OrdinaryColor c1, ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        Location loc = p.getLocation().clone();

        // 创建螺旋上升的龙卷风效果
        double maxHeight = 2.5;
        double radius = 0.6;
        int spirals = 3; // 螺旋数量
        int pointsPerSpiral = 20; // 每个螺旋的点数

        for (int spiral = 0; spiral < spirals; spiral++) {
            double spiralOffset = 2 * Math.PI * spiral / spirals; // 螺旋间的角度偏移

            for (int i = 0; i < pointsPerSpiral; i++) {
                double ratio = (double) i / pointsPerSpiral;
                double height = ratio * maxHeight;
                double angle = spiralOffset + ratio * 4 * Math.PI; // 随高度增加旋转

                // 随高度减小半径，形成锥形
                double currentRadius = radius * (1 - ratio * 0.5);

                double x = currentRadius * Math.cos(angle);
                double z = currentRadius * Math.sin(angle);

                Location particleLoc = loc.clone().add(x, height, z);

                // 根据高度使用不同颜色
                if (height < maxHeight / 3) {
                    ParticleEffect.REDSTONE.display(c1, particleLoc, 30.0);
                } else if (height < maxHeight * 2 / 3) {
                    ParticleEffect.REDSTONE.display(c2, particleLoc, 30.0);
                } else {
                    ParticleEffect.REDSTONE.display(c3, particleLoc, 30.0);
                }
            }
        }

        // 在龙卷风顶部添加一些粒子
        Location topLoc = loc.clone().add(0, maxHeight, 0);
        ParticleEffect.CLOUD.display(0, 0, 0, 0.05f, 3, topLoc, 30.0);
    }

    /**
     * 生成星星特效 (19星套装)
     */
    public static void SpawnStars(Player p, ParticleEffect.OrdinaryColor c1, ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        Location loc = p.getLocation().clone();

        // 创建环绕玩家的星星
        int starCount = 5;
        double radius = 1.2;

        for (int i = 0; i < starCount; i++) {
            // 计算星星的位置
            double angle = 2 * Math.PI * i / starCount;
            double x = radius * Math.cos(angle);
            double z = radius * Math.sin(angle);
            double y = 1.0 + Math.sin(angle) * 0.5; // 高度变化

            Location starCenter = loc.clone().add(x, y, z);

            // 创建五角星形状
            int points = 5;
            double innerRadius = 0.1;
            double outerRadius = 0.2;

            for (int j = 0; j < points * 2; j++) {
                double starAngle = Math.PI * j / points;
                double starRadius = (j % 2 == 0) ? outerRadius : innerRadius;

                double starX = starRadius * Math.cos(starAngle);
                double starZ = starRadius * Math.sin(starAngle);

                Location pointLoc = starCenter.clone().add(starX, 0, starZ);

                // 根据星星的索引使用不同颜色
                if (i % 3 == 0) {
                    ParticleEffect.REDSTONE.display(c1, pointLoc, 30.0);
                } else if (i % 3 == 1) {
                    ParticleEffect.REDSTONE.display(c2, pointLoc, 30.0);
                } else {
                    ParticleEffect.REDSTONE.display(c3, pointLoc, 30.0);
                }
            }

            // 添加一些闪烁效果
            if (Math.random() < 0.3) {
                ParticleEffect.FIREWORKS_SPARK.display(0, 0, 0, 0, 1, starCenter, 30.0);
            }
        }

        // 在头顶上方添加一个大星星
        Location topLoc = loc.clone().add(0, 2.5, 0);
        ParticleEffect.SPELL_WITCH.display(0, 0, 0, 0, 3, topLoc, 30.0);
    }

    /**
     * 雷电特效 - 强力电击效果
     */
    public static void SpawnLightning(Player p, ParticleEffect.OrdinaryColor color1,
            ParticleEffect.OrdinaryColor color2, ParticleEffect.OrdinaryColor color3) {
        Location loc = p.getLocation().clone().add(0, 1.0, 0);

        // 主雷电柱
        for (double y = 0; y < 3.0; y += 0.1) {
            Location lightningLoc = loc.clone().add(0, y, 0);

            // 添加随机偏移模拟闪电效果
            double offsetX = (Math.random() - 0.5) * 0.3;
            double offsetZ = (Math.random() - 0.5) * 0.3;
            lightningLoc.add(offsetX, 0, offsetZ);

            ParticleEffect.REDSTONE.display(color1, lightningLoc, 30.0);

            // 添加电火花
            if (Math.random() < 0.3) {
                ParticleEffect.SPELL_INSTANT.display(0.0f, 0.0f, 0.0f, 0.1f, 2, lightningLoc, 30.0);
            }
        }

        // 雷电分支
        for (int i = 0; i < 4; i++) {
            double angle = (Math.PI * 2 * i) / 4;
            for (double r = 0; r < 1.5; r += 0.2) {
                double x = Math.cos(angle) * r;
                double z = Math.sin(angle) * r;
                double y = 1.5 + Math.random() * 0.5;

                Location branchLoc = loc.clone().add(x, y, z);
                ParticleEffect.REDSTONE.display(color2, branchLoc, 30.0);
            }
        }
    }

    /**
     * 冰霜特效 - 冰冷寒气效果
     */
    public static void SpawnFrost(Player p, ParticleEffect.OrdinaryColor color1, ParticleEffect.OrdinaryColor color2,
            ParticleEffect.OrdinaryColor color3) {
        Location loc = p.getLocation().clone().add(0, 0.5, 0);

        // 冰霜螺旋
        long time = System.currentTimeMillis();
        for (double t = 0; t < Math.PI * 4; t += Math.PI / 16) {
            double radius = 1.5 - (t / (Math.PI * 4)) * 1.0; // 向上收缩
            double x = Math.cos(t + time / 200.0) * radius;
            double z = Math.sin(t + time / 200.0) * radius;
            double y = (t / (Math.PI * 4)) * 2.5;

            Location frostLoc = loc.clone().add(x, y, z);
            ParticleEffect.REDSTONE.display(color1, frostLoc, 30.0);

            // 添加雪花效果
            if (Math.random() < 0.4) {
                ParticleEffect.SNOW_SHOVEL.display(0, 0, 0, 0, 1, frostLoc, 30.0);
            }
        }

        // 地面冰霜圈
        for (double angle = 0; angle < Math.PI * 2; angle += Math.PI / 12) {
            double x = Math.cos(angle) * 2.0;
            double z = Math.sin(angle) * 2.0;
            Location groundLoc = loc.clone().add(x, -0.3, z);
            ParticleEffect.REDSTONE.display(color3, groundLoc, 30.0);
        }
    }

    /**
     * 暗影特效 - 黑暗力量效果
     */
    public static void SpawnShadow(Player p, ParticleEffect.OrdinaryColor color1, ParticleEffect.OrdinaryColor color2,
            ParticleEffect.OrdinaryColor color3) {
        Location loc = p.getLocation().clone().add(0, 1.0, 0);
        long time = System.currentTimeMillis();

        // 暗影触手
        for (int i = 0; i < 6; i++) {
            double baseAngle = (Math.PI * 2 * i) / 6;
            for (double t = 0; t < Math.PI * 2; t += Math.PI / 8) {
                double angle = baseAngle + Math.sin(time / 300.0 + i) * 0.5;
                double radius = 1.0 + Math.sin(t + time / 400.0) * 0.5;
                double x = Math.cos(angle) * radius;
                double z = Math.sin(angle) * radius;
                double y = Math.sin(t) * 1.5;

                Location shadowLoc = loc.clone().add(x, y, z);
                ParticleEffect.REDSTONE.display(color1, shadowLoc, 30.0);
            }
        }

        // 暗影粒子云
        for (int i = 0; i < 15; i++) {
            double x = (Math.random() - 0.5) * 3.0;
            double y = Math.random() * 2.5;
            double z = (Math.random() - 0.5) * 3.0;

            Location cloudLoc = loc.clone().add(x, y, z);
            ParticleEffect.SMOKE_LARGE.display(0, 0, 0, 0, 1, cloudLoc, 30.0);
            ParticleEffect.REDSTONE.display(color2, cloudLoc, 30.0);
        }
    }

    /**
     * 彩虹特效 - 七彩光芒效果
     */
    public static void SpawnRainbow(Player p, ParticleEffect.OrdinaryColor color1, ParticleEffect.OrdinaryColor color2,
            ParticleEffect.OrdinaryColor color3) {
        Location loc = p.getLocation().clone().add(0, 1.0, 0);
        long time = System.currentTimeMillis();

        // 彩虹圆环
        for (double angle = 0; angle < Math.PI * 2; angle += Math.PI / 24) {
            double radius = 2.0;
            double x = Math.cos(angle + time / 500.0) * radius;
            double z = Math.sin(angle + time / 500.0) * radius;
            double y = Math.sin(angle * 3 + time / 300.0) * 0.5;

            // 计算彩虹颜色
            float hue = (float) ((angle + time / 1000.0) % (Math.PI * 2)) / (float) (Math.PI * 2);
            java.awt.Color rainbowColor = java.awt.Color.getHSBColor(hue, 1.0f, 1.0f);
            ParticleEffect.OrdinaryColor particleColor = new ParticleEffect.OrdinaryColor(
                    rainbowColor.getRed(), rainbowColor.getGreen(), rainbowColor.getBlue());

            Location rainbowLoc = loc.clone().add(x, y, z);
            ParticleEffect.REDSTONE.display(particleColor, rainbowLoc, 30.0);
        }

        // 彩虹射线
        for (int i = 0; i < 8; i++) {
            double angle = (Math.PI * 2 * i) / 8 + time / 400.0;
            for (double r = 0; r < 3.0; r += 0.3) {
                double x = Math.cos(angle) * r;
                double z = Math.sin(angle) * r;
                double y = 1.5 + Math.sin(r + time / 200.0) * 0.3;

                float hue = (float) ((i + time / 1000.0) % 8) / 8.0f;
                java.awt.Color rayColor = java.awt.Color.getHSBColor(hue, 1.0f, 1.0f);
                ParticleEffect.OrdinaryColor particleColor = new ParticleEffect.OrdinaryColor(
                        rayColor.getRed(), rayColor.getGreen(), rayColor.getBlue());

                Location rayLoc = loc.clone().add(x, y, z);
                ParticleEffect.REDSTONE.display(particleColor, rayLoc, 30.0);
            }
        }
    }

    /**
     * 时空裂缝特效 - 空间扭曲效果
     */
    public static void SpawnSpaceRift(Player p, ParticleEffect.OrdinaryColor color1,
            ParticleEffect.OrdinaryColor color2, ParticleEffect.OrdinaryColor color3) {
        Location loc = p.getLocation().clone().add(0, 1.5, 0);
        long time = System.currentTimeMillis();

        // 时空裂缝主体
        for (double t = 0; t < Math.PI * 2; t += Math.PI / 16) {
            double distortion = Math.sin(t * 3 + time / 150.0) * 0.3;
            double x = Math.cos(t) * (1.5 + distortion);
            double z = Math.sin(t) * (0.3 + distortion * 0.5);
            double y = Math.sin(t * 2 + time / 200.0) * 0.8;

            Location riftLoc = loc.clone().add(x, y, z);
            ParticleEffect.REDSTONE.display(color1, riftLoc, 30.0);
            ParticleEffect.PORTAL.display(0.0f, 0.0f, 0.0f, 0.5f, 2, riftLoc, 30.0);
        }

        // 空间碎片
        for (int i = 0; i < 12; i++) {
            double angle = (Math.PI * 2 * i) / 12 + time / 300.0;
            double radius = 2.5 + Math.sin(time / 400.0 + i) * 0.5;
            double x = Math.cos(angle) * radius;
            double z = Math.sin(angle) * radius;
            double y = Math.sin(angle * 2 + time / 250.0) * 1.0;

            Location fragmentLoc = loc.clone().add(x, y, z);
            ParticleEffect.REDSTONE.display(color2, fragmentLoc, 30.0);

            if (Math.random() < 0.3) {
                ParticleEffect.ENCHANTMENT_TABLE.display(0, 0, 0, 1, 3, fragmentLoc, 30.0);
            }
        }
    }

    /**
     * 套装特效方法
     */
    public static void effectSuit(Player p, SuitManager.SuitAttribute attribute) {
        // 检查是否启用特效叠加
        if (attribute.enableStacking && !attribute.effects.isEmpty()) {
            // 新的多特效系统
            spawnMultipleSuitEffects(p, attribute);
        } else {
            // 兼容旧的单特效系统
            spawnSingleSuitEffect(p, attribute);
        }
    }

    /**
     * 生成多个套装特效（叠加模式）
     */
    private static void spawnMultipleSuitEffects(Player p, SuitManager.SuitAttribute attribute) {
        // 显示所有启用的特效
        for (SuitManager.EffectConfig effect : attribute.effects) {
            if (effect.enabled) {
                // 获取每个特效的颜色
                ParticleEffect.OrdinaryColor color1 = getColorFromString(effect.colore1);
                ParticleEffect.OrdinaryColor color2 = getColorFromString(effect.colore2);
                ParticleEffect.OrdinaryColor color3 = getColorFromString(effect.colore3);

                spawnSuitEffectByType(p, effect.type, color1, color2, color3);
            }
        }
    }

    /**
     * 生成单个套装特效（兼容模式）
     */
    private static void spawnSingleSuitEffect(Player p, SuitManager.SuitAttribute attribute) {
        // 获取颜色对象
        ParticleEffect.OrdinaryColor color1 = getColorFromString(attribute.color1);
        ParticleEffect.OrdinaryColor color2 = getColorFromString(attribute.color2);
        ParticleEffect.OrdinaryColor color3 = getColorFromString(attribute.color3);

        spawnSuitEffectByType(p, attribute.effectType, color1, color2, color3);
    }

    /**
     * 根据特效类型生成套装特效
     */
    private static void spawnSuitEffectByType(Player p, String effectType, ParticleEffect.OrdinaryColor color1,
            ParticleEffect.OrdinaryColor color2, ParticleEffect.OrdinaryColor color3) {
        // 检查是否使用PlayerParticles特效
        if (effectType.startsWith("pp_")) {
            // 使用PlayerParticles集成特效
            ParticleEffect.OrdinaryColor[] colors = { color1, color2, color3 };
            PlayerParticlesIntegration.displayPlayerParticlesEffect(p, effectType, colors);
            return;
        }

        // 根据特效类型显示不同特效
        switch (effectType.toLowerCase()) {
            case "halo":
                effectlisten.SpawnHalo(p, color1, color2, color3);
                break;
            case "flame":
                effectlisten.SpawnFlame(p, color1, color2, color3);
                break;
            case "nebula":
                effectlisten.SpawnNebula(p, color1, color2, color3);
                break;
            case "tornado":
                effectlisten.SpawnTornado(p, color1, color2, color3);
                break;
            case "stars":
                effectlisten.SpawnStars(p, color1, color2, color3);
                break;
            case "spiral":
                effectlisten.SpawnSpiral(p, color1, color2, color3);
                break;
            case "sphere":
                effectlisten.SpawnSphere(p, color1, color2, color3);
                break;
            case "orbit":
                effectlisten.SpawnOrbit(p, color1, color2, color3);
                break;
            case "cube":
                effectlisten.SpawnCube(p, color1, color2, color3);
                break;
            case "helix":
                effectlisten.SpawnHelix(p, color1, color2, color3);
                break;
            case "batman":
                effectlisten.SpawnBatman(p, color1, color2, color3);
                break;
            case "popper":
                effectlisten.SpawnPopper(p, color1, color2, color3);
                break;
            case "pulse":
                effectlisten.SpawnPulse(p, color1, color2, color3);
                break;
            case "whirl":
                effectlisten.SpawnWhirl(p, color1, color2, color3);
                break;
            case "whirlwind":
                effectlisten.SpawnWhirlwind(p, color1, color2, color3);
                break;
            case "invocation":
                effectlisten.SpawnInvocation(p, color1, color2, color3);
                break;
            case "lightning":
                effectlisten.SpawnLightning(p, color1, color2, color3);
                break;
            case "rainbow":
                effectlisten.SpawnRainbow(p, color1, color2, color3);
                break;
            case "spacerift":
                effectlisten.SpawnSpaceRift(p, color1, color2, color3);
                break;
            case "frost":
                effectlisten.SpawnFrost(p, color1, color2, color3);
                break;
            case "shadow":
                effectlisten.SpawnShadow(p, color1, color2, color3);
                break;
            // 淬炼5.6版本特效
            case "flame_basic":
                effectlisten.SpawnFlameBasic(p, color1, color2, color3);
                break;
            case "footstep":
                effectlisten.SpawnFootstep(p, color1, color2, color3);
                break;
            case "spiral_flame":
                effectlisten.SpawnSpiralFlame(p, color1, color2, color3);
                break;
            case "rotating_halo":
                effectlisten.SpawnRotatingHalo(p, color1, color2, color3);
                break;
            case "four_direction_spell":
                effectlisten.SpawnFourDirectionSpell(p, color1, color2, color3);
                break;
            case "wings_56":
                effectlisten.SpawnWings56(p, color1, color2, color3);
                break;
            case "wings":
            default:
                effectlisten.SpawnWings(p, color1, color2, color3);
                break;
        }
    }

    /**
     * 生成螺旋特效
     */
    public static void SpawnSpiral(Player p, ParticleEffect.OrdinaryColor c1, ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        Location loc = p.getLocation().clone();

        // 创建双螺旋结构
        double height = 3.0;
        double radius = 0.8;
        int points = 40;

        for (int i = 0; i < points; i++) {
            double ratio = (double) i / points;
            double y = ratio * height;
            double angle = ratio * 4 * Math.PI; // 两圈螺旋

            // 第一条螺旋
            double x1 = radius * Math.cos(angle);
            double z1 = radius * Math.sin(angle);
            Location spiral1 = loc.clone().add(x1, y, z1);

            // 第二条螺旋（相位差180度）
            double x2 = radius * Math.cos(angle + Math.PI);
            double z2 = radius * Math.sin(angle + Math.PI);
            Location spiral2 = loc.clone().add(x2, y, z2);

            // 根据高度使用不同颜色
            if (y < height / 3) {
                ParticleEffect.REDSTONE.display(c1, spiral1, 30.0);
                ParticleEffect.REDSTONE.display(c2, spiral2, 30.0);
            } else if (y < height * 2 / 3) {
                ParticleEffect.REDSTONE.display(c2, spiral1, 30.0);
                ParticleEffect.REDSTONE.display(c3, spiral2, 30.0);
            } else {
                ParticleEffect.REDSTONE.display(c3, spiral1, 30.0);
                ParticleEffect.REDSTONE.display(c1, spiral2, 30.0);
            }
        }

        // 在顶部添加一些闪烁粒子
        Location topLoc = loc.clone().add(0, height, 0);
        ParticleEffect.FIREWORKS_SPARK.display(0, 0, 0, 0, 3, topLoc, 30.0);
    }

    /**
     * 生成球体特效
     */
    public static void SpawnSphere(Player p, ParticleEffect.OrdinaryColor c1, ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        Location loc = p.getLocation().clone().add(0, 1.5, 0);

        double radius = 1.0;
        int points = 50;

        for (int i = 0; i < points; i++) {
            // 使用球坐标系生成均匀分布的点
            double phi = Math.acos(1 - 2 * Math.random()); // 极角
            double theta = 2 * Math.PI * Math.random(); // 方位角

            double x = radius * Math.sin(phi) * Math.cos(theta);
            double y = radius * Math.cos(phi);
            double z = radius * Math.sin(phi) * Math.sin(theta);

            Location particleLoc = loc.clone().add(x, y, z);

            // 根据高度使用不同颜色
            if (y > radius * 0.3) {
                ParticleEffect.REDSTONE.display(c1, particleLoc, 30.0);
            } else if (y > -radius * 0.3) {
                ParticleEffect.REDSTONE.display(c2, particleLoc, 30.0);
            } else {
                ParticleEffect.REDSTONE.display(c3, particleLoc, 30.0);
            }
        }

        // 在球心添加一个亮点
        ParticleEffect.SPELL_WITCH.display(0, 0, 0, 0, 1, loc, 30.0);
    }

    /**
     * 生成轨道特效
     */
    public static void SpawnOrbit(Player p, ParticleEffect.OrdinaryColor c1, ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        Location loc = p.getLocation().clone().add(0, 1.0, 0);

        // 创建三个不同的轨道
        double[] radii = { 0.8, 1.2, 1.6 };
        double[] heights = { 0.0, 0.5, 1.0 };
        ParticleEffect.OrdinaryColor[] colors = { c1, c2, c3 };

        for (int orbit = 0; orbit < 3; orbit++) {
            double radius = radii[orbit];
            double height = heights[orbit];
            ParticleEffect.OrdinaryColor color = colors[orbit];

            int particles = 16;
            for (int i = 0; i < particles; i++) {
                double angle = 2 * Math.PI * i / particles;
                // 为每个轨道添加不同的旋转偏移
                angle += orbit * Math.PI / 3;

                double x = radius * Math.cos(angle);
                double z = radius * Math.sin(angle);

                Location orbitLoc = loc.clone().add(x, height, z);
                ParticleEffect.REDSTONE.display(color, orbitLoc, 30.0);
            }
        }

        // 在中心添加核心粒子
        ParticleEffect.ENCHANTMENT_TABLE.display(0, 0, 0, 0, 5, loc, 30.0);
    }

    /**
     * 生成立方体特效
     */
    public static void SpawnCube(Player p, ParticleEffect.OrdinaryColor c1, ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        Location loc = p.getLocation().clone().add(0, 1.0, 0);

        double size = 1.0;
        int pointsPerEdge = 8;

        // 绘制立方体的12条边
        for (int edge = 0; edge < 12; edge++) {
            for (int i = 0; i < pointsPerEdge; i++) {
                double ratio = (double) i / (pointsPerEdge - 1);
                Location edgeLoc = getCubeEdgePoint(loc, size, edge, ratio);

                // 根据边的索引使用不同颜色
                if (edge < 4) {
                    ParticleEffect.REDSTONE.display(c1, edgeLoc, 30.0);
                } else if (edge < 8) {
                    ParticleEffect.REDSTONE.display(c2, edgeLoc, 30.0);
                } else {
                    ParticleEffect.REDSTONE.display(c3, edgeLoc, 30.0);
                }
            }
        }

        // 在立方体中心添加核心
        ParticleEffect.PORTAL.display(0, 0, 0, 0, 3, loc, 30.0);
    }

    /**
     * 获取立方体边上的点
     */
    private static Location getCubeEdgePoint(Location center, double size, int edge, double ratio) {
        double half = size / 2;
        double x = 0, y = 0, z = 0;

        switch (edge) {
            // 底面的4条边
            case 0:
                x = -half + ratio * size;
                y = -half;
                z = -half;
                break;
            case 1:
                x = half;
                y = -half;
                z = -half + ratio * size;
                break;
            case 2:
                x = half - ratio * size;
                y = -half;
                z = half;
                break;
            case 3:
                x = -half;
                y = -half;
                z = half - ratio * size;
                break;
            // 顶面的4条边
            case 4:
                x = -half + ratio * size;
                y = half;
                z = -half;
                break;
            case 5:
                x = half;
                y = half;
                z = -half + ratio * size;
                break;
            case 6:
                x = half - ratio * size;
                y = half;
                z = half;
                break;
            case 7:
                x = -half;
                y = half;
                z = half - ratio * size;
                break;
            // 垂直的4条边
            case 8:
                x = -half;
                y = -half + ratio * size;
                z = -half;
                break;
            case 9:
                x = half;
                y = -half + ratio * size;
                z = -half;
                break;
            case 10:
                x = half;
                y = -half + ratio * size;
                z = half;
                break;
            case 11:
                x = -half;
                y = -half + ratio * size;
                z = half;
                break;
        }

        return center.clone().add(x, y, z);
    }

    /**
     * 生成螺旋桨特效
     */
    public static void SpawnHelix(Player p, ParticleEffect.OrdinaryColor c1, ParticleEffect.OrdinaryColor c2,
            ParticleEffect.OrdinaryColor c3) {
        Location loc = p.getLocation().clone();

        // 创建四重螺旋
        double height = 2.5;
        double radius = 0.6;
        int helixCount = 4;
        int pointsPerHelix = 20;

        for (int helix = 0; helix < helixCount; helix++) {
            double helixOffset = 2 * Math.PI * helix / helixCount;
            ParticleEffect.OrdinaryColor color = (helix % 3 == 0) ? c1 : (helix % 3 == 1) ? c2 : c3;

            for (int i = 0; i < pointsPerHelix; i++) {
                double ratio = (double) i / pointsPerHelix;
                double y = ratio * height;
                double angle = helixOffset + ratio * 3 * Math.PI;

                double x = radius * Math.cos(angle);
                double z = radius * Math.sin(angle);

                Location helixLoc = loc.clone().add(x, y, z);
                ParticleEffect.REDSTONE.display(color, helixLoc, 30.0);
            }
        }

        // 在顶部和底部添加连接点
        ParticleEffect.SPELL_MOB.display(c1, loc.clone(), 30.0);
        ParticleEffect.SPELL_MOB.display(c3, loc.clone().add(0, height, 0), 30.0);
    }

    /**
     * 蝙蝠侠标志特效 - 基于数学方程的蝙蝠侠标志
     * 方程来源: https://www.desmos.com/calculator/cscx2zcrlf
     */
    public static void SpawnBatman(Player p, ParticleEffect.OrdinaryColor color1,
            ParticleEffect.OrdinaryColor color2,
            ParticleEffect.OrdinaryColor color3) {
        Location loc = p.getLocation().add(0, 2, 0);
        float yaw = loc.getYaw();

        // 蝙蝠侠标志的各个部分

        // Segment 1 - 上半部分的翅膀
        for (double x = -7; x <= -3; x += 0.1) {
            double y = 3 * Math.sqrt(-Math.pow(x / 7, 2) + 1);
            addBatmanParticle(loc, x, y, yaw, color1);
        }
        for (double x = 3; x <= 7; x += 0.1) {
            double y = 3 * Math.sqrt(-Math.pow(x / 7, 2) + 1);
            addBatmanParticle(loc, x, y, yaw, color1);
        }

        // Segment 2 - 下半部分的翅膀
        for (double x = -7; x <= -4; x += 0.1) {
            double y = -3 * Math.sqrt(-Math.pow(x / 7, 2) + 1);
            addBatmanParticle(loc, x, y, yaw, color1);
        }
        for (double x = 4; x <= 7; x += 0.1) {
            double y = -3 * Math.sqrt(-Math.pow(x / 7, 2) + 1);
            addBatmanParticle(loc, x, y, yaw, color1);
        }

        // Segment 3 - 中间的身体部分
        for (double x = -4; x <= 4; x += 0.2) {
            double y = Math.abs(x / 2) - ((3 * Math.sqrt(33) - 7) / 112) * Math.pow(x, 2) +
                    Math.sqrt(1 - Math.pow(Math.abs(Math.abs(x) - 2) - 1, 2)) - 3;
            addBatmanParticle(loc, x, y, yaw, color2);
        }

        // Segment 4 - 耳朵部分
        for (double x = -1; x <= -0.75; x += 0.05) {
            double y = 9 - 8 * Math.abs(x);
            addBatmanParticle(loc, x, y, yaw, color3);
        }
        for (double x = 0.75; x <= 1; x += 0.05) {
            double y = 9 - 8 * Math.abs(x);
            addBatmanParticle(loc, x, y, yaw, color3);
        }

        // Segment 5 - 头部连接
        for (double x = -0.75; x <= -0.5; x += 0.1) {
            double y = 3 * Math.abs(x) + 0.75;
            addBatmanParticle(loc, x, y, yaw, color2);
        }
        for (double x = 0.5; x <= 0.75; x += 0.1) {
            double y = 3 * Math.abs(x) + 0.75;
            addBatmanParticle(loc, x, y, yaw, color2);
        }

        // Segment 6 - 头顶
        for (double x = -0.5; x <= 0.5; x += 0.2) {
            double y = 2.25;
            addBatmanParticle(loc, x, y, yaw, color3);
        }

        // Segment 7 - 下半身
        for (double x = -3; x <= -1; x += 0.05) {
            double y = 1.5 - 0.5 * Math.abs(x) - ((6 * Math.sqrt(10)) / 14) *
                    (Math.sqrt(3 - Math.pow(x, 2) + 2 * Math.abs(x)) - 2);
            addBatmanParticle(loc, x, y, yaw, color2);
        }
        for (double x = 1; x <= 3; x += 0.05) {
            double y = 1.5 - 0.5 * Math.abs(x) - ((6 * Math.sqrt(10)) / 14) *
                    (Math.sqrt(3 - Math.pow(x, 2) + 2 * Math.abs(x)) - 2);
            addBatmanParticle(loc, x, y, yaw, color2);
        }
    }

    /**
     * 添加蝙蝠侠标志的单个粒子点，考虑玩家朝向
     */
    private static void addBatmanParticle(Location center, double x, double y, float yaw,
            ParticleEffect.OrdinaryColor color) {
        // 缩放因子
        double scale = 0.3;
        x *= scale;
        y *= scale;

        // 根据玩家朝向旋转
        double radians = Math.toRadians(-yaw);
        double rotatedX = x * Math.cos(radians) - 0 * Math.sin(radians);
        double rotatedZ = x * Math.sin(radians) + 0 * Math.cos(radians);

        Location particleLoc = center.clone().add(rotatedX, y, rotatedZ);
        ParticleEffect.REDSTONE.display(color, particleLoc, 50.0);
    }

    /**
     * 爆炸螺旋特效 - 螺旋上升后爆炸的特效
     * 基于PlayerParticles的Popper特效
     */
    public static void SpawnPopper(Player p, ParticleEffect.OrdinaryColor color1,
            ParticleEffect.OrdinaryColor color2,
            ParticleEffect.OrdinaryColor color3) {
        Location loc = p.getLocation().add(0, 1, 0);
        long time = System.currentTimeMillis();

        // Popper特效参数
        double radius = 1.0; // 底部半径
        double grow = 0.08; // 高度增长率
        double radials = Math.PI / 16; // 螺旋陡峭度
        int helices = 2; // 螺旋数量
        int maxStep = 32; // 最大步数
        int popParticleAmount = 10; // 爆炸粒子数量
        double popSpread = 0.5; // 爆炸扩散范围
        double popOffset = 1.5; // 爆炸高度偏移

        // 计算当前步数（基于时间的循环）
        int step = (int) ((time / 100) % maxStep); // 每100ms一步

        // 计算当前半径（随步数减小）
        double currentRadius = radius * (1 - (double) step / maxStep);

        // 绘制螺旋
        for (int i = 0; i < helices; i++) {
            double angle = step * radials + (2 * Math.PI * i / helices);
            double x = Math.cos(angle) * currentRadius;
            double z = Math.sin(angle) * currentRadius;
            double y = step * grow - 1;

            Location particleLoc = loc.clone().add(x, y, z);

            // 根据螺旋选择颜色
            ParticleEffect.OrdinaryColor color = (i == 0) ? color1 : color2;
            ParticleEffect.REDSTONE.display(color, particleLoc, 50.0);
        }

        // 在最高点时创建爆炸效果
        if (step == maxStep - 1) {
            Location popLoc = loc.clone().add(0, popOffset, 0);

            // 创建爆炸粒子
            for (int i = 0; i < popParticleAmount; i++) {
                // 随机方向
                double angle = Math.random() * 2 * Math.PI;
                double pitch = (Math.random() - 0.5) * Math.PI;

                double x = Math.cos(angle) * Math.cos(pitch) * popSpread;
                double y = Math.sin(pitch) * popSpread;
                double z = Math.sin(angle) * Math.cos(pitch) * popSpread;

                Location explosionLoc = popLoc.clone().add(x, y, z);
                ParticleEffect.REDSTONE.display(color3, explosionLoc, 50.0);
            }

            // 额外的爆炸中心效果
            ParticleEffect.REDSTONE.display(color3, popLoc, 50.0);
            ParticleEffect.REDSTONE.display(color1, popLoc.clone().add(0, 0.1, 0), 50.0);
            ParticleEffect.REDSTONE.display(color2, popLoc.clone().add(0, -0.1, 0), 50.0);
        }
    }

    /**
     * 脉冲特效 - 圆形脉冲向外扩散的特效
     * 基于PlayerParticles的Pulse特效
     */
    public static void SpawnPulse(Player p, ParticleEffect.OrdinaryColor color1,
            ParticleEffect.OrdinaryColor color2,
            ParticleEffect.OrdinaryColor color3) {
        Location loc = p.getLocation().add(0, 0.1, 0); // 脚部稍上位置
        long time = System.currentTimeMillis();

        // Pulse特效参数
        int points = 50; // 脉冲圆圈的点数
        double radius = 0.5; // 脉冲圆圈的半径
        int delay = 15; // 脉冲间隔（tick）
        double speedMultiplier = 1.0; // 速度倍数

        // 计算当前步数（基于时间的循环）
        int step = (int) ((time / 50) % delay); // 每50ms一步，delay步为一个周期

        // 只在第一步时生成脉冲
        if (step == 0) {
            // 创建圆形脉冲
            for (int i = 0; i < points; i++) {
                double angle = Math.PI * 2 * ((double) i / points);
                double dx = Math.cos(angle) * radius;
                double dz = Math.sin(angle) * radius;

                Location particleLoc = loc.clone().add(dx, 0, dz);

                // 根据位置选择颜色
                ParticleEffect.OrdinaryColor color;
                if (i % 3 == 0) {
                    color = color1;
                } else if (i % 3 == 1) {
                    color = color2;
                } else {
                    color = color3;
                }

                ParticleEffect.REDSTONE.display(color, particleLoc, 50.0);
            }

            // 中心点特效
            ParticleEffect.REDSTONE.display(color2, loc.clone(), 50.0);

            // 额外的内圈效果
            for (int i = 0; i < points / 2; i++) {
                double angle = Math.PI * 2 * ((double) i / (points / 2));
                double dx = Math.cos(angle) * (radius * 0.3);
                double dz = Math.sin(angle) * (radius * 0.3);

                Location innerLoc = loc.clone().add(dx, 0.1, dz);
                ParticleEffect.REDSTONE.display(color1, innerLoc, 50.0);
            }

            // 外圈扩散效果
            for (int i = 0; i < points / 3; i++) {
                double angle = Math.PI * 2 * ((double) i / (points / 3));
                double dx = Math.cos(angle) * (radius * 1.5);
                double dz = Math.sin(angle) * (radius * 1.5);

                Location outerLoc = loc.clone().add(dx, -0.1, dz);
                ParticleEffect.REDSTONE.display(color3, outerLoc, 50.0);
            }
        }
    }

    /**
     * 旋涡特效 - 从中心向外旋转发射的射线
     * 基于PlayerParticles的Whirl特效
     */
    public static void SpawnWhirl(Player p, ParticleEffect.OrdinaryColor color1,
            ParticleEffect.OrdinaryColor color2,
            ParticleEffect.OrdinaryColor color3) {
        Location loc = p.getLocation().add(0, 0.1, 0); // 脚部稍上位置
        long time = System.currentTimeMillis();

        // Whirl特效参数
        int rays = 2; // 射线数量
        int steps = 40; // 完整旋转的步数
        double speedMultiplier = 1.0; // 速度倍数

        // 计算当前步数（基于时间的旋转）
        double step = (time / 50.0) % steps; // 每50ms一步
        double stepAngle = (step * Math.PI * 2) / steps;

        // 创建旋转射线
        for (int i = 0; i < rays; i++) {
            double baseAngle = stepAngle + (Math.PI * 2 * ((double) i / rays));

            // 创建射线上的多个粒子点
            for (int j = 0; j < 8; j++) {
                double distance = j * 0.3; // 射线长度
                double dx = Math.cos(baseAngle) * distance;
                double dz = Math.sin(baseAngle) * distance;

                Location particleLoc = loc.clone().add(dx, 0, dz);

                // 根据射线和距离选择颜色
                ParticleEffect.OrdinaryColor color;
                if (j < 3) {
                    color = color1; // 内圈
                } else if (j < 6) {
                    color = color2; // 中圈
                } else {
                    color = color3; // 外圈
                }

                ParticleEffect.REDSTONE.display(color, particleLoc, 50.0);
            }
        }

        // 中心点特效
        ParticleEffect.REDSTONE.display(color2, loc.clone(), 50.0);
    }

    /**
     * 旋风特效 - 更强烈的旋转射线特效
     * 基于PlayerParticles的Whirlwind特效
     */
    public static void SpawnWhirlwind(Player p, ParticleEffect.OrdinaryColor color1,
            ParticleEffect.OrdinaryColor color2,
            ParticleEffect.OrdinaryColor color3) {
        Location loc = p.getLocation().add(0, 0.1, 0); // 脚部稍上位置
        long time = System.currentTimeMillis();

        // Whirlwind特效参数
        int rays = 3; // 射线数量（比Whirl多）
        int steps = 40; // 完整旋转的步数
        double speedMultiplier = 2.5; // 更快的速度

        // 计算当前步数（基于时间的旋转）
        double step = (time / 30.0) % steps; // 更快的旋转（每30ms一步）
        double stepAngle = (step * Math.PI * 2) / steps;

        // 创建旋转射线
        for (int i = 0; i < rays; i++) {
            double baseAngle = stepAngle + (Math.PI * 2 * ((double) i / rays));

            // 创建射线上的更多粒子点
            for (int j = 0; j < 12; j++) {
                double distance = j * 0.25; // 更密集的射线
                double dx = Math.cos(baseAngle) * distance;
                double dz = Math.sin(baseAngle) * distance;

                Location particleLoc = loc.clone().add(dx, 0, dz);

                // 根据射线选择颜色
                ParticleEffect.OrdinaryColor color;
                if (i == 0) {
                    color = color1;
                } else if (i == 1) {
                    color = color2;
                } else {
                    color = color3;
                }

                ParticleEffect.REDSTONE.display(color, particleLoc, 50.0);
            }
        }

        // 中心强化特效
        ParticleEffect.REDSTONE.display(color1, loc.clone(), 50.0);
        ParticleEffect.REDSTONE.display(color2, loc.clone().add(0, 0.1, 0), 50.0);
        ParticleEffect.REDSTONE.display(color3, loc.clone().add(0, -0.1, 0), 50.0);

        // 额外的外圈旋转效果
        for (int i = 0; i < 6; i++) {
            double outerAngle = stepAngle * 2 + (Math.PI * 2 * ((double) i / 6)); // 反向旋转
            double outerRadius = 3.0;
            double dx = Math.cos(outerAngle) * outerRadius;
            double dz = Math.sin(outerAngle) * outerRadius;

            Location outerLoc = loc.clone().add(dx, 0.2, dz);
            ParticleEffect.REDSTONE.display(color3, outerLoc, 50.0);
        }
    }

    /**
     * 召唤法阵特效 - 复杂的魔法阵效果
     * 基于PlayerParticles的Invocation特效
     */
    public static void SpawnInvocation(Player p, ParticleEffect.OrdinaryColor color1,
            ParticleEffect.OrdinaryColor color2,
            ParticleEffect.OrdinaryColor color3) {
        Location loc = p.getLocation().add(0, -0.9, 0); // 脚下位置
        long time = System.currentTimeMillis();

        // Invocation特效参数
        int spinningPoints = 6; // 旋转点数量
        double radius = 3.5; // 法阵半径（恢复原始大小）
        int circlePoints = 120; // 圆圈点数（恢复原始数量）
        double speedMultiplier = 1.0; // 速度倍数

        // 计算当前步数
        double step = (time / 100.0) % circlePoints; // 每100ms一步
        int circleStep = (int) ((time / 50) % circlePoints); // 圆圈步数
        double stepAngle = (step * Math.PI * 2) / circlePoints;

        // 1. 外圈法阵圆圈（每5步显示一次，模拟闪烁效果）
        if (circleStep % 5 == 0) {
            for (int i = 0; i < circlePoints; i++) {
                double angle = Math.PI * 2 * ((double) i / circlePoints);
                double dx = Math.cos(angle) * radius;
                double dz = Math.sin(angle) * radius;

                Location circleLoc = loc.clone().add(dx, 0, dz);
                ParticleEffect.REDSTONE.display(color3, circleLoc, 50.0);
            }
        }

        // 2. 顺时针旋转的内圈粒子
        for (int i = 0; i < spinningPoints; i++) {
            double angle = stepAngle + (Math.PI * 2 * ((double) i / spinningPoints));
            double dx = Math.cos(angle) * radius * 0.6; // 内圈半径
            double dz = Math.sin(angle) * radius * 0.6;

            Location clockwiseLoc = loc.clone().add(dx, 0.1, dz);
            ParticleEffect.REDSTONE.display(color1, clockwiseLoc, 50.0);

            // 添加向内的轨迹效果
            for (int j = 1; j <= 3; j++) {
                double innerRadius = radius * 0.6 * (1 - j * 0.2);
                double innerDx = Math.cos(angle) * innerRadius;
                double innerDz = Math.sin(angle) * innerRadius;

                Location innerLoc = loc.clone().add(innerDx, 0.05, innerDz);
                ParticleEffect.REDSTONE.display(color1, innerLoc, 50.0);
            }
        }

        // 3. 逆时针旋转的内圈粒子
        for (int i = 0; i < spinningPoints; i++) {
            double angle = -stepAngle + (Math.PI * 2 * ((double) i / spinningPoints));
            double dx = Math.cos(angle) * radius * 0.4; // 更内圈半径
            double dz = Math.sin(angle) * radius * 0.4;

            Location counterLoc = loc.clone().add(dx, 0.2, dz);
            ParticleEffect.REDSTONE.display(color2, counterLoc, 50.0);

            // 添加向外的轨迹效果
            for (int j = 1; j <= 3; j++) {
                double outerRadius = radius * 0.4 * (1 + j * 0.3);
                double outerDx = Math.cos(angle) * outerRadius;
                double outerDz = Math.sin(angle) * outerRadius;

                Location outerLoc = loc.clone().add(outerDx, 0.15, outerDz);
                ParticleEffect.REDSTONE.display(color2, outerLoc, 50.0);
            }
        }

        // 4. 中心法阵核心
        ParticleEffect.REDSTONE.display(color1, loc.clone().add(0, 0.3, 0), 50.0);
        ParticleEffect.REDSTONE.display(color2, loc.clone().add(0, 0.2, 0), 50.0);
        ParticleEffect.REDSTONE.display(color3, loc.clone().add(0, 0.1, 0), 50.0);

        // 5. 额外的魔法符文效果（六芒星）
        for (int i = 0; i < 6; i++) {
            double runeAngle = (Math.PI * 2 * i / 6) + stepAngle * 0.5;
            double runeDx = Math.cos(runeAngle) * radius * 0.8;
            double runeDz = Math.sin(runeAngle) * radius * 0.8;

            Location runeLoc = loc.clone().add(runeDx, 0.05, runeDz);
            ParticleEffect.REDSTONE.display(color3, runeLoc, 50.0);

            // 连接线到中心
            for (int j = 1; j <= 5; j++) {
                double lineRadius = radius * 0.8 * (1 - j * 0.15);
                double lineDx = Math.cos(runeAngle) * lineRadius;
                double lineDz = Math.sin(runeAngle) * lineRadius;

                Location lineLoc = loc.clone().add(lineDx, 0.03, lineDz);
                ParticleEffect.REDSTONE.display(color2, lineLoc, 50.0);
            }
        }

        // 6. 上升的能量柱效果
        for (int i = 0; i < 8; i++) {
            double height = i * 0.3;
            double energyRadius = radius * 0.1 * (1 - i * 0.1);

            for (int j = 0; j < 8; j++) {
                double energyAngle = (Math.PI * 2 * j / 8) + stepAngle * 2;
                double energyDx = Math.cos(energyAngle) * energyRadius;
                double energyDz = Math.sin(energyAngle) * energyRadius;

                Location energyLoc = loc.clone().add(energyDx, height, energyDz);
                ParticleEffect.REDSTONE.display(color1, energyLoc, 50.0);
            }
        }
    }

    /**
     * 从字符串获取颜色对象
     */
    private static ParticleEffect.OrdinaryColor getColorFromString(String colorName) {
        ParticleEffect.OrdinaryColor color = Cuilian.color.get(colorName);
        if (color != null) {
            return color;
        }

        // 如果颜色映射中没有，使用默认颜色
        switch (colorName) {
            case "红":
                return new ParticleEffect.OrdinaryColor(255, 0, 0);
            case "橙":
                return new ParticleEffect.OrdinaryColor(255, 128, 0);
            case "黄":
                return new ParticleEffect.OrdinaryColor(255, 255, 0);
            case "绿":
                return new ParticleEffect.OrdinaryColor(0, 255, 0);
            case "蓝":
                return new ParticleEffect.OrdinaryColor(0, 0, 255);
            case "粉":
                return new ParticleEffect.OrdinaryColor(255, 0, 255);
            case "黑":
                return new ParticleEffect.OrdinaryColor(0, 0, 0);
            case "灰":
                return new ParticleEffect.OrdinaryColor(128, 128, 128);
            case "白":
                return new ParticleEffect.OrdinaryColor(255, 255, 255);
            case "银":
                return new ParticleEffect.OrdinaryColor(192, 192, 192);
            case "紫":
                return new ParticleEffect.OrdinaryColor(128, 0, 128);
            default:
                return new ParticleEffect.OrdinaryColor(255, 0, 0); // 默认红色
        }
    }
}
