package cn.winde.cuilian.listeners;

import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import cn.winde.cuilian.clbh.Mygui;

/**
 * 玩家加入/退出监听器
 * 用于实时更新UI中的在线玩家列表
 */
public class PlayerJoinLeaveListener implements Listener {

    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        String playerName = event.getPlayer().getName();

        // 记录玩家登录时间（用于在线时长统计）
        Mygui.recordPlayerLogin(playerName);

        // 通知UI更新在线玩家列表和统计数据
        Mygui.onPlayerJoin(playerName);

        // 实时更新排行榜和统计数据
        Mygui.updateRealTimeData();
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        String playerName = event.getPlayer().getName();

        // 移除玩家登录记录（用于在线时长统计）
        Mygui.removePlayerLogin(playerName);

        // 通知UI更新在线玩家列表和统计数据
        Mygui.onPlayerLeave(playerName);

        // 实时更新排行榜和统计数据
        Mygui.updateRealTimeData();
    }
}
