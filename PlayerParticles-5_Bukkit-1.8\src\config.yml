# __________ __                           __________                __   __        __                            ________
# \______   \  | _____  ___ __  __________\______   \_____ ________/  |_|__| ____ |  |   ____   ______    ___  _|   ____/
#  |     ___/  | \__  \<   |  |/ __ \_  __ \     ___/\__  \\_  __ \   __\  |/ ___\|  | _/ __ \ /  ___/    \  \/ /____  \ 
#  |    |   |  |__/ __ \\___  \  ___/|  | \/    |     / __ \|  | \/|  | |  \  \___|  |_\  ___/ \___ \      \   //       \
#  |____|   |____(____  / ____|\___  >__|  |____|    (____  /__|   |__| |__|\___  >____/\___  >____  >      \_//______  /
#                     \/\/         \/                     \/                    \/          \/     \/                 \/ 

# This is the final build supporting Minecraft 1.8

# ==================================================== #
#                PlayerParticles Config                #
#               Welcome to the beginning               #
# ==================================================== #

# Changing this value will reset your config on the next server reload / restart. 
# I don't recommend changing it
# NOTE: Updating to a new version of the plugin will change this number and delete your config. 
#       Make sure you create a backup each time before you update!
version: 5

# If the command /pp gui is enabled
# Disable this if you have your own custom GUI through another plugin
# Default: true
gui-enabled: true

# The worlds which this plugin is disabled in
# Remove the [] before you enter world names
# Default: []
disabled-worlds: []
#  - your_world_name_here
#  - add_more_under_these

# Max fixed effects per player
# Default: 5
max-fixed-effects: 5

# Max fixed effect creation distance
# Determines how far away a player may create a fixed effect from themselves
# This measurement is in blocks
# Set to 0 for infinite distance
# Default: 128
max-fixed-effect-creation-distance: 128

# How many ticks to wait before spawning more particles
# Increasing this value may cause less lag (if there was any), but will decrease prettiness
# Only use whole numbers greater than or equal to 1
# Going over 3 will likely look terrible
# Default: 1
ticks-per-particle: 1

# ================================================================ #
#                     MESSAGE CONFIGURATION                        #
# Important Notes:                                                 #
# * You can use the & symbol to color the messages                 #
# * {TYPE} Will be replaced with whatever that message requires    #
# * You cannot use the apostrophe character! ( ' )                 #
# ================================================================ #

# If you're using other plugins to execute commands you may wish to turn off messages
# Default: true
messages-enabled: true

# Whether or not to use the message-prefix field when displaying messages
# Default: true
use-message-prefix: true

# The prefix to use for all PlayerParticle messages
# This is useless if use-message-prefix is set to false
# Default: '&7[&3PlayerParticles&7]'
message-prefix: '&7[&3PlayerParticles&7]'

# ------------- #
#   Particles   #
# ------------- #

# No Particle Permission
# Default: '&cYou do not have permission to use &b{TYPE} &cparticles!'
message-no-permission: '&cYou do not have permission to use &b{TYPE} &cparticles!'

# /pp list No Particles
# Default: '&cYou do not have permission to use any particles!'
message-no-particles: '&cYou do not have permission to use any particles!'

# Now Using Particles
# Default: '&aNow using &b{TYPE} &aparticles!'
message-now-using: '&aNow using &b{TYPE} &aparticles!'

# Cleared Particles
# Default: '&aYour particles have been cleared!'
message-cleared-particles: '&aYour particles have been cleared!'

# Invalid Particle Type
# Default: '&cInvalid particle type! &b/pp effects'
message-invalid-type: '&cInvalid particle type! &b/pp effects &c| &b/pp effect <type>'

# Particle Command Usage
# You should not change the text here, only the coloring
# Default: '&b/pp effect <type>'
message-particle-usage: '&b/pp effect <type>'

# -------------- #
#     Styles     #
# -------------- #

# No Style Permission
# Default: '&cYou do not have permission to use the style type &b{TYPE}&c!'
message-no-permission-style: '&cYou do not have permission to use the style type &b{TYPE}&c!'

# /pp styles No Styles
# Default: '&cYou do not have permission to use any styles!'
message-no-styles: '&cYou do not have permission to use any styles!'

# Now Using Style
# Default: '&aNow using the style type &b{TYPE}&a!'
message-now-using-style: '&aNow using the style type &b{TYPE}&a!'

# Cleared Style
# Default: '&aYour style has been cleared!'
message-cleared-style: '&aYour style has been cleared!'

# Invalid Style Type
# Default: '&cInvalid style type! &b/pp styles &c| &b/pp style <type>'
message-invalid-type-style: '&cInvalid style type! &b/pp styles &c| &b/pp style <type>'

# Style Command Usage
# You should not change the text here, only the coloring
# Default: '&b/pp style <type>'
message-style-usage: '&b/pp style <type>'

# ------------ #
#     Data     #
# ------------ #

# Data Usage
# Default: '&eYour current effect requires &b{TYPE} &edata!'
message-data-usage: '&eYour current effect requires &b{TYPE} &edata!'

# No Data Required
# Default: '&eYour current effect does not use any data!'
message-no-data-usage: '&eYour current effect does not use any data!'

# Data Applied
# Default: '&aYour &b{TYPE} &adata has been applied!'
message-data-applied: '&aYour &b{TYPE} &adata has been applied!'

# Invalid Data Arguments
# Default: '&cInvalid &b{TYPE} &cdata arguments!'
message-data-invalid-arguments: '&cInvalid &b{TYPE} &cdata arguments!'

# Unknown Material
# Default: '&cThe {TYPE} name you supplied is invalid!'
message-data-material-unknown: '&cThe &b{TYPE} &cname you supplied is invalid!'

# Mismatched Material
# Default: '&cThe material supplied is not of type &b{TYPE}&c!'
message-data-material-mismatch: '&cThe material supplied is not of type &b{TYPE}&c!'

# Note Data Usage
# You should not change the text here, only the coloring
# Default: '&b/pp data [<0-23>]|[rainbow]'
message-note-data-usage: '&b/pp data [<0-23>]|[rainbow]'

# Color Data Usage
# You should not change the text here, only the coloring
# Default: '&b/pp data [<0-255> <0-255> <0-255>]|[rainbow]'
message-color-data-usage: '&b/pp data [<0-255> <0-255> <0-255>]|[rainbow]'

# Item Data Usage
# You should not change the text here, only the coloring
# Default: '&b/pp data <itemName/ID> <0-15>'
message-item-data-usage: '&b/pp data <itemName> <0-15>'

# Block Data Usage
# You should not change the text here, only the coloring
# Default: '&b/pp data <blockName/ID> <0-15>'
message-block-data-usage: '&b/pp data <blockName> <0-15>'

# ---------------- #
#     Prefixes     #
# ---------------- #

# You Can Use Particles
# Default: '&eYou can use:'
message-use: '&eYou can use:&b'

# Usage
# Default: '&eUsage:'
message-usage: '&eUsage:'

# Available Commands
# Default: '&eAvailable commands: &beffect, effects, style, styles, data, fixed, reset, gui, worlds, version, help'
message-available-commands: '&eAvailable commands: &beffect, effects, style, styles, data, fixed, reset, gui, worlds, version, help'

# Disabled Worlds
# Default: '&eParticles are disabled in these worlds:&b'
message-disabled-worlds: '&eParticles are disabled in these worlds:&b'

# ------------------ #
#   Alt. Execution
# ------------------ #

# Executed For Player
# Default: '&aCommand executed for &b{TYPE}'
message-executed-for-player: '&aCommand executed for &b{TYPE}'

# Failed Execute Not Found
# Default: '&cFailed to execute for &b{TYPE}&c! Player not found!'
message-failed-execute-not-found: '&cFailed to execute command for &b{TYPE}&c! Player not found!'

# Failed Execute No Permission
# Default: '&cFailed to execute for &b{TYPE}&c! You do not have permission!'
message-failed-execute-no-permission: '&cFailed to execute command for &b{TYPE}&c! You do not have permission!'

# ----------------- #
#   Fixed Effects
# ----------------- #

# -- Create -- #

# Missing Creation Arguments
# Default: '&cUnable to create fixed effect, you are missing &b{TYPE} &crequired arguments!'
message-create-fixed-missing-args: '&cUnable to create fixed effect, you are missing &b{TYPE} &crequired arguments!'

# Invalid Coordinates
# Default: '&cUnable to create fixed effect, one or more coordinates you entered is invalid!'
message-create-fixed-invalid-coords: '&cUnable to create fixed effect, one or more coordinates you entered is invalid!'

# Coords Out Of Range
# Default: '&cUnable to create fixed effect, you must be within &b{TYPE}&c blocks of your desired location!'
message-create-fixed-out-of-range: '&cUnable to create fixed effect, you must be within &b{TYPE}&c blocks of your desired location!'

# Invalid Effect
# Default: '&cUnable to create fixed effect, an effect with the name &b{TYPE} &cdoes not exist!'
message-create-fixed-invalid-effect: '&cUnable to create fixed effect, an effect with the name &b{TYPE} &cdoes not exist!'

# No Permission Effect
# Default: '&cUnable to create fixed effect, you do not have permission to use the effect &b{TYPE}&c!'
message-create-fixed-no-permission-effect: '&cUnable to create fixed effect, you do not have permission to use the effect &b{TYPE}&c!'

# Invalid Style
# Default: '&cUnable to create fixed effect, a style with the name &b{TYPE} &cdoes not exist!'
message-create-fixed-invalid-style: '&cUnable to create fixed effect, a style with the name &b{TYPE} &cdoes not exist!'

# No Permission Style
# Default: '&cUnable to create fixed effect, you do not have permission to use the style &b{TYPE}&c!'
message-create-fixed-no-permission-style: '&cUnable to create fixed effect, you do not have permission to use the style &b{TYPE}&c!'

# Style Not Fixable
# Default: '&cThe style &b{TYPE} &cprovided can not be used in fixed effects!'
message-create-fixed-non-fixable-style: '&cThe style &b{TYPE} &ccan not be used in fixed effects!'

# Data Error
# Default: '&cUnable to create fixed effect, the data provided is not correct! This effect requires &b{TYPE}&c!'
message-create-fixed-data-error: '&cUnable to create fixed effect, the data provided is not correct! This effect requires &b{TYPE}&c data!'

# Creation Success
# Default: '&aYour fixed effect has been created!'
message-create-fixed-success: '&aYour fixed effect has been created!'

# -- Remove -- #

# Could Not Remove
# Default: '&cUnable to remove, you do not have a fixed effect with the id of &b{TYPE}&c!'
message-remove-fixed-nonexistant: '&cUnable to remove, you do not have a fixed effect with the id of &b{TYPE}&c!'

# No Args Remove
# Default: '&cYou did not specify an id to remove!'
message-remove-fixed-no-args: '&cYou did not specify an id to remove!'

# Invalid Args Remove
# Default: '&cUnable to remove, the id specified must be a number!'
message-remove-fixed-invalid-args: '&cUnable to remove, the id specified must be a number!'

# Effect Removed
# Default: '&aYour fixed effect with the id &b{TYPE}&a has been removed!'
message-remove-fixed-success: '&aYour fixed effect with the id &b{TYPE}&a has been removed!'

# -- List -- #

# List Success
# Default: '&eYou have fixed effects with these ids: &b'
message-list-fixed-success: '&eYou have fixed effects with these ids: &b'

# List None
# Default: '&eYou do not have any fixed effects!'
message-list-fixed-none: '&eYou do not have any fixed effects!'

# -- Info -- #

# Could Not Get Info
# Default: '&cUnable to get info, you do not have a fixed effect with the id of &b{TYPE}&c!'
message-info-fixed-nonexistant: '&cUnable to get info, you do not have a fixed effect with the id of &b{TYPE}&c!'

# No Args Info
# Default: '&cYou did not specify an id to display info for!'
message-info-fixed-no-args: '&cYou did not specify an id to display info for!'

# Invalid Args Info
# Default: '&cUnable to get info, the id specified must be a number!'
message-info-fixed-invalid-args: '&cUnable to get info, the id specified must be a number!'

# Fixed Effect Info
# Key:
#   {0} = ID
#   {1} = World Name
#   {2} = World X Position
#   {3} = World Y Position
#   {4} = World Z Position
#   {5} = Effect Name
#   {6} = Style Name
#   {7} = Data
# Default: '&eID: &b{0} &eWorld: &b{1} &eX: &b{2} &eY: &b{3} &eZ: &b{4} &eEffect: &b{5} &eStyle: &b{6} &eData: &b{7}'
message-info-fixed-info: '&eID: &b{0} &eWorld: &b{1} &eX: &b{2} &eY: &b{3} &eZ: &b{4} &eEffect: &b{5} &eStyle: &b{6} &eData: &b{7}'

# -- Clear -- #

# No Permission Clear
# Default: '&cYou do not have permission to clear fixed effects of other players!'
message-clear-no-permission: '&cYou do not have permission to clear fixed effects of other players!'

# No Arguments Clear
# Default: '&cYou did not provide a radius to clear fixed effects for!'
message-clear-no-args: '&cYou did not provide a radius to clear fixed effects for!'

# Invalid Arguments Clear
# Default: '&cThe radius you provided is invalid. Make sure it is a positive whole number!'
message-clear-invalid-args: '&cThe radius you provided is invalid. Make sure it is a positive whole number!'

# Successfully Cleared
# Key:
#   {0} = Number of effects cleared
#   {1} = The provided radius
# Default: '&aCleared &b{0} &afixed effects within &b{1} &ablocks of your location!'
message-clear-success: '&aCleared &b{0} &afixed effects within &b{1} &ablocks of your location!'

# -- Other -- #

# No Permission Fixed
# Default: '&cYou do not have permission to use fixed effects!'
message-no-permission-fixed: '&cYou do not have permission to use fixed effects!'

# Reached Max Allowed
# Default: '&cYou have reached the maximum allowed fixed effects!'
message-max-fixed-effects-reached: '&cYou have reached the maximum allowed fixed effects!'

# Invalid Fixed Command
# Default: '&cInvalid sub-command for &b/pp fixed&c!'
message-invalid-fixed-command: '&cInvalid subcommand for &b/pp fixed&c! &eCommands: '

# -- Command Descriptions -- #

# Fixed Command Description For Create
# Default '&e/pp fixed create <x> <y> <z> <effect> <style> [data]'
message-fixed-command-desc-create: '&e/pp fixed create <x> <y> <z> <effect> <style> [data] - Creates a new fixed effect'

# Fixed Command Description For Remove
# Default: '&e/pp fixed remove <id>'
message-fixed-command-desc-remove: '&e/pp fixed remove <id> - Removes a fixed effect by its id'

# Fixed Command Description For List
# Default: '&e/pp fixed list - Lists all ids of your fixed effects'
message-fixed-command-desc-list: '&e/pp fixed list - Lists all ids of your fixed effects'

# Fixed Command Description For Information
# Default: '&e/pp fixed info <id> - Gets info on one of your fixed effects'
message-fixed-command-desc-info: '&e/pp fixed info <id> - Gets info on one of your fixed effects'

# Fixed Command Description For Clear
# Default: '&e/pp fixed clear <radius> - Clears all fixed effects of all players within the given radius'
message-fixed-command-desc-clear: '&e/pp fixed clear <radius> - Clears all fixed effects of all players within the given radius'

# ------------- #
#      GUI      #
# ------------- #

# Disabled
# Default: '&cThe server administrator has disabled the GUI.'
message-gui-disabled: '&cThe server administrator has disabled the GUI.'

# Opened By Default
# Default: '&eWe opened the GUI for you because you did not specify a command. View other commands with &b/pp help&e or use &b/pp gui&e to avoid this message.'
message-gui-by-default: '&eWe opened the GUI for you because you did not specify a command. View other commands with &b/pp help&e or use &b/pp gui&e to avoid this message.'

# Back Button
# Default: '&eGo Back'
message-gui-back-button: '&eGo Back'

# Icon Name Color
# Default: '&a'
message-gui-icon-name-color: '&a'

# Currently Active Effect/Style
# Default: '&d- Your current {TYPE} -'
message-gui-icon-current-active: '&d- Your current {TYPE} -'

# Sets your style/effect to {effect name}
# The effect/style name will be added to the end
# Default: '&eSets your {TYPE} to '
message-gui-icon-sets-to: '&eSets your {TYPE} to &b'

# Select Your 
# Default: '&eSelect your {TYPE}'
message-gui-icon-set-your: '&eSelect your {TYPE}'

# No Access To
# Default: '&cYou have no access to any {TYPE}!'
message-gui-no-access-to: '&cYou have no access to any {TYPE}!'

# No Data
# Default: '&cYour effect does not use any data!'
message-gui-no-data: '&cYour effect does not use any data!'

# ------------- #
#     Other     #
# ------------- #

# Reset
# Default: '&aYour effect, style, and data have all been reset!'
message-reset: '&aYour effect, style, and data have all been reset!'

# Invalid Arguments
# Default: '&cInvalid arguments! &a/pp help'
message-invalid-arguments: '&cInvalid arguments! &b/pp help'

# Disabled Worlds None
# Default: '&eParticles are not disabled in any worlds!'
message-disabled-worlds-none: '&eParticles are not disabled in any worlds!'

# Command Usage
# Default: '&eCommand Usage: /pp <command>'
message-command-usage: '&eCommand Usage: &b/pp <command>'

# ================================================================ #
#                     DATABASE CONFIGURATION                       #
# Information:                                                     #
# * This is meant for people who have multiple servers connected   #
#   together through BungeeCord. Unless you have multiple servers, #
#   it is recommended to keep the database storage disabled for    #
#   the best performance!                                          #
# ================================================================ #

# Enable Database
# Default: false
database-enable: false

# ------------------------------------------------------------------- #
# The following are only required if database-enable is set to 'true' #
# ------------------------------------------------------------------- #

# Database Hostname
# Default: ''
database-hostname: ''

# Database Port
# Default: 3306
database-port: 3306

# Database Name
# Default: ''
database-name: ''

# Database User Name
# Default: ''
database-user-name: ''

# Database User Password
# Default: ''
database-user-password: ''

# =================================================================== #
#                        GUI ICON SETTINGS                            #
# This configuration option allows you to change any of the GUI       #
# icons to whatever block/item you want.                              #
#                                                                     #
# Important Notes:                                                    #
# * If any of the block/item names are invalid the icon in the GUI    #
#   will be the barrier icon to show that it failed to load.          #
# * Do NOT change the particle/style name                             #
# * You MUST use the Spigot-given name for it to work. You can see    #
#   all the Spigot-given names at the link below:                     #
#   https://hub.spigotmc.org/javadocs/spigot/org/bukkit/Material.html #
# =================================================================== #

gui-icon: 
  main-menu:
    EFFECT: BLAZE_POWDER
    STYLE: NETHER_STAR
    DATA: BOOK
  effect: 
    NONE: THIN_GLASS
    EXPLODE: SULPHUR
    LARGE_EXPLODE: FIREBALL
    HUGE_EXPLOSION: TNT
    FIREWORKS_SPARK: FIREWORK
    BUBBLE: GLASS
    WAKE: BOAT
    SUSPENDED: WATER_LILY
    DEPTH_SUSPEND: BEDROCK
    CRIT: IRON_SWORD
    MAGIC_CRIT: NETHER_STALK
    SMOKE: TORCH
    LARGE_SMOKE: WEB
    SPELL: GLASS_BOTTLE
    INSTANT_SPELL: POTION
    MOB_SPELL: GLOWSTONE_DUST
    MOB_SPELL_AMBIENT: BEACON
    WITCH_MAGIC: CAULDRON_ITEM
    DRIP_WATER: WATER_BUCKET
    DRIP_LAVA: LAVA_BUCKET
    ANGRY_VILLAGER: IRON_DOOR
    HAPPY_VILLAGER: WOOD_DOOR
    NOTE: NOTE_BLOCK
    PORTAL: OBSIDIAN
    ENCHANTMENT_TABLE: ENCHANTMENT_TABLE
    FLAME: BLAZE_POWDER
    LAVA: MAGMA_CREAM
    FOOTSTEP: GRASS
    CLOUD: WOOL
    RED_DUST: REDSTONE
    SNOWBALL_POOF: SNOW_BALL
    SNOW_SHOVEL: SNOW
    SLIME: SLIME_BALL
    HEART: RED_ROSE
    BARRIER: BARRIER
    ITEM_CRACK: STICK
    BLOCK_CRACK: DEAD_BUSH
    BLOCK_DUST: SOUL_SAND
    DROPLET: LAPIS_ORE
    DRAGON_BREATH: DRAGONS_BREATH
    END_ROD: END_ROD
    DAMAGE_INDICATOR: BOW
    SWEEP_ATTACK: GOLD_SWORD
    FALLING_DUST: SAND
    TOTEM: TOTEM
    SPIT: PUMPKIN_SEEDS
  style: 
    NONE: THIN_GLASS
    BEAM: POWERED_RAIL
    HALO: ENDER_PORTAL_FRAME
    POINT: STONE_BUTTON
    MOVE: PISTON_BASE
    SPIN: BEACON
    QUADHELIX: ACTIVATOR_RAIL
    ORBIT: ENCHANTMENT_TABLE
    FEET: GRASS
    CUBE: STONE
    ARROWS: BOW
    SPIRAL: HOPPER
    THICK: VINE
    WINGS: FIREWORK
    SPHERE: SNOW_BALL
    SWORDS: IRON_SWORD
    HURT: CACTUS
    BLOCKPLACE: WOOD
    BLOCKBREAK: IRON_PICKAXE
    BLOCKEDIT: DISPENSER


# That's everything! You reached the end of the configuration.
# Enjoy the plugin!