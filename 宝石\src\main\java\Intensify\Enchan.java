/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.enchantments.Enchantment
 */
package Intensify;

import java.util.HashMap;
import org.bukkit.enchantments.Enchantment;

public class Enchan {
    private HashMap<Integer, Enchantment> enchan = new HashMap();

    public Enchan() {
        this.enchan.put(0, Enchantment.getById((int)0));
        this.enchan.put(1, Enchantment.getById((int)1));
        this.enchan.put(2, Enchantment.getById((int)2));
        this.enchan.put(3, Enchantment.getById((int)3));
        this.enchan.put(4, Enchantment.getById((int)4));
        this.enchan.put(5, Enchantment.getById((int)5));
        this.enchan.put(6, Enchantment.getById((int)6));
        this.enchan.put(7, Enchantment.getById((int)7));
        this.enchan.put(16, Enchantment.getById((int)16));
        this.enchan.put(17, Enchantment.getById((int)17));
        this.enchan.put(18, Enchantment.getById((int)18));
        this.enchan.put(19, Enchantment.getById((int)19));
        this.enchan.put(20, Enchantment.getById((int)20));
        this.enchan.put(21, Enchantment.getById((int)21));
        this.enchan.put(32, Enchantment.getById((int)32));
        this.enchan.put(33, Enchantment.getById((int)33));
        this.enchan.put(34, Enchantment.getById((int)34));
        this.enchan.put(35, Enchantment.getById((int)35));
        this.enchan.put(48, Enchantment.getById((int)48));
        this.enchan.put(49, Enchantment.getById((int)49));
        this.enchan.put(50, Enchantment.getById((int)50));
        this.enchan.put(51, Enchantment.getById((int)51));
    }

    public Enchantment getEnchan(int id) {
        return this.enchan.get(id);
    }
}

