package com.example.gemenhancer.commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import com.example.gemenhancer.GemEnhancer;

public class Gem<PERSON>ommand implements CommandExecutor {

    private final GemEnhancer plugin;

    public GemCommand(GemEnhancer plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (args.length == 0) {
            showHelp(sender);
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "reload":
                return handleReloadCommand(sender);

            case "give":
                return handleGiveCommand(sender, label, args);

            case "info":
                return handleInfoCommand(sender);

            case "style":
                return handleStyleCommand(sender);

            case "help":
                showHelp(sender);
                return true;

            default:
                showHelp(sender);
                return true;
        }
    }

    /**
     * 处理重新加载命令
     *
     * @param sender 命令发送者
     * @return 命令执行结果
     */
    private boolean handleReloadCommand(CommandSender sender) {
        if (!sender.hasPermission("gemenhancer.reload")) {
            plugin.sendMessage(sender, "&c你没有权限执行此命令！");
            return true;
        }

        plugin.reloadPlugin();
        plugin.sendMessage(sender, plugin.getConfig().getString("messages.reload", "&a配置文件已重新加载！"));
        return true;
    }

    /**
     * 处理给予宝石命令
     *
     * @param sender 命令发送者
     * @param label  命令标签
     * @param args   命令参数
     * @return 命令执行结果
     */
    private boolean handleGiveCommand(CommandSender sender, String label, String[] args) {
        if (!sender.hasPermission("gemenhancer.give")) {
            plugin.sendMessage(sender, "&c你没有权限执行此命令！");
            return true;
        }

        if (args.length < 2) {
            plugin.sendMessage(sender, "&c用法: /" + label + " give <玩家名> [数量]");
            return true;
        }

        Player target = plugin.getServer().getPlayer(args[1]);
        if (target == null) {
            plugin.sendMessage(sender, "&c找不到玩家: " + args[1]);
            return true;
        }

        int amount = 1;
        if (args.length >= 3) {
            try {
                amount = Integer.parseInt(args[2]);
                if (amount <= 0)
                    amount = 1;
                if (amount > 64)
                    amount = 64; // 限制最大数量为64
            } catch (NumberFormatException e) {
                plugin.sendMessage(sender, "&c无效的数量: " + args[2]);
                return true;
            }
        }

        plugin.getGemManager().giveGem(target, amount);
        plugin.sendMessage(sender, "&a已给予 " + target.getName() + " " + amount + " 个强化宝石！");
        plugin.sendMessage(target, "&a你收到了 " + amount + " 个强化宝石！");
        return true;
    }

    /**
     * 处理插件信息命令
     *
     * @param sender 命令发送者
     * @return 命令执行结果
     */
    private boolean handleInfoCommand(CommandSender sender) {
        plugin.sendMessage(sender, "&6=== GemEnhancer 插件信息 ===");
        plugin.sendMessage(sender, "&e版本: &f" + plugin.getDescription().getVersion());
        plugin.sendMessage(sender, "&e作者: &f" + String.join(", ", plugin.getDescription().getAuthors()));
        plugin.sendMessage(sender, "&e宝石强化成功率: &f" + plugin.getGemManager().getSuccessRate() + "%");
        return true;
    }

    /**
     * 处理强化风格命令
     *
     * @param sender 命令发送者
     * @return 命令执行结果
     */
    private boolean handleStyleCommand(CommandSender sender) {
        if (!sender.hasPermission("gemenhancer.style")) {
            plugin.sendMessage(sender, "&c你没有权限执行此命令！");
            return true;
        }

        plugin.sendMessage(sender, "&6=== GemEnhancer 强化风格 ===");
        plugin.sendMessage(sender, "&e强化颜色: &f" + plugin.getConfig().getString("style.color", "&c"));
        plugin.sendMessage(sender, "&e有等级符号: &f" + plugin.getConfig().getString("style.with_level", "§c§l◆"));
        plugin.sendMessage(sender, "&e无等级符号: &f" + plugin.getConfig().getString("style.without_level", "§7§l◇"));
        plugin.sendMessage(sender, "&e最大强化等级: &f" + plugin.getConfig().getInt("style.max_level", 10));
        plugin.sendMessage(sender, "&e强化名称前缀: &f" + plugin.getConfig().getString("style.prefix", "强化"));

        // 显示强化符号示例
        String withLevel = plugin.getConfig().getString("style.with_level", "§c§l◆");
        String withoutLevel = plugin.getConfig().getString("style.without_level", "§7§l◇");
        int maxLevel = plugin.getConfig().getInt("style.max_level", 10);

        plugin.sendMessage(sender, "&e强化符号示例:");
        for (int i = 0; i <= maxLevel; i++) {
            StringBuilder symbols = new StringBuilder();
            for (int j = 0; j < i; j++) {
                symbols.append(withLevel);
            }
            for (int j = i; j < maxLevel; j++) {
                symbols.append(withoutLevel);
            }
            plugin.sendMessage(sender, "&e等级 " + i + ": &f" + symbols.toString());
        }

        return true;
    }

    /**
     * 显示帮助信息
     *
     * @param sender 命令发送者
     */
    private void showHelp(CommandSender sender) {
        plugin.sendMessage(sender, "&6=== GemEnhancer 帮助 ===");
        plugin.sendMessage(sender, "&e/gem help &7- 显示此帮助信息");
        plugin.sendMessage(sender, "&e/gem info &7- 显示插件信息");

        if (sender.hasPermission("gemenhancer.style")) {
            plugin.sendMessage(sender, "&e/gem style &7- 显示强化风格设置");
        }

        if (sender.hasPermission("gemenhancer.reload")) {
            plugin.sendMessage(sender, "&e/gem reload &7- 重新加载配置文件");
        }

        if (sender.hasPermission("gemenhancer.give")) {
            plugin.sendMessage(sender, "&e/gem give <玩家名> [数量] &7- 给予玩家强化宝石");
        }
    }
}
