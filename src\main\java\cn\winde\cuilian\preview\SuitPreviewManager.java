package cn.winde.cuilian.preview;

import cn.winde.cuilian.Cuilian;
import cn.winde.cuilian.lizi.effectlisten;
import cn.winde.cuilian.suit.SuitManager;
import cn.winde.cuilian.tps.TPSMonitor;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.HashMap;
import java.util.Map;

/**
 * 套装预览管理器
 * 负责管理套装特效的临时预览功能
 */
public class SuitPreviewManager {

    // 存储正在预览的玩家信息
    private static final Map<String, PreviewInfo> previewingPlayers = new HashMap<>();

    // 存储玩家预览冷却时间
    private static final Map<String, Long> previewCooldowns = new HashMap<>();

    /**
     * 预览信息类
     */
    private static class PreviewInfo {
        public final String suitName;
        public final BukkitTask task;
        public final long startTime;
        public final int duration;
        public final Integer originalEffect; // 保存玩家原有的特效状态

        public PreviewInfo(String suitName, BukkitTask task, int duration, Integer originalEffect) {
            this.suitName = suitName;
            this.task = task;
            this.startTime = System.currentTimeMillis();
            this.duration = duration;
            this.originalEffect = originalEffect;
        }
    }

    /**
     * 开始预览套装特效
     *
     * @param player   玩家
     * @param suitName 套装名称
     * @return 是否成功开始预览
     */
    public static boolean startPreview(Player player, String suitName) {
        String playerName = player.getName();

        // 检查是否已经在预览中
        if (isPlayerPreviewing(playerName)) {
            player.sendMessage("§c§l您正在预览其他套装特效，请等待结束后再试");
            return false;
        }

        // 检查预览冷却时间
        int cooldownSeconds = Cuilian.config.getInt("suit_preview.cooldown", 60);
        if (cooldownSeconds > 0) { // 只有当冷却时间大于0时才检查冷却
            long currentTime = System.currentTimeMillis();
            Long lastPreviewTime = previewCooldowns.get(playerName);
            if (lastPreviewTime != null) {
                long cooldownMillis = cooldownSeconds * 1000L;
                long timeSinceLastPreview = currentTime - lastPreviewTime;

                if (timeSinceLastPreview < cooldownMillis) {
                    long remainingCooldown = (cooldownMillis - timeSinceLastPreview) / 1000;
                    player.sendMessage("§c§l预览功能冷却中，请等待 " + remainingCooldown + " 秒后再试");
                    return false;
                }
            }
        }

        // 检查全局特效开关
        if (!Cuilian.config.getBoolean("effects", true)) {
            player.sendMessage("§c§l服务器已禁用特效显示，无法预览套装特效");
            return false;
        }

        // 检查TPS自动特效开关状态
        if (TPSMonitor.isEffectDisabledByTPS()) {
            player.sendMessage("§c§l当前服务器TPS过低，特效已被自动禁用，无法预览");
            return false;
        }

        // 检查玩家个人特效开关
        if (!SuitManager.isPlayerEffectEnabled(playerName)) {
            player.sendMessage("§c§l您的个人特效已关闭，请先开启特效后再预览");
            player.sendMessage("§7§l使用 §e/cuilian effect on §7开启个人特效");
            return false;
        }

        // 获取套装属性
        SuitManager.SuitAttribute attribute = SuitManager.getSuitAttribute(suitName);
        if (attribute == null) {
            player.sendMessage("§c§l套装 " + suitName + " 不存在或配置错误");
            return false;
        }

        // 获取预览时间
        int duration = Cuilian.config.getInt("suit_preview.duration", 30);

        // 保存玩家原有的特效状态
        Integer originalEffect = Cuilian.lizi.get(playerName);

        // 先取消玩家原有的特效（如果有的话）
        if (originalEffect != null) {
            Cuilian.lizi.remove(playerName);
            player.sendMessage("§7§l已暂停当前特效以进行预览");
        }

        // 设置预览特效（使用特殊标记 -2 表示预览模式）
        Cuilian.lizi.put(playerName, -2);

        // 创建预览任务
        BukkitTask previewTask = new BukkitRunnable() {
            private int tickCount = 0;
            private final int maxTicks = duration * 20; // 转换为tick

            @Override
            public void run() {
                // 检查玩家是否在线
                if (!player.isOnline()) {
                    stopPreview(playerName);
                    return;
                }

                // 检查是否超时
                if (tickCount >= maxTicks) {
                    stopPreview(playerName);
                    return;
                }

                // 检查全局特效开关（实时检查）
                if (!Cuilian.config.getBoolean("effects", true)) {
                    stopPreview(playerName);
                    player.sendMessage("§c§l服务器特效已被禁用，预览已停止");
                    return;
                }

                // 检查TPS状态（实时检查）
                if (TPSMonitor.isEffectDisabledByTPS()) {
                    stopPreview(playerName);
                    player.sendMessage("§c§l服务器TPS过低，预览已停止");
                    return;
                }

                // 显示预览特效
                effectlisten.effectSuit(player, attribute);

                tickCount++;
            }
        }.runTaskTimer(Cuilian.getInstance(), 0L, 1L); // 每tick执行一次

        // 记录预览信息
        previewingPlayers.put(playerName, new PreviewInfo(suitName, previewTask, duration, originalEffect));

        // 记录预览开始时间用于冷却计算
        previewCooldowns.put(playerName, System.currentTimeMillis());

        // 发送开始预览消息
        player.sendMessage("§a§l开始预览套装: §e" + suitName);
        player.sendMessage("§7§l预览时间: §e" + duration + "秒");
        if (cooldownSeconds > 0) {
            player.sendMessage("§7§l预览冷却: §e" + cooldownSeconds + "秒");
        } else {
            player.sendMessage("§7§l预览冷却: §a无冷却");
        }
        player.sendMessage("§7§l预览期间将持续显示套装特效");

        // 安排结束提醒任务
        Bukkit.getScheduler().runTaskLater(Cuilian.getInstance(), () -> {
            if (isPlayerPreviewing(playerName)) {
                player.sendMessage("§e§l套装预览即将结束...");
            }
        }, (duration - 5) * 20L); // 结束前5秒提醒

        return true;
    }

    /**
     * 停止预览套装特效
     *
     * @param playerName 玩家名称
     */
    public static void stopPreview(String playerName) {
        PreviewInfo info = previewingPlayers.remove(playerName);
        if (info != null) {
            // 取消预览任务
            if (info.task != null) {
                info.task.cancel();
            }

            // 移除预览特效
            Cuilian.lizi.remove(playerName);

            // 恢复玩家原有的特效状态
            if (info.originalEffect != null) {
                Cuilian.lizi.put(playerName, info.originalEffect);
            }

            // 发送结束消息
            Player player = Bukkit.getPlayer(playerName);
            if (player != null && player.isOnline()) {
                long previewTime = (System.currentTimeMillis() - info.startTime) / 1000;
                int cooldownSeconds = Cuilian.config.getInt("suit_preview.cooldown", 60);

                player.sendMessage("§a§l套装预览已结束");
                player.sendMessage("§7§l预览时间: §e" + previewTime + "秒");
                player.sendMessage("§7§l套装: §e" + info.suitName);

                if (cooldownSeconds > 0) {
                    player.sendMessage("§c§l预览冷却: §e" + cooldownSeconds + "秒 §7(需等待后才能再次预览)");
                } else {
                    player.sendMessage("§a§l预览冷却: §2无冷却 §7(可立即再次预览)");
                }

                // 提示特效恢复状态
                if (info.originalEffect != null) {
                    if (info.originalEffect == -1) {
                        player.sendMessage("§a§l已恢复您的套装特效");
                    } else {
                        player.sendMessage("§a§l已恢复您的" + info.originalEffect + "星淬炼特效");
                    }
                } else {
                    player.sendMessage("§7§l您之前没有激活的特效");
                }
            }
        }
    }

    /**
     * 检查玩家是否正在预览
     *
     * @param playerName 玩家名称
     * @return 是否正在预览
     */
    public static boolean isPlayerPreviewing(String playerName) {
        return previewingPlayers.containsKey(playerName);
    }

    /**
     * 获取玩家预览信息
     *
     * @param playerName 玩家名称
     * @return 预览信息，如果不在预览则返回null
     */
    public static String getPreviewInfo(String playerName) {
        PreviewInfo info = previewingPlayers.get(playerName);
        if (info != null) {
            long elapsed = (System.currentTimeMillis() - info.startTime) / 1000;
            long remaining = info.duration - elapsed;
            return String.format("正在预览: %s (剩余 %d 秒)", info.suitName, Math.max(0, remaining));
        }
        return null;
    }

    /**
     * 玩家离线时清理预览状态
     *
     * @param playerName 玩家名称
     */
    public static void cleanupPlayerPreview(String playerName) {
        stopPreview(playerName);
        // 注意：不清理冷却时间，让玩家重新上线后仍需等待冷却
    }

    /**
     * 检查玩家是否在冷却中
     *
     * @param playerName 玩家名称
     * @return 是否在冷却中
     */
    public static boolean isPlayerInCooldown(String playerName) {
        int cooldownSeconds = Cuilian.config.getInt("suit_preview.cooldown", 60);
        if (cooldownSeconds <= 0) {
            return false; // 无冷却时间
        }

        Long lastPreviewTime = previewCooldowns.get(playerName);
        if (lastPreviewTime == null) {
            return false;
        }

        long cooldownMillis = cooldownSeconds * 1000L;
        long timeSinceLastPreview = System.currentTimeMillis() - lastPreviewTime;

        return timeSinceLastPreview < cooldownMillis;
    }

    /**
     * 获取玩家剩余冷却时间（秒）
     *
     * @param playerName 玩家名称
     * @return 剩余冷却时间，如果不在冷却中则返回0
     */
    public static long getRemainingCooldown(String playerName) {
        int cooldownSeconds = Cuilian.config.getInt("suit_preview.cooldown", 60);
        if (cooldownSeconds <= 0) {
            return 0; // 无冷却时间
        }

        Long lastPreviewTime = previewCooldowns.get(playerName);
        if (lastPreviewTime == null) {
            return 0;
        }

        long cooldownMillis = cooldownSeconds * 1000L;
        long timeSinceLastPreview = System.currentTimeMillis() - lastPreviewTime;

        if (timeSinceLastPreview >= cooldownMillis) {
            return 0;
        }

        return (cooldownMillis - timeSinceLastPreview) / 1000;
    }

    /**
     * 清理过期的冷却记录（可选的清理方法）
     */
    public static void cleanupExpiredCooldowns() {
        long currentTime = System.currentTimeMillis();
        int cooldownSeconds = Cuilian.config.getInt("suit_preview.cooldown", 60);
        long cooldownMillis = cooldownSeconds * 1000L;

        previewCooldowns.entrySet().removeIf(entry -> {
            long timeSinceLastPreview = currentTime - entry.getValue();
            return timeSinceLastPreview >= cooldownMillis;
        });
    }

    /**
     * 获取所有正在预览的玩家数量
     *
     * @return 预览玩家数量
     */
    public static int getPreviewingPlayerCount() {
        return previewingPlayers.size();
    }

    /**
     * 清理所有预览状态（插件卸载时使用）
     */
    public static void cleanupAll() {
        for (String playerName : previewingPlayers.keySet()) {
            stopPreview(playerName);
        }
        previewingPlayers.clear();
    }
}
