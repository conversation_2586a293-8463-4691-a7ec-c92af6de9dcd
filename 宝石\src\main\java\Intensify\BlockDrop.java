/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.block.Block
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.block.BlockBreakEvent
 *  org.bukkit.inventory.ItemStack
 */
package Intensify;

import Intensify.Intensify;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.inventory.ItemStack;

public class BlockDrop
        implements Listener {
    private Intensify i;

    public BlockDrop(Intensify i) {
        this.i = i;
    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent e) {
        if (!this.i.config.getBoolean("drop.block")) {
            return;
        }
        Block block = e.getBlock();
        Player p = e.getPlayer();
        int id = -1;
        int i = 0;
        while (i < Intensify.getBlockId().size()) {
            int a = Integer.parseInt(Intensify.getBlockId().get(i).split(" ")[0]);
            if (a == block.getTypeId()) {
                id = i;
                break;
            }
            ++i;
        }
        if (id != -1) {
            ItemStack item = null;
            int key = this.i.rm.nextInt(100);
            String type = Intensify.getBlockId().get(id).split(" ")[1].toLowerCase();
            int chance = Integer.parseInt(Intensify.getBlockId().get(id).split(" ")[2]);
            switch (type.hashCode()) {
                case -1039745817: {
                    item = this.i.normalItem;
                    break;
                }
                case 3333041: {
                    item = this.i.luckItem;
                    break;
                }
                case 3522445: {
                    item = this.i.safeItem;
                    break;
                }
                case 116765: {
                    item = this.i.vipItem;
                }
            }
            if (key < chance) {
                this.i.show(p, this.i.get(8).replace("{0}", item.getItemMeta().getDisplayName()));
                block.getWorld().dropItem(block.getLocation(), item);
            }
        }
    }
}
