package cn.winde.cuilian.tps;

import cn.winde.cuilian.Cuilian;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

/**
 * TPS监控器 - 实时自动管理特效开关
 * 使用tick时间计算来监控服务器性能
 */
public class TPSMonitor extends BukkitRunnable {

    private static TPSMonitor instance;
    private boolean lastEffectState = true; // 上次的特效状态
    private boolean tpsEffectDisabled = false; // 是否因为TPS过低而禁用特效
    private long lastCheckTime = 0;
    private double lastTPS = 20.0;

    // TPS计算相关
    private long lastTickTime = System.currentTimeMillis();
    private double currentTPS = 20.0;

    // 恢复延迟相关
    private long tpsRecoveryStartTime = 0; // TPS开始恢复的时间
    private boolean isWaitingForRecovery = false; // 是否正在等待恢复延迟

    public static void startMonitoring() {
        if (instance != null) {
            instance.cancel();
        }

        instance = new TPSMonitor();
        // 每秒检查一次TPS，实现实时监控
        instance.runTaskTimer(Cuilian.getInstance(), 20L, 20L);

        System.out.println("TPS实时监控器已启动");
    }

    public static void stopMonitoring() {
        if (instance != null) {
            instance.cancel();
            instance = null;
            System.out.println("TPS监控器已停止");
        }
    }

    @Override
    public void run() {
        try {
            // 检查是否启用TPS自动特效开关
            boolean tpsAutoEnabled = Cuilian.config.getBoolean("tps_auto_effect.enabled", false);
            if (!tpsAutoEnabled) {
                // 如果TPS自动开关被禁用，但之前因为TPS禁用了特效，需要恢复
                if (tpsEffectDisabled) {
                    restoreEffects();
                }
                return;
            }

            // 获取当前TPS
            double currentTPS = getCurrentTPS();
            lastTPS = currentTPS;
            lastCheckTime = System.currentTimeMillis();

            // 获取TPS阈值
            double threshold = Cuilian.config.getDouble("tps_auto_effect.threshold", 15.0);

            // 获取当前特效状态
            boolean currentEffectState = Cuilian.config.getBoolean("effects", true);

            if (currentTPS < threshold) {
                // TPS过低，需要禁用特效
                if (currentEffectState && !tpsEffectDisabled) {
                    disableEffectsDueToLowTPS(currentTPS, threshold);
                    // 重置恢复等待状态
                    isWaitingForRecovery = false;
                    tpsRecoveryStartTime = 0;
                }
            } else {
                // TPS正常，如果之前因为TPS禁用了特效，现在考虑恢复
                if (tpsEffectDisabled) {
                    handleTPSRecovery(currentTPS, threshold);
                }
            }

        } catch (Exception e) {
            System.err.println("TPS监控器运行时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取当前服务器TPS
     * 使用tick时间差来计算TPS
     */
    private double getCurrentTPS() {
        try {
            long currentTime = System.currentTimeMillis();
            long timeDiff = currentTime - lastTickTime;
            lastTickTime = currentTime;

            // 计算TPS：理想情况下每tick应该是50ms
            // TPS = 1000ms / (实际tick时间ms) * 20ticks
            if (timeDiff > 0) {
                currentTPS = Math.min(1000.0 / timeDiff * 20.0, 20.0);
            } else {
                currentTPS = 20.0; // 如果时间差为0，说明性能很好
            }

            return currentTPS;
        } catch (Exception e) {
            System.err.println("计算TPS时出现错误: " + e.getMessage());
            return 20.0; // 默认返回满TPS
        }
    }

    /**
     * 处理TPS恢复逻辑（带延迟）
     */
    private void handleTPSRecovery(double currentTPS, double threshold) {
        try {
            long currentTime = System.currentTimeMillis();
            int recoveryDelay = Cuilian.config.getInt("tps_auto_effect.recovery_delay", 10);

            if (!isWaitingForRecovery) {
                // 开始等待恢复延迟
                isWaitingForRecovery = true;
                tpsRecoveryStartTime = currentTime;
                System.out.println(String.format("TPS已恢复正常(%.2f >= %.2f)，等待%d秒后重新启用特效",
                        currentTPS, threshold, recoveryDelay));
                return;
            }

            // 检查是否已经等待足够的时间
            long waitedTime = (currentTime - tpsRecoveryStartTime) / 1000;
            if (waitedTime >= recoveryDelay) {
                // 延迟时间已到，恢复特效
                enableEffectsDueToNormalTPS(currentTPS, threshold);

                // 重置等待状态
                isWaitingForRecovery = false;
                tpsRecoveryStartTime = 0;
            }

        } catch (Exception e) {
            System.err.println("处理TPS恢复时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 因为TPS过低而禁用特效
     */
    private void disableEffectsDueToLowTPS(double currentTPS, double threshold) {
        try {
            // 保存当前特效状态
            lastEffectState = Cuilian.config.getBoolean("effects", true);

            // 禁用特效
            Cuilian instance = Cuilian.getInstance();
            if (instance != null) {
                instance.getConfig().set("effects", false);
                instance.saveConfig();
                Cuilian.config.set("effects", false);
            }

            // 清空所有玩家特效
            Cuilian.lizi.clear();

            // 标记为因TPS禁用
            tpsEffectDisabled = true;

            // 发送全服公告
            sendLowTPSAnnouncement(currentTPS, threshold);

            System.out.println(String.format("因TPS过低(%.2f < %.2f)自动禁用特效", currentTPS, threshold));

        } catch (Exception e) {
            System.err.println("禁用特效时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 因为TPS恢复正常而启用特效
     */
    private void enableEffectsDueToNormalTPS(double currentTPS, double threshold) {
        try {
            // 恢复特效状态
            Cuilian instance = Cuilian.getInstance();
            if (instance != null) {
                instance.getConfig().set("effects", lastEffectState);
                instance.saveConfig();
                Cuilian.config.set("effects", lastEffectState);
            }

            // 取消TPS禁用标记
            tpsEffectDisabled = false;

            // 只有在原来特效是启用状态时才发送恢复公告
            if (lastEffectState) {
                sendNormalTPSAnnouncement(currentTPS, threshold);
            }

            System.out.println(String.format("因TPS恢复正常(%.2f >= %.2f)自动恢复特效状态", currentTPS, threshold));

        } catch (Exception e) {
            System.err.println("恢复特效时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 恢复特效（当TPS自动开关被禁用时）
     */
    private void restoreEffects() {
        if (tpsEffectDisabled) {
            try {
                Cuilian instance = Cuilian.getInstance();
                if (instance != null) {
                    instance.getConfig().set("effects", lastEffectState);
                    instance.saveConfig();
                    Cuilian.config.set("effects", lastEffectState);
                }

                tpsEffectDisabled = false;
                System.out.println("TPS自动开关已禁用，恢复特效状态");

            } catch (Exception e) {
                System.err.println("恢复特效状态时出现错误: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 发送TPS过低的公告
     */
    private void sendLowTPSAnnouncement(double currentTPS, double threshold) {
        try {
            Bukkit.broadcastMessage("§6§l===========================================");
            Bukkit.broadcastMessage("§c§l[系统] 服务器TPS过低，自动禁用特效！");
            Bukkit.broadcastMessage(String.format("§e§l[TPS] 当前TPS: %.2f | 阈值: %.2f", currentTPS, threshold));
            Bukkit.broadcastMessage("§7§l[说明] 为保证服务器流畅运行，已暂时关闭特效");
            Bukkit.broadcastMessage("§7§l[提示] TPS恢复正常后将自动重新启用特效");
            Bukkit.broadcastMessage("§6§l===========================================");

            // 发送标题提示
            for (Player player : Bukkit.getOnlinePlayers()) {
                try {
                    player.sendTitle("§c§l特效已禁用", "§7TPS过低，自动关闭特效");
                } catch (Exception titleEx) {
                    // 忽略标题发送错误
                }
            }

        } catch (Exception e) {
            System.err.println("发送TPS过低公告时出现错误: " + e.getMessage());
        }
    }

    /**
     * 发送TPS恢复正常的公告
     */
    private void sendNormalTPSAnnouncement(double currentTPS, double threshold) {
        try {
            Bukkit.broadcastMessage("§6§l===========================================");
            Bukkit.broadcastMessage("§a§l[系统] 服务器TPS已恢复，自动启用特效！");
            Bukkit.broadcastMessage(String.format("§e§l[TPS] 当前TPS: %.2f | 阈值: %.2f", currentTPS, threshold));
            Bukkit.broadcastMessage("§7§l[说明] 服务器运行已恢复正常，特效功能重新开启");
            Bukkit.broadcastMessage("§c§l[注意] 需要重新穿戴装备才能激活特效");
            Bukkit.broadcastMessage("§6§l===========================================");

            // 发送标题提示
            for (Player player : Bukkit.getOnlinePlayers()) {
                try {
                    player.sendTitle("§a§l特效已启用", "§eTPS恢复，重新穿戴装备激活特效");
                } catch (Exception titleEx) {
                    // 忽略标题发送错误
                }
            }

        } catch (Exception e) {
            System.err.println("发送TPS恢复公告时出现错误: " + e.getMessage());
        }
    }

    /**
     * 获取当前TPS状态信息
     */
    public static String getTPSStatus() {
        if (instance != null) {
            return String.format("TPS: %.2f | 上次检查: %d秒前 | TPS自动开关: %s",
                    instance.lastTPS,
                    (System.currentTimeMillis() - instance.lastCheckTime) / 1000,
                    instance.tpsEffectDisabled ? "已触发" : "正常");
        }
        return "TPS监控器未运行";
    }

    /**
     * 检查是否因为TPS而禁用了特效
     */
    public static boolean isEffectDisabledByTPS() {
        return instance != null && instance.tpsEffectDisabled;
    }
}
