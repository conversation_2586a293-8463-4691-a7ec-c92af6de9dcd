/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.event.Listener
 */
package cn.winde.cuilian;

import org.bukkit.event.Listener;

public class Playermove
implements Listener {
    public static int getWordCount(String s) {
        int length = 0;
        int i = 0;
        while (i < s.length()) {
            int ascii = Character.codePointAt(s, i);
            length = ascii >= 0 && ascii <= 255 ? ++length : (length += 2);
            ++i;
        }
        return length;
    }
}

