package cn.winde.cuilian.gui;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import cn.winde.cuilian.Cuilian;
import cn.winde.cuilian.suit.SuitManager;
import cn.winde.cuilian.util.SuitMessageManager;
import cn.winde.cuilian.preview.SuitPreviewManager;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 游戏内GUI界面管理类
 * 提供玩家可以直接在游戏中使用的GUI界面
 */
public class InGameGUI {

    /**
     * 打开装备管理界面
     *
     * @param player 玩家
     */
    public static void openEquipmentGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, "§6§l装备管理界面");

        // 显示当前装备
        ItemStack helmet = player.getInventory().getHelmet();
        ItemStack chestplate = player.getInventory().getChestplate();
        ItemStack leggings = player.getInventory().getLeggings();
        ItemStack boots = player.getInventory().getBoots();
        ItemStack weapon = player.getInventory().getItemInHand();

        // 设置装备显示位置
        if (helmet != null)
            gui.setItem(10, createEquipmentDisplay(helmet, "头盔"));
        if (chestplate != null)
            gui.setItem(19, createEquipmentDisplay(chestplate, "胸甲"));
        if (leggings != null)
            gui.setItem(28, createEquipmentDisplay(leggings, "护腿"));
        if (boots != null)
            gui.setItem(37, createEquipmentDisplay(boots, "靴子"));
        if (weapon != null)
            gui.setItem(22, createEquipmentDisplay(weapon, "武器"));

        // 显示套装信息
        ItemStack suitInfo = createSuitInfoDisplay(player);
        gui.setItem(13, suitInfo);

        // 添加功能按钮
        gui.setItem(45, createButton(Material.EMERALD, "§a§l特效设置", "§7点击管理特效开关"));
        gui.setItem(46, createButton(Material.DIAMOND, "§b§l套装预览", "§7点击预览可用套装"));
        gui.setItem(47, createButton(Material.GOLD_INGOT, "§e§l淬炼信息", "§7查看淬炼相关信息"));

        player.openInventory(gui);
    }

    /**
     * 打开套装预览界面
     *
     * @param player 玩家
     */
    public static void openSuitPreviewGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, "§6§l套装预览界面");

        java.util.Set<String> allSuitsSet = SuitManager.getAllSuits();
        java.util.List<String> allSuits = new java.util.ArrayList<>(allSuitsSet);
        int slot = 0;

        for (String suitName : allSuits) {
            if (slot >= 45)
                break; // 最多显示45个套装

            ItemStack suitDisplay = createSuitPreviewItem(suitName);
            gui.setItem(slot, suitDisplay);
            slot++;
        }

        // 添加返回按钮
        gui.setItem(49, createButton(Material.ARROW, "§c§l返回", "§7返回装备管理界面"));

        player.openInventory(gui);
    }

    /**
     * 打开特效设置界面
     *
     * @param player 玩家
     */
    public static void openEffectSettingsGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§6§l特效设置界面");

        boolean effectEnabled = SuitManager.isPlayerEffectEnabled(player.getName());

        // 特效开关
        ItemStack effectToggle = createButton(
                effectEnabled ? Material.EMERALD_BLOCK : Material.REDSTONE_BLOCK,
                effectEnabled ? "§a§l特效已开启" : "§c§l特效已关闭",
                "§7点击切换特效状态");
        gui.setItem(13, effectToggle);

        // 当前特效信息 - 烟花物品始终显示
        int currentLevel = Cuilian.lizi.getOrDefault(player.getName(), 0);
        ItemStack currentEffect;

        if (currentLevel == -1) {
            // 命名套装特效
            String suitName = SuitManager.getPlayerSuit(player.getName());
            if (suitName != null) {
                currentEffect = createButton(
                        Material.FIREWORK,
                        "§d§l当前特效: " + suitName,
                        "§7当前激活的命名套装特效");
            } else {
                currentEffect = createButton(
                        Material.FIREWORK,
                        "§d§l当前特效: 命名套装",
                        "§7当前激活的命名套装特效");
            }
        } else if (currentLevel > 0) {
            // 淬炼套装特效
            currentEffect = createButton(
                    Material.FIREWORK,
                    "§d§l当前特效等级: " + currentLevel + "星",
                    "§7当前激活的淬炼套装特效");
        } else {
            // 没有激活的特效
            currentEffect = createMultiLineButton(
                    Material.FIREWORK,
                    "§7§l当前特效: 无",
                    "§7暂无激活的特效",
                    "§c需要穿戴6星以上套装或命名套装");
        }

        // 烟花物品始终显示在位置11
        gui.setItem(11, currentEffect);

        // 套装特效消息开关（仅在配置允许时显示）
        if (!SuitMessageManager.isForceDisabled()) {
            boolean messageEnabled = SuitMessageManager.isPlayerMessageEnabled(player.getName());
            ItemStack messageToggle = createButton(
                    messageEnabled ? Material.BOOK : Material.BOOK_AND_QUILL,
                    messageEnabled ? "§a§l消息提示已开启" : "§c§l消息提示已关闭",
                    "§7点击切换套装特效激活/失去消息");
            gui.setItem(15, messageToggle);
        }

        // 返回按钮
        gui.setItem(22, createButton(Material.ARROW, "§c§l返回", "§7返回装备管理界面"));

        // 添加玩家到特效设置界面跟踪列表
        InGameGUIListener.addPlayerToEffectSettings(player.getName());

        player.openInventory(gui);
    }

    /**
     * 打开淬炼信息界面
     *
     * @param player 玩家
     */
    public static void openEnhanceInfoGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 36, "§6§l淬炼信息界面");

        // 显示淬炼等级信息
        int zbLevel = Cuilian.checkPlayerZBCL(player);
        int wqLevel = Cuilian.checkPlayerWQCL(player);
        int tzLevel = Cuilian.getCLTZ(zbLevel, wqLevel);

        // 装备等级信息
        ItemStack zbInfo = createButton(Material.DIAMOND_CHESTPLATE,
                "§b§l装备等级: " + zbLevel + "星",
                "§7当前装备的最低淬炼等级");
        gui.setItem(11, zbInfo);

        // 武器等级信息
        ItemStack wqInfo = createButton(Material.DIAMOND_SWORD,
                "§c§l武器等级: " + wqLevel + "星",
                "§7当前武器的淬炼等级");
        gui.setItem(13, wqInfo);

        // 套装等级信息
        ItemStack tzInfo = createButton(Material.NETHER_STAR,
                "§6§l套装等级: " + tzLevel + "星",
                "§7套装激活等级（取装备和武器最低值）");
        gui.setItem(15, tzInfo);

        // 返回按钮
        gui.setItem(31, createButton(Material.ARROW, "§c§l返回", "§7返回装备管理界面"));

        player.openInventory(gui);
    }

    /**
     * 创建装备显示物品
     */
    private static ItemStack createEquipmentDisplay(ItemStack equipment, String type) {
        ItemStack display = equipment.clone();
        ItemMeta meta = display.getItemMeta();

        if (meta != null) {
            List<String> lore = meta.hasLore() ? new ArrayList<>(meta.getLore()) : new ArrayList<>();
            lore.add("");
            lore.add("§7§l装备类型: §e" + type);
            lore.add("§7§l淬炼等级: §a" + Cuilian.getCuilianlevel(equipment) + "星");
            meta.setLore(lore);
            display.setItemMeta(meta);
        }

        return display;
    }

    /**
     * 创建套装信息显示
     */
    private static ItemStack createSuitInfoDisplay(Player player) {
        ItemStack info = new ItemStack(Material.DIAMOND_CHESTPLATE);
        ItemMeta meta = info.getItemMeta();

        if (meta != null) {
            meta.setDisplayName("§6§l套装信息");
            List<String> lore = new ArrayList<>();

            // 首先检查命名套装
            String namedSuit = SuitManager.getPlayerSuit(player.getName());
            if (namedSuit != null) {
                // 命名套装信息
                lore.add("§a§l当前套装: " + namedSuit);
                lore.add("§7套装类型: §e命名套装");
                lore.add("");
                lore.add("§e§l套装效果:");

                // 检查特效状态
                Integer currentLevel = Cuilian.lizi.get(player.getName());
                if (currentLevel != null && currentLevel == -1) {
                    lore.add("§7- 特效状态: §a激活中");
                } else {
                    lore.add("§7- 特效状态: " + (SuitManager.isPlayerEffectEnabled(player.getName()) ? "§c未激活" : "§c已关闭"));
                }

                // 获取套装属性信息
                SuitManager.SuitAttribute attribute = SuitManager.getSuitAttribute(namedSuit);
                if (attribute != null) {
                    lore.add("");
                    lore.add("§b§l套装属性:");
                    if (attribute.attackDamage > 0) {
                        lore.add("§7- 攻击伤害: +" + attribute.attackDamage);
                    }
                    if (attribute.defense > 0) {
                        lore.add("§7- 防御力: +" + attribute.defense);
                    }
                    if (attribute.health > 0) {
                        lore.add("§7- 生命值: +" + attribute.health);
                    }
                    if (attribute.speed > 0) {
                        lore.add("§7- 移动速度: +" + (attribute.speed * 100) + "%");
                    }
                    if (attribute.jump > 0) {
                        lore.add("§7- 跳跃高度: +" + attribute.jump);
                    }
                    if (attribute.vampire > 0) {
                        lore.add("§7- 吸血: +" + attribute.vampire + "%");
                    }
                }
            } else {
                // 淬炼套装信息
                int zbLevel = Cuilian.checkPlayerZBCL(player);
                int wqLevel = Cuilian.checkPlayerWQCL(player);
                int suitLevel = Cuilian.getCLTZ(zbLevel, wqLevel);

                if (suitLevel >= 6) {
                    lore.add("§a§l当前套装等级: " + suitLevel + "星");
                    lore.add("§7套装类型: §e淬炼等级套装");
                    lore.add("§7套装名称: " + Cuilian.getCuilianStr(suitLevel));
                    lore.add("");
                    lore.add("§e§l套装效果:");

                    // 检查特效状态
                    Integer currentLevel = Cuilian.lizi.get(player.getName());
                    if (currentLevel != null && currentLevel > 0) {
                        lore.add("§7- 特效等级: " + currentLevel + "星");
                        lore.add("§7- 特效状态: §a激活中");
                    } else {
                        lore.add("§7- 特效等级: " + suitLevel + "星");
                        lore.add("§7- 特效状态: " + (SuitManager.isPlayerEffectEnabled(player.getName()) ? "§c未激活" : "§c已关闭"));
                    }

                    lore.add("");
                    lore.add("§b§l套装组成:");
                    lore.add("§7- 装备等级: " + zbLevel + "星");
                    lore.add("§7- 武器等级: " + wqLevel + "星");
                } else {
                    lore.add("§c§l未激活套装");
                    lore.add("§7需要穿戴完整的6星以上装备");
                    lore.add("§7才能激活套装效果");
                    lore.add("");
                    lore.add("§7§l当前状态:");
                    lore.add("§7- 装备等级: " + zbLevel + "星");
                    lore.add("§7- 武器等级: " + wqLevel + "星");
                    lore.add("§7- 套装等级: " + suitLevel + "星");
                }
            }

            meta.setLore(lore);
            info.setItemMeta(meta);
        }

        return info;
    }

    /**
     * 创建套装预览物品
     */
    private static ItemStack createSuitPreviewItem(String suitName) {
        ItemStack preview = new ItemStack(Material.LEATHER_CHESTPLATE);
        ItemMeta meta = preview.getItemMeta();

        if (meta != null) {
            meta.setDisplayName("§6§l" + suitName);
            List<String> lore = new ArrayList<>();

            // 从配置获取套装信息
            String basePath = "suit." + suitName;
            if (Cuilian.Suit.contains(basePath)) {
                int quality = Cuilian.Suit.getInt(basePath + ".quality", 1);
                int attack = Cuilian.Suit.getInt(basePath + ".attribute.attack_damage", 0);
                int defense = Cuilian.Suit.getInt(basePath + ".attribute.defense", 0);
                int health = Cuilian.Suit.getInt(basePath + ".attribute.health", 0);

                lore.add("§7§l品质等级: §e" + quality);
                lore.add("");
                lore.add("§a§l套装属性:");
                lore.add("§7- 攻击伤害: +" + attack);
                lore.add("§7- 防御力: +" + defense);
                lore.add("§7- 生命值: +" + health);
                lore.add("");

                // 添加特效信息
                addSuitEffectInfoToLore(lore, suitName);

                // 添加宝石等级要求
                addGemLevelRequirements(lore, basePath);

                // 添加预览信息
                int previewDuration = Cuilian.config.getInt("suit_preview.duration", 30);
                int previewCooldown = Cuilian.config.getInt("suit_preview.cooldown", 60);

                lore.add("§b§l⏱ 预览信息:");
                lore.add("§7  预览时间: §e" + previewDuration + "秒");
                lore.add("§7  预览冷却: §c" + previewCooldown + "秒");

                lore.add("");
                lore.add("§e§l左键点击: §7查看详细信息");
                lore.add("§a§l右键点击: §7预览套装特效");
            }

            meta.setLore(lore);
            preview.setItemMeta(meta);
        }

        return preview;
    }

    /**
     * 创建功能按钮
     */
    private static ItemStack createButton(Material material, String name, String description) {
        ItemStack button = new ItemStack(material);
        ItemMeta meta = button.getItemMeta();

        if (meta != null) {
            meta.setDisplayName(name);
            List<String> lore = new ArrayList<>();
            lore.add(description);
            meta.setLore(lore);
            button.setItemMeta(meta);
        }

        return button;
    }

    /**
     * 创建多行描述的功能按钮
     */
    private static ItemStack createMultiLineButton(Material material, String name, String... descriptions) {
        ItemStack button = new ItemStack(material);
        ItemMeta meta = button.getItemMeta();

        if (meta != null) {
            meta.setDisplayName(name);
            List<String> lore = new ArrayList<>();
            for (String description : descriptions) {
                lore.add(description);
            }
            meta.setLore(lore);
            button.setItemMeta(meta);
        }

        return button;
    }

    /**
     * 添加套装特效信息到lore
     *
     * @param lore     lore列表
     * @param suitName 套装名称
     */
    private static void addSuitEffectInfoToLore(List<String> lore, String suitName) {
        try {
            // 直接从配置文件获取特效信息，支持新旧格式
            String basePath = "suit." + suitName + ".effect";
            if (!Cuilian.Suit.contains(basePath)) {
                return;
            }

            lore.add("§6§l✨ 套装特效:");

            // 检查是否有新格式的effects数组
            if (Cuilian.Suit.contains(basePath + ".effects")) {
                List<?> effectsList = Cuilian.Suit.getList(basePath + ".effects");
                boolean enableStacking = Cuilian.Suit.getBoolean(basePath + ".enable_stacking", false);

                if (effectsList != null && !effectsList.isEmpty()) {
                    if (enableStacking && effectsList.size() > 1) {
                        lore.add("§a§l📋 叠加特效 §7(共 §e" + effectsList.size() + " §7种)");

                        for (int i = 0; i < effectsList.size(); i++) {
                            if (effectsList.get(i) instanceof Map) {
                                @SuppressWarnings("unchecked")
                                Map<String, Object> effectMap = (Map<String, Object>) effectsList.get(i);
                                String type = (String) effectMap.get("type");
                                boolean enabled = (Boolean) effectMap.getOrDefault("enabled", true);

                                String displayName = getEffectDisplayName(type);
                                String statusColor = enabled ? "§a" : "§c";
                                String statusText = enabled ? "启用" : "禁用";

                                lore.add(String.format("§7  %d. %s%s §8[%s%s§8]",
                                        i + 1, statusColor, displayName, statusColor, statusText));
                            }
                        }
                    } else {
                        // 单一特效（新格式）
                        if (effectsList.get(0) instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> effectMap = (Map<String, Object>) effectsList.get(0);
                            String type = (String) effectMap.get("type");
                            boolean enabled = (Boolean) effectMap.getOrDefault("enabled", true);

                            String displayName = getEffectDisplayName(type);
                            String statusColor = enabled ? "§a" : "§c";
                            String statusText = enabled ? "启用" : "禁用";

                            lore.add("§a§l🎆 单一特效:");
                            lore.add(String.format("§7  %s%s §8[%s%s§8]",
                                    statusColor, displayName, statusColor, statusText));
                        }
                    }
                }
            } else if (Cuilian.Suit.contains(basePath + ".type")) {
                // 旧格式的特效配置
                String type = Cuilian.Suit.getString(basePath + ".type");
                String color1 = Cuilian.Suit.getString(basePath + ".color1", "");
                String color2 = Cuilian.Suit.getString(basePath + ".color2", "");
                String color3 = Cuilian.Suit.getString(basePath + ".color3", "");

                String displayName = getEffectDisplayName(type);

                lore.add("§a§l🎆 单一特效:");
                lore.add(String.format("§7  §a%s §8[§a启用§8]", displayName));

                if (!color1.isEmpty() || !color2.isEmpty() || !color3.isEmpty()) {
                    lore.add(String.format("§7  颜色: §f%s§7, §f%s§7, §f%s",
                            color1.isEmpty() ? "无" : color1,
                            color2.isEmpty() ? "无" : color2,
                            color3.isEmpty() ? "无" : color3));
                }
            }

            lore.add("");
        } catch (Exception e) {
            System.err.println("添加套装特效信息到lore时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 添加宝石等级要求到lore
     *
     * @param lore     lore列表
     * @param basePath 配置路径
     */
    private static void addGemLevelRequirements(List<String> lore, String basePath) {
        try {
            String gemPath = basePath + ".gem_levels";
            if (Cuilian.Suit.contains(gemPath)) {
                lore.add("§d§l💎 宝石等级要求:");

                int headLevel = Cuilian.Suit.getInt(gemPath + ".head", 0);
                int chestLevel = Cuilian.Suit.getInt(gemPath + ".chest", 0);
                int legLevel = Cuilian.Suit.getInt(gemPath + ".leg", 0);
                int footLevel = Cuilian.Suit.getInt(gemPath + ".foot", 0);
                int swordLevel = Cuilian.Suit.getInt(gemPath + ".sword", 0);
                int bowLevel = Cuilian.Suit.getInt(gemPath + ".bow", 0);

                if (headLevel > 0)
                    lore.add("§7  头盔: §b+" + headLevel + " §7级");
                if (chestLevel > 0)
                    lore.add("§7  胸甲: §b+" + chestLevel + " §7级");
                if (legLevel > 0)
                    lore.add("§7  护腿: §b+" + legLevel + " §7级");
                if (footLevel > 0)
                    lore.add("§7  靴子: §b+" + footLevel + " §7级");
                if (swordLevel > 0)
                    lore.add("§7  剑: §b+" + swordLevel + " §7级");
                if (bowLevel > 0)
                    lore.add("§7  弓: §b+" + bowLevel + " §7级");

                lore.add("");
            }
        } catch (Exception e) {
            System.err.println("添加宝石等级要求到lore时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取特效显示名称
     *
     * @param effectType 特效类型
     * @return 显示名称
     */
    private static String getEffectDisplayName(String effectType) {
        return SuitManager.getEffectDisplayName(effectType);
    }
}
