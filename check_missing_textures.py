#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查代码中引用的纹理文件是否存在
"""

import os
import re
from pathlib import Path

def get_existing_textures():
    """获取实际存在的纹理文件"""
    texture_dir = Path('src/main/resources/textures')
    if not texture_dir.exists():
        return set()
    
    textures = set()
    for file in texture_dir.glob('*.png'):
        if file.name != 'README.md':
            # 移除扩展名
            textures.add(file.stem)
    
    return textures

def get_referenced_textures():
    """从代码中提取引用的纹理文件"""
    java_file = Path('src/main/java/cn/winde/cuilian/texture/TextureManager.java')
    if not java_file.exists():
        return set()
    
    with open(java_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找 addTextureMapping 调用中的纹理文件名
    pattern = r'addTextureMapping\([^,]+,\s*"([^"]+)"\)'
    matches = re.findall(pattern, content)
    
    referenced = set()
    for match in matches:
        referenced.add(match)
    
    return referenced

def main():
    print("检查纹理文件引用...")
    print("=" * 50)
    
    existing_textures = get_existing_textures()
    referenced_textures = get_referenced_textures()
    
    print(f"实际存在的纹理文件: {len(existing_textures)} 个")
    print(f"代码中引用的纹理文件: {len(referenced_textures)} 个")
    print()
    
    # 找出代码中引用但不存在的文件
    missing_textures = referenced_textures - existing_textures
    if missing_textures:
        print("❌ 代码中引用但文件不存在的纹理:")
        for texture in sorted(missing_textures):
            print(f"  - {texture}.png")
        print()
    
    # 找出存在但代码中未引用的文件
    unused_textures = existing_textures - referenced_textures
    if unused_textures:
        print("⚠️  存在但代码中未引用的纹理:")
        for texture in sorted(unused_textures):
            print(f"  - {texture}.png")
        print()
    
    # 找出匹配的文件
    matching_textures = existing_textures & referenced_textures
    if matching_textures:
        print("✅ 正确匹配的纹理文件:")
        for texture in sorted(matching_textures):
            print(f"  - {texture}.png")
        print()
    
    print("检查完成！")
    
    if missing_textures:
        print(f"\n需要修复 {len(missing_textures)} 个缺失的纹理引用。")
    
    if unused_textures:
        print(f"\n发现 {len(unused_textures)} 个未使用的纹理文件，可以考虑添加到代码中。")

if __name__ == "__main__":
    main()
